import { <PERSON>, Post, Body, HttpStatus, HttpException, Get, Param } from '@nestjs/common';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { WhatsAppService } from './whatsapp.service';

export class SendMessageDto {
  @IsString({ message: 'To field must be a string' })
  @IsNotEmpty({ message: 'To field is required' })
  to: string;

  @IsString({ message: 'Message must be a string' })
  @IsNotEmpty({ message: 'Message is required' })
  message: string;
}

export class SendInteractiveMessageDto {
  @IsString({ message: 'To field must be a string' })
  @IsNotEmpty({ message: 'To field is required' })
  to: string;

  @IsString({ message: 'Message must be a string' })
  @IsNotEmpty({ message: 'Message is required' })
  message: string;

  @IsNotEmpty({ message: 'Buttons are required' })
  buttons: Array<{ id: string; title: string }>;
}

export class SendListMessageDto {
  @IsString({ message: 'To field must be a string' })
  @IsNotEmpty({ message: 'To field is required' })
  to: string;

  @IsString({ message: 'Message must be a string' })
  @IsNotEmpty({ message: 'Message is required' })
  message: string;

  @IsNotEmpty({ message: 'Sections are required' })
  sections: Array<{ title: string; rows: Array<{ id: string; title: string; description?: string }> }>;
}

export class SendImageMessageDto {
  @IsString({ message: 'To field must be a string' })
  @IsNotEmpty({ message: 'To field is required' })
  to: string;

  @IsString({ message: 'Image URL must be a string' })
  @IsNotEmpty({ message: 'Image URL is required' })
  image: string;

  @IsOptional()
  @IsString({ message: 'Caption must be a string' })
  caption?: string;
}

@Controller('whatsapp')
export class WhatsAppController {
  constructor(private readonly whatsappService: WhatsAppService) {}

  @Post('send-text')
  async sendTextMessage(@Body() sendMessageDto: SendMessageDto) {
    try {
      const result = await this.whatsappService.sendTextMessage(
        sendMessageDto.to,
        sendMessageDto.message
      );

      if (result.success) {
        return { 
          success: true, 
          message: 'Message sent successfully',
          messageId: result.messageId 
        };
      } else {
        throw new HttpException('Failed to send message', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      throw new HttpException('Failed to send message', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('send-interactive')
  async sendInteractiveMessage(@Body() sendInteractiveDto: SendInteractiveMessageDto) {
    try {
      const result = await this.whatsappService.sendInteractiveMessage(
        sendInteractiveDto.to,
        sendInteractiveDto.message,
        sendInteractiveDto.buttons
      );

      if (result.success) {
        return { 
          success: true, 
          message: 'Interactive message sent successfully',
          messageId: result.messageId 
        };
      } else {
        throw new HttpException('Failed to send interactive message', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      throw new HttpException('Failed to send interactive message', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('send-list')
  async sendListMessage(@Body() sendListDto: SendListMessageDto) {
    try {
      const result = await this.whatsappService.sendListMessage(
        sendListDto.to,
        sendListDto.message,
        sendListDto.sections
      );

      if (result.success) {
        return { 
          success: true, 
          message: 'List message sent successfully',
          messageId: result.messageId 
        };
      } else {
        throw new HttpException('Failed to send list message', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      throw new HttpException('Failed to send list message', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('send-image')
  async sendImageMessage(@Body() sendImageDto: SendImageMessageDto) {
    try {
      const result = await this.whatsappService.sendImageMessage(
        sendImageDto.to,
        sendImageDto.image,
        sendImageDto.caption
      );

      if (result.success) {
        return { 
          success: true, 
          message: 'Image message sent successfully',
          messageId: result.messageId 
        };
      } else {
        throw new HttpException('Failed to send image message', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      throw new HttpException('Failed to send image message', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('message-status/:messageId')
  async getMessageStatus(@Param('messageId') messageId: string) {
    try {
      const status = await this.whatsappService.getMessageStatus(messageId);
      
      return {
        success: true,
        messageId: messageId,
        status: status
      };
    } catch (error) {
      throw new HttpException('Failed to get message status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
} 