# Template Module Documentation

## Overview

The Template Module provides comprehensive WhatsApp template management functionality, including creation, editing, Meta API integration, AI-powered generation, and voice-to-template conversion. This module follows the established patterns from the AuthModule for consistent architecture, error handling, and response formatting.

## Table of Contents

1. [Architecture](#architecture)
2. [API Endpoints](#api-endpoints)
3. [Data Models](#data-models)
4. [Business Logic](#business-logic)
5. [Security](#security)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Troubleshooting](#troubleshooting)

## Architecture

### Module Structure

```
src/template/
├── dto/                          # Data Transfer Objects
│   ├── create-template.dto.ts    # Template creation validation (Meta format)
│   ├── update-template.dto.ts    # Template update validation
│   ├── template-query.dto.ts     # Query parameters validation
│   ├── create-ai-template.dto.ts # AI template generation
│   └── index.ts                  # DTO exports
├── utils/                        # Utility classes
│   ├── template-constants.util.ts # Constants and messages
│   ├── template-validation.util.ts # Validation logic
│   └── template-response.util.ts # Response formatting
├── docs/                         # Documentation
│   ├── INDEX.md                  # This file
│   ├── README.md                 # Quick start guide
│   ├── API_ENDPOINTS.md          # Complete API reference
│   ├── META_TEMPLATE_API.md      # Meta API integration guide
│   ├── UNIFIED_API_GUIDE.md      # Unified API documentation
│   ├── FRONTEND_API_DOCUMENTATION.md # Comprehensive frontend docs
│   ├── QUICK_REFERENCE_GUIDE.md  # Developer quick reference
│   ├── Template_API_Postman_Collection.json # Postman collection
│   ├── CHANGELOG.md              # Version history and migration
│   ├── TESTING.md                # Testing guidelines
│   └── DEPLOYMENT.md             # Deployment guide
├── __tests__/                    # Test suites
│   ├── unit/                     # Unit tests
│   ├── fixtures/                 # Test fixtures
│   └── e2e/                      # End-to-end tests
├── template.controller.ts        # HTTP endpoints
├── template.service.ts           # Business logic
├── template.module.ts            # Module configuration
└── README.md                     # Quick start guide
```

### Key Components

1. **TemplateController**: Handles HTTP requests and responses
2. **TemplateService**: Contains business logic and data operations
3. **DTOs**: Validate and structure incoming data
4. **Utility Classes**: Provide reusable functionality
5. **Constants**: Centralized configuration and messages

### Dependencies

- **SupabaseService**: Database operations and user management
- **MetaApiService**: WhatsApp Business API integration
- **AiService**: AI-powered template generation
- **ConfigService**: Environment configuration

## API Endpoints

### Template CRUD Operations

#### Create Template (Meta Format - Recommended)
```http
POST /templates/meta
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "welcome_message",
  "description": "Welcome new users",
  "category": "UTILITY",
  "language": "en",
  "waba_id": "123456789",
  "components": [
    {
      "type": "BODY",
      "text": "Welcome {{1}}! We're excited to have you.",
      "example": {
        "body_text": [["John"]]
      }
    }
  ]
}
```

#### Create Template (Legacy Format)
```http
POST /templates
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "welcome_message",
  "description": "Welcome new users",
  "type": "text",
  "content": "Welcome {{1}}! We're excited to have you.",
  "language": "en",
  "category": "UTILITY",
  "waba_id": "123456789"
}
```

#### Create Draft Template
```http
POST /templates/draft
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "draft_template",
  "content": "This is a draft template",
  "language": "en",
  "waba_id": "123456789"
}
```

#### Get User Templates
```http
GET /templates?page=1&limit=10&search=welcome&category=UTILITY
Authorization: Bearer <token>
```

#### Get Workspace Templates
```http
GET /templates/workspace/123?page=1&limit=10
Authorization: Bearer <token>
```

#### Get Template by ID
```http
GET /templates/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer <token>
```

#### Update Template
```http
PUT /templates/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "updated_welcome_message",
  "content": "Updated welcome message {{1}}!"
}
```

#### Delete Template
```http
DELETE /templates/123456789/template/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer <token>
```

### Meta Integration

#### Sync All Templates with Meta
```http
GET /templates/meta/sync/waba/123456789
Authorization: Bearer <token>
```

#### Sync Single Template with Meta
```http
POST /templates/550e8400-e29b-41d4-a716-446655440000/sync
Authorization: Bearer <token>
```

#### Get Meta Templates
```http
GET /templates/meta/templates
Authorization: Bearer <token>
```

### AI Generation

#### Generate AI Template
```http
POST /templates/ai/generate
Content-Type: application/json
Authorization: Bearer <token>

{
  "prompt": "Create a welcome message for new customers",
  "waba_id": "123456789",
  "category": "MARKETING",
  "language": "en"
}
```

#### Generate Template from Voice
```http
POST /templates/voice/generate
Content-Type: multipart/form-data
Authorization: Bearer <token>

audioFile: <audio_file>
waba_id: "123456789"
```

## Data Models

### Template Entity

```typescript
interface Template {
  id: string;
  name: string;
  description?: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'location' | 'contact' | 'interactive';
  headerText?: string;
  footer?: string;
  content: string;
  buttons?: Button[];
  sections?: Section[];
  image_url?: string;
  image_caption?: string;
  language: string;
  variables?: Record<string, any>;
  meta_template_id?: string;
  meta_template_status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED' | 'DISABLED';
  meta_template_category: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION';
  workspace_id: string;
  created_by: string;
  is_active: boolean;
  ai_generated?: boolean;
  waba_id: string;
  created_at: string;
  updated_at: string;
}
```

### Button Entity

```typescript
interface Button {
  text: string;
  type: 'quick_reply' | 'url' | 'phone_number';
  url?: string;
  phone_number?: string;
}
```

### Section Entity

```typescript
interface Section {
  title: string;
  type: 'product_list' | 'product_catalog';
  products?: any[];
}
```

## Business Logic

### Template Creation Flow

1. **Validation**: Validate input data using DTOs
2. **Duplicate Check**: Ensure no duplicate templates exist
3. **Meta Integration**: Create template in Meta API if credentials available
4. **Database Storage**: Save template to local database
5. **Response**: Return standardized success/error response

### Template Update Flow

1. **Existence Check**: Verify template exists and belongs to user
2. **Validation**: Validate update data
3. **Meta Update**: Update template in Meta API if applicable
4. **Database Update**: Update local database record
5. **Response**: Return updated template data

### AI Template Generation

1. **Prompt Processing**: Send prompt to AI service
2. **Template Generation**: AI generates template structure
3. **Validation**: Validate generated template
4. **Draft Creation**: Save as draft template (not submitted to Meta)
5. **Response**: Return generated template

### Voice Template Generation

1. **Audio Processing**: Transcribe audio to text
2. **AI Generation**: Use transcribed text as prompt for AI
3. **Template Creation**: Create draft template from AI output
4. **Response**: Return generated template

## Security

### Authentication
- All endpoints require valid JWT token
- User context validated on every request
- Workspace access control enforced

### Authorization
- Users can only access their own templates
- Workspace members can access workspace templates
- Meta API credentials scoped to workspace

### Input Validation
- Comprehensive DTO validation
- File upload restrictions (audio files only, 10MB limit)
- SQL injection prevention through parameterized queries

### Data Protection
- Sensitive data (Meta tokens) not logged
- User data scoped to workspace
- Audit trail for template operations

## Testing

### Unit Tests
- Service method testing
- Validation logic testing
- Utility function testing
- Mock external dependencies

### Integration Tests
- Database operations
- Meta API integration
- AI service integration
- File upload handling

### End-to-End Tests
- Complete template creation flow
- Template update and deletion
- Meta synchronization
- AI generation workflows

## Deployment

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...

# Meta API
META_APP_ID=...
META_APP_SECRET=...

# AI Service
OPENAI_API_KEY=...
```

### Docker Configuration
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "start:prod"]
```

### Health Checks
- Database connectivity
- Meta API availability
- AI service status
- File system access

## Troubleshooting

### Common Issues

#### Template Creation Fails
- Check Meta API credentials
- Verify WABA ID format
- Ensure template name is unique
- Validate template content format

#### Meta Sync Issues
- Verify Meta API access token
- Check template approval status
- Ensure WABA ID matches credentials
- Review Meta API rate limits

#### AI Generation Errors
- Check AI service configuration
- Verify prompt format
- Ensure audio file format is supported
- Review AI service quotas

#### File Upload Problems
- Check file size limits (10MB)
- Verify audio file format
- Ensure proper multipart form data
- Review server storage space

### Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 400 | Bad Request | Check request format and validation |
| 401 | Unauthorized | Verify authentication token |
| 404 | Not Found | Check template ID and permissions |
| 409 | Conflict | Template name already exists |
| 500 | Internal Error | Check server logs and configuration |

### Logging

The module provides comprehensive logging:
- Request/response logging
- Error tracking
- Performance metrics
- Audit trails

Log levels:
- `ERROR`: Critical errors requiring attention
- `WARN`: Warning conditions
- `INFO`: General information
- `DEBUG`: Detailed debugging information

### Monitoring

Key metrics to monitor:
- Template creation success rate
- Meta API response times
- AI generation success rate
- File upload success rate
- Database query performance

## Documentation Overview

### For Frontend Developers
- **[Frontend API Documentation](./FRONTEND_API_DOCUMENTATION.md)** - Comprehensive API guide with examples, TypeScript interfaces, and React helpers
- **[Quick Reference Guide](./QUICK_REFERENCE_GUIDE.md)** - Quick start guide with common templates and helper functions
- **[Postman Collection](./Template_API_Postman_Collection.json)** - Ready-to-use Postman collection for API testing

### For Backend Developers
- **[API Endpoints](./API_ENDPOINTS.md)** - Complete API reference with request/response examples
- **[Unified API Guide](./UNIFIED_API_GUIDE.md)** - Meta format API documentation
- **[Meta Template API](./META_TEMPLATE_API.md)** - Meta API integration details

### For DevOps & QA
- **[Changelog](./CHANGELOG.md)** - Version history, breaking changes, and migration guide
- **[Testing](./TESTING.md)** - Testing guidelines and best practices
- **[Deployment](./DEPLOYMENT.md)** - Deployment instructions and configuration

### Key Features in v2.0.0
- ✅ **Unified API**: Single endpoint for all template operations
- ✅ **Meta Format**: Direct Meta API format support
- ✅ **AI Generation**: Enhanced AI template generation
- ✅ **Voice Support**: Voice-to-template conversion
- ✅ **Comprehensive Docs**: Complete documentation suite

### Support

For technical support:
1. Check application logs
2. Review error messages
3. Verify configuration
4. Test with minimal data
5. Contact development team with detailed error information

### Quick Links
- [Create Template Example](#create-template)
- [AI Generation Guide](#ai-generation)
- [Component Examples](#component-examples)
- [Error Handling](#error-handling)
- [Migration Guide](./CHANGELOG.md#migration-guide)
