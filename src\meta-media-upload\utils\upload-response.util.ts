/**
 * Utility class for formatting upload API responses
 */
export class UploadResponseUtil {
  /**
   * Creates a standardized success response
   */
  static createSuccessResponse(data: any, message: string, statusCode: number = 200): any {
    return {
      success: true,
      message,
      data,
      status_code: statusCode,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Creates a standardized error response
   */
  static createErrorResponse(message: string, statusCode: number = 400, error?: any): any {
    return {
      success: false,
      message,
      error: error || null,
      status_code: statusCode,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Formats upload session response
   */
  static formatUploadSessionResponse(uploadSessionId: string): any {
    return {
      id: uploadSessionId,
      success: true,
      message: 'Upload session created successfully',
    };
  }

  /**
   * Formats file handle response
   */
  static formatFileHandleResponse(handleId: string, uploadSessionId: string): any {
    return {
      handleId,
      success: true,
      message: 'File uploaded successfully',
      uploadSessionId,
    };
  }

  /**
   * Formats resume upload response
   */
  static formatResumeUploadResponse(uploadSessionId: string, fileOffset: number): any {
    return {
      id: uploadSessionId,
      fileOffset,
      success: true,
      message: 'Upload session status retrieved successfully',
    };
  }
}
