import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserDocument = User & Document;

@Schema({ timestamps: true })
export class User {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  first_name: string;

  @Prop({ required: true })
  last_name: string;

  @Prop({ default: false })
  isActive: boolean;

  @Prop({ default: 'user' })
  role: string;

  @Prop({ required: true, default: '0000000000' })
  phoneNumber: string;

  @Prop()
  profilePicture?: string;

  @Prop({ required: true, default: '1' })
  countrycode: string;

  @Prop({ required: true, default: 'Unknown' })
  country: string;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Add compound index for phone and countrycode to avoid duplicate null values
UserSchema.index({ phoneNumber: 1, countrycode: 1 }, { 
  unique: true,
  sparse: true, // This allows multiple documents with null values
  partialFilterExpression: { 
    phoneNumber: { $ne: null },
    countrycode: { $ne: null }
  }
}); 