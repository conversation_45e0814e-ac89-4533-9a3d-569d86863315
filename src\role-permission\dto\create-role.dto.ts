import { Is<PERSON><PERSON>, <PERSON>Not<PERSON>mpty, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

/**
 * DTO for creating a role
 */
export class CreateRoleDto {
  @IsString({ message: 'Role name must be a string' })
  @IsNotEmpty({ message: 'Role name is required' })
  @MinLength(1, { message: 'Role name must be at least 1 character long' })
  @MaxLength(100, { message: 'Role name must not exceed 100 characters' })
  name: string;

  @IsOptional()
  @IsString({ message: 'Role description must be a string' })
  @MaxLength(500, { message: 'Role description must not exceed 500 characters' })
  description?: string;

  @IsOptional()
  @IsEnum(['active', 'inactive'], { message: 'Role status must be either "active" or "inactive"' })
  status?: 'active' | 'inactive';
}
