import { Test, TestingModule } from '@nestjs/testing';
import { TemplateService } from './template.service';
import { SupabaseService } from '../supabase/supabase.service';
import { MetaApiService, MetaTemplateRequest, MetaTemplateResponse } from '../meta-api/meta-api.service';
import { ConfigService } from '@nestjs/config';
import { AiService } from '../ai/ai.service';
import { UnauthorizedException, BadRequestException, NotFoundException } from '@nestjs/common';
import { UpdateTemplateDto } from '../dto/template.dto';
import { Response } from 'express';

describe('TemplateService - updateTemplate', () => {
    let service: TemplateService;
    let supabaseService: jest.Mocked<SupabaseService>;
    let metaApiService: jest.Mocked<MetaApiService>;
    let configService: jest.Mocked<ConfigService>;
    let aiService: jest.Mocked<AiService>;

    const mockUser = {
        id: 'user-123',
        email: '<EMAIL>'
    };

    const mockExistingTemplate = {
        id: 'template-123',
        name: 'test_template',
        content: 'Hello {{1}}',
        type: 'text',
        category: 'UTILITY',
        language: 'en',
        waba_id: 'waba-123',
        created_by: 'user-123',
        meta_template_id: null,
        meta_template_status: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    };

    const mockExistingTemplateWithMeta = {
        ...mockExistingTemplate,
        meta_template_id: 'meta-template-456',
        meta_template_status: 'APPROVED'
    };

    const mockUserProfile = {
        data: {
            id: 'user-123',
            workspace_id: 'workspace-123'
        },
        error: null
    };

    const mockMetaCredentials = {
        access_token: 'mock-access-token',
        whatsapp_business_id: 'waba-123',
        status: 'Active',
        created_by: 'user-123'
    };

    const mockUpdateTemplateDto: UpdateTemplateDto = {
        name: 'updated_test_template',
        content: 'Hello {{1}}, welcome!',
        description: 'Updated description'
    };

    const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis()
    } as unknown as Response;

    const mockRequest = {
        user: mockUser
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TemplateService,
                {
                    provide: SupabaseService,
                    useValue: {
                        getClient: jest.fn(),
                        getUserProfile: jest.fn()
                    }
                },
                {
                    provide: MetaApiService,
                    useValue: {
                        updateTemplate: jest.fn(),
                        createTemplate: jest.fn(),
                        convertToMetaFormat: jest.fn()
                    }
                },
                {
                    provide: ConfigService,
                    useValue: {
                        get: jest.fn()
                    }
                },
                {
                    provide: AiService,
                    useValue: {
                        generateTemplate: jest.fn()
                    }
                }
            ]
        }).compile();

        service = module.get<TemplateService>(TemplateService);
        supabaseService = module.get(SupabaseService);
        metaApiService = module.get(MetaApiService);
        configService = module.get(ConfigService);
        aiService = module.get(AiService);

        // Reset all mocks
        jest.clearAllMocks();
    });

    // Helper function to create mock Supabase client
    const createMockSupabaseClient = (responses: any[]) => {
        let callIndex = 0;
        return {
            from: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            update: jest.fn().mockReturnThis(),
            single: jest.fn(() => {
                const response = responses[callIndex] || { data: null, error: null };
                callIndex++;
                return Promise.resolve(response);
            })
        };
    };

    describe('Authentication and Authorization Tests', () => {
        it('should throw UnauthorizedException when user is not provided', async () => {
            const requestWithoutUser = { user: null };

            await expect(
                service.updateTemplate('template-123', mockUpdateTemplateDto, requestWithoutUser, mockResponse)
            ).rejects.toThrow(UnauthorizedException);
        });

        it('should throw UnauthorizedException when user id is missing', async () => {
            const requestWithInvalidUser = { user: { email: '<EMAIL>' } };

            await expect(
                service.updateTemplate('template-123', mockUpdateTemplateDto, requestWithInvalidUser, mockResponse)
            ).rejects.toThrow(UnauthorizedException);
        });
    });

    describe('Template Existence and Ownership Tests', () => {
        it('should throw NotFoundException when template does not exist', async () => {
            const mockClient = createMockSupabaseClient([
                { data: null, error: { code: 'PGRST116', message: 'No rows found' } }
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);

            await expect(
                service.updateTemplate('non-existent-template', mockUpdateTemplateDto, mockRequest, mockResponse)
            ).rejects.toThrow(NotFoundException);
        });

        it('should throw BadRequestException for other database errors', async () => {
            const mockClient = createMockSupabaseClient([
                { data: null, error: { code: 'OTHER_ERROR', message: 'Database error' } }
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);

            await expect(
                service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse)
            ).rejects.toThrow(BadRequestException);
        });

        it('should throw error when template belongs to different user', async () => {
            const mockClient = createMockSupabaseClient([
                { data: null, error: { code: 'PGRST116', message: 'No rows found' } }
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);

            await expect(
                service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse)
            ).rejects.toThrow(NotFoundException);
        });
    });

    describe('Template Update - Local Only (No Meta Integration)', () => {
        it('should update template locally when no Meta credentials found', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }, // Template fetch
                { data: null, error: { code: 'PGRST116', message: 'No rows found' } }, // No Meta credentials
                { data: { ...mockExistingTemplate, ...mockUpdateTemplateDto }, error: null } // Successful update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            await service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse);

            expect(mockClient.update).toHaveBeenCalledWith(
                expect.objectContaining({
                    ...mockUpdateTemplateDto,
                    updated_at: expect.any(String)
                })
            );
            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    status: 'success',
                    message: 'Template updated successfully in local DB only'
                })
            );
        });

        it('should update template locally when user profile not found', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue({
                data: null,
                error: { message: 'User not found' }
            });

            await expect(
                service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse)
            ).rejects.toThrow(BadRequestException);
        });
    });

    describe('Template Update - With Existing Meta Template', () => {
        it('should update template in both Meta and local DB successfully', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplateWithMeta, error: null }, // Template fetch
                { data: mockMetaCredentials, error: null }, // Meta credentials
                { data: { ...mockExistingTemplateWithMeta, ...mockUpdateTemplateDto }, error: null } // Local update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            const mockMetaRequest: MetaTemplateRequest = {
                name: 'updated_test_template',
                category: 'UTILITY',
                components: [],
                language: 'en'
            };
            metaApiService.convertToMetaFormat.mockReturnValue(mockMetaRequest);

            const mockMetaResponse: MetaTemplateResponse = {
                id: 'meta-template-456',
                status: 'APPROVED',
                category: 'UTILITY',
                components: [],
                language: 'en',
                name: 'updated_test_template'
            };
            metaApiService.updateTemplate.mockResolvedValue(mockMetaResponse);

            await service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse);

            expect(metaApiService.updateTemplate).toHaveBeenCalledWith(
                'meta-template-456',
                'mock-access-token',
                mockMetaRequest
            );
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: 'Template updated successfully in both local DB and Meta'
                })
            );
        });

        it('should continue with local update when Meta update fails', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplateWithMeta, error: null }, // Template fetch
                { data: mockMetaCredentials, error: null }, // Meta credentials
                { data: { ...mockExistingTemplateWithMeta, ...mockUpdateTemplateDto }, error: null } // Local update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            const mockMetaRequest: MetaTemplateRequest = {
                name: 'updated_test_template',
                category: 'UTILITY',
                components: [],
                language: 'en'
            };
            metaApiService.convertToMetaFormat.mockReturnValue(mockMetaRequest);
            metaApiService.updateTemplate.mockRejectedValue(new Error('Meta API error'));

            await service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse);

            expect(metaApiService.updateTemplate).toHaveBeenCalled();
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: 'Template updated successfully in local DB only'
                })
            );
        });
    });

    describe('Template Update - Create New Meta Template', () => {
        it('should create new Meta template and update local DB', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }, // Template fetch
                { data: mockMetaCredentials, error: null }, // Meta credentials
                { data: { 
                    ...mockExistingTemplate, 
                    ...mockUpdateTemplateDto,
                    meta_template_id: 'new-meta-template-789',
                    meta_template_status: 'PENDING'
                }, error: null } // Local update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            const mockMetaRequest: MetaTemplateRequest = {
                name: 'updated_test_template',
                category: 'UTILITY',
                components: [],
                language: 'en'
            };
            metaApiService.convertToMetaFormat.mockReturnValue(mockMetaRequest);

            const mockMetaResponse: MetaTemplateResponse = {
                id: 'new-meta-template-789',
                status: 'PENDING',
                category: 'UTILITY',
                components: [],
                language: 'en',
                name: 'updated_test_template'
            };
            metaApiService.createTemplate.mockResolvedValue(mockMetaResponse);

            await service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse);

            expect(metaApiService.createTemplate).toHaveBeenCalledWith(
                'waba-123',
                'mock-access-token',
                mockMetaRequest
            );

            expect(mockClient.update).toHaveBeenCalledWith(
                expect.objectContaining({
                    ...mockUpdateTemplateDto,
                    meta_template_id: 'new-meta-template-789',
                    meta_template_status: 'PENDING',
                    meta_template_category: 'UTILITY',
                    updated_at: expect.any(String)
                })
            );
        });

        it('should handle Meta template creation failure gracefully', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }, // Template fetch
                { data: mockMetaCredentials, error: null }, // Meta credentials
                { data: { ...mockExistingTemplate, ...mockUpdateTemplateDto }, error: null } // Local update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            const mockMetaRequest: MetaTemplateRequest = {
                name: 'updated_test_template',
                category: 'UTILITY',
                components: [],
                language: 'en'
            };
            metaApiService.convertToMetaFormat.mockReturnValue(mockMetaRequest);
            metaApiService.createTemplate.mockRejectedValue(new Error('Meta creation failed'));

            await service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse);

            expect(metaApiService.createTemplate).toHaveBeenCalled();
            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: 'Template updated successfully in local DB only'
                })
            );
        });
    });

    describe('Database Update Failures', () => {
        it('should throw BadRequestException when local DB update fails', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }, // Template fetch
                { data: null, error: { message: 'No credentials' } }, // No Meta credentials
                { data: null, error: { message: 'Update failed' } } // Failed update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            await expect(
                service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse)
            ).rejects.toThrow(BadRequestException);
        });
    });

    describe('Edge Cases and Integration Scenarios', () => {
        it('should handle empty update DTO', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }, // Template fetch
                { data: null, error: { message: 'No credentials' } }, // No Meta credentials
                { data: mockExistingTemplate, error: null } // Successful update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            await service.updateTemplate('template-123', {}, mockRequest, mockResponse);

            expect(mockClient.update).toHaveBeenCalledWith(
                expect.objectContaining({
                    updated_at: expect.any(String)
                })
            );
        });

        it('should handle partial template updates', async () => {
            const partialUpdate: UpdateTemplateDto = {
                name: 'new_name_only'
            };

            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }, // Template fetch
                { data: null, error: { message: 'No credentials' } }, // No Meta credentials
                { data: { ...mockExistingTemplate, name: 'new_name_only' }, error: null } // Successful update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            await service.updateTemplate('template-123', partialUpdate, mockRequest, mockResponse);

            expect(mockClient.update).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: 'new_name_only',
                    updated_at: expect.any(String)
                })
            );
        });

        it('should handle Meta credentials with different WABA ID', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }, // Template fetch
                { data: null, error: { code: 'PGRST116', message: 'No matching credentials' } }, // No matching credentials
                { data: mockExistingTemplate, error: null } // Successful update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            await service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse);

            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    message: 'Template updated successfully in local DB only'
                })
            );
        });
    });

    describe('Response Format Validation', () => {
        it('should return correct response format for successful update', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplate, error: null }, // Template fetch
                { data: null, error: { message: 'No credentials' } }, // No Meta credentials
                { data: { ...mockExistingTemplate, ...mockUpdateTemplateDto }, error: null } // Successful update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            await service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse);

            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(mockResponse.json).toHaveBeenCalledWith({
                status: 'success',
                code: 200,
                message: 'Template updated successfully in local DB only',
                data: expect.objectContaining({
                    ...mockExistingTemplate,
                    ...mockUpdateTemplateDto,
                    meta_update_success: false
                }),
                timestamp: expect.any(String)
            });
        });

        it('should include meta_update_success flag in response', async () => {
            const mockClient = createMockSupabaseClient([
                { data: mockExistingTemplateWithMeta, error: null }, // Template fetch
                { data: mockMetaCredentials, error: null }, // Meta credentials
                { data: { ...mockExistingTemplateWithMeta, ...mockUpdateTemplateDto }, error: null } // Local update
            ]);
            supabaseService.getClient.mockReturnValue(mockClient as any);
            supabaseService.getUserProfile.mockResolvedValue(mockUserProfile);

            const mockMetaRequest: MetaTemplateRequest = {
                name: 'updated_test_template',
                category: 'UTILITY',
                components: [],
                language: 'en'
            };
            metaApiService.convertToMetaFormat.mockReturnValue(mockMetaRequest);

            const mockMetaResponse: MetaTemplateResponse = {
                id: 'meta-template-456',
                status: 'APPROVED',
                category: 'UTILITY',
                components: [],
                language: 'en',
                name: 'updated_test_template'
            };
            metaApiService.updateTemplate.mockResolvedValue(mockMetaResponse);

            await service.updateTemplate('template-123', mockUpdateTemplateDto, mockRequest, mockResponse);

            expect(mockResponse.json).toHaveBeenCalledWith(
                expect.objectContaining({
                    data: expect.objectContaining({
                        meta_update_success: true
                    })
                })
            );
        });
    });
});
