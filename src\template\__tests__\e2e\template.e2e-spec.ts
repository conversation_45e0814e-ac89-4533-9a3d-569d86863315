import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { getModelToken } from '@nestjs/mongoose';
import * as request from 'supertest';
import { TemplateModule } from '../../template.module';
import { templateFixtures } from '../fixtures/template.fixtures';

describe('Template E2E Tests', () => {
  let app: INestApplication;
  let mockTemplateModel: any;

  beforeAll(async () => {
    const mockModel = {
      create: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      findByIdAndUpdate: jest.fn(),
      findByIdAndDelete: jest.fn(),
      countDocuments: jest.fn(),
      aggregate: jest.fn(),
    };

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TemplateModule],
    })
      .overrideProvider(getModelToken('Template'))
      .useValue(mockModel)
      .compile();

    app = moduleFixture.createNestApplication();
    mockTemplateModel = moduleFixture.get(getModelToken('Template'));
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /templates', () => {
    it('should create template successfully', async () => {
      const createTemplateDto = templateFixtures.validTemplate;
      const mockTemplate = templateFixtures.mockTemplate;

      mockTemplateModel.findOne.mockResolvedValue(null);
      mockTemplateModel.create.mockResolvedValue(mockTemplate);

      const response = await request(app.getHttpServer())
        .post('/templates')
        .set('Authorization', 'Bearer mock-jwt-token')
        .send(createTemplateDto)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toBeDefined();
    });

    it('should return 400 for invalid data', async () => {
      const invalidTemplate = templateFixtures.invalidTemplate;

      const response = await request(app.getHttpServer())
        .post('/templates')
        .set('Authorization', 'Bearer mock-jwt-token')
        .send(invalidTemplate)
        .expect(400);

      expect(response.body.status).toBe('error');
    });

    it('should return 401 without authentication', async () => {
      const createTemplateDto = templateFixtures.validTemplate;

      await request(app.getHttpServer())
        .post('/templates')
        .send(createTemplateDto)
        .expect(401);
    });
  });

  describe('GET /templates', () => {
    it('should retrieve user templates', async () => {
      const mockTemplates = [templateFixtures.mockTemplate];

      mockTemplateModel.find.mockReturnValue({
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockTemplates),
      });
      mockTemplateModel.countDocuments.mockResolvedValue(1);

      const response = await request(app.getHttpServer())
        .get('/templates')
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.templates).toBeDefined();
    });

    it('should handle pagination', async () => {
      const mockTemplates = [templateFixtures.mockTemplate];

      mockTemplateModel.find.mockReturnValue({
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockTemplates),
      });
      mockTemplateModel.countDocuments.mockResolvedValue(1);

      const response = await request(app.getHttpServer())
        .get('/templates?page=1&limit=10')
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.pagination).toBeDefined();
    });
  });

  describe('GET /templates/:id', () => {
    it('should retrieve template by ID', async () => {
      const templateId = 'template-123';
      const mockTemplate = templateFixtures.mockTemplate;

      mockTemplateModel.findOne.mockResolvedValue(mockTemplate);

      const response = await request(app.getHttpServer())
        .get(`/templates/${templateId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.template).toBeDefined();
    });

    it('should return 404 for non-existent template', async () => {
      const templateId = 'non-existent';

      mockTemplateModel.findOne.mockResolvedValue(null);

      const response = await request(app.getHttpServer())
        .get(`/templates/${templateId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(404);

      expect(response.body.status).toBe('error');
    });
  });

  describe('PUT /templates/:id', () => {
    it('should update template successfully', async () => {
      const templateId = 'template-123';
      const updateDto = templateFixtures.validUpdate;
      const mockTemplate = templateFixtures.mockTemplate;
      const updatedTemplate = { ...mockTemplate, ...updateDto };

      mockTemplateModel.findOne.mockResolvedValue(mockTemplate);
      mockTemplateModel.findByIdAndUpdate.mockResolvedValue(updatedTemplate);

      const response = await request(app.getHttpServer())
        .put(`/templates/${templateId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .send(updateDto)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.template).toBeDefined();
    });

    it('should return 404 for non-existent template', async () => {
      const templateId = 'non-existent';
      const updateDto = templateFixtures.validUpdate;

      mockTemplateModel.findOne.mockResolvedValue(null);

      const response = await request(app.getHttpServer())
        .put(`/templates/${templateId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .send(updateDto)
        .expect(404);

      expect(response.body.status).toBe('error');
    });
  });

  describe('DELETE /templates/:wabaId/template/:templateId', () => {
    it('should delete template successfully', async () => {
      const wabaId = 'waba-123';
      const templateId = 'template-123';
      const mockTemplate = templateFixtures.mockTemplate;

      mockTemplateModel.findOne.mockResolvedValue(mockTemplate);
      mockTemplateModel.findByIdAndDelete.mockResolvedValue(mockTemplate);

      const response = await request(app.getHttpServer())
        .delete(`/templates/${wabaId}/template/${templateId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(200);

      expect(response.body.status).toBe('success');
    });

    it('should return 404 for non-existent template', async () => {
      const wabaId = 'waba-123';
      const templateId = 'non-existent';

      mockTemplateModel.findOne.mockResolvedValue(null);

      const response = await request(app.getHttpServer())
        .delete(`/templates/${wabaId}/template/${templateId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(404);

      expect(response.body.status).toBe('error');
    });
  });

  describe('POST /templates/ai/generate', () => {
    it('should generate AI template successfully', async () => {
      const aiTemplateDto = templateFixtures.validAiTemplate;
      const mockTemplate = templateFixtures.mockTemplate;

      mockTemplateModel.create.mockResolvedValue(mockTemplate);

      const response = await request(app.getHttpServer())
        .post('/templates/ai/generate')
        .set('Authorization', 'Bearer mock-jwt-token')
        .send(aiTemplateDto)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data.template).toBeDefined();
    });

    it('should return 400 for invalid AI template data', async () => {
      const invalidAiTemplate = {
        prompt: '',
        waba_id: '',
      };

      const response = await request(app.getHttpServer())
        .post('/templates/ai/generate')
        .set('Authorization', 'Bearer mock-jwt-token')
        .send(invalidAiTemplate)
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });

  describe('POST /templates/voice/generate', () => {
    it('should generate template from voice successfully', async () => {
      const mockTemplate = templateFixtures.mockTemplate;

      mockTemplateModel.create.mockResolvedValue(mockTemplate);

      const response = await request(app.getHttpServer())
        .post('/templates/voice/generate')
        .set('Authorization', 'Bearer mock-jwt-token')
        .attach('audioFile', Buffer.from('mock audio data'), 'test.mp3')
        .field('waba_id', '123456789')
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data.template).toBeDefined();
    });

    it('should return 400 for missing audio file', async () => {
      const response = await request(app.getHttpServer())
        .post('/templates/voice/generate')
        .set('Authorization', 'Bearer mock-jwt-token')
        .field('waba_id', '123456789')
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });

  describe('GET /templates/meta/sync/waba/:wabaId', () => {
    it('should sync templates with Meta successfully', async () => {
      const wabaId = 'waba-123';
      const mockMetaTemplates = [templateFixtures.mockMetaTemplate];

      mockTemplateModel.findOne.mockResolvedValue(null);
      mockTemplateModel.create.mockResolvedValue(templateFixtures.mockTemplate);

      const response = await request(app.getHttpServer())
        .get(`/templates/meta/sync/waba/${wabaId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.synced).toBeDefined();
    });

    it('should return 400 for invalid WABA ID', async () => {
      const wabaId = '';

      const response = await request(app.getHttpServer())
        .get(`/templates/meta/sync/waba/${wabaId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });

  describe('POST /templates/:id/sync', () => {
    it('should sync single template with Meta successfully', async () => {
      const templateId = 'template-123';
      const mockTemplate = templateFixtures.mockTemplate;

      mockTemplateModel.findOne.mockResolvedValue(mockTemplate);
      mockTemplateModel.findByIdAndUpdate.mockResolvedValue(mockTemplate);

      const response = await request(app.getHttpServer())
        .post(`/templates/${templateId}/sync`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.local_template).toBeDefined();
    });

    it('should return 404 for non-existent template', async () => {
      const templateId = 'non-existent';

      mockTemplateModel.findOne.mockResolvedValue(null);

      const response = await request(app.getHttpServer())
        .post(`/templates/${templateId}/sync`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(404);

      expect(response.body.status).toBe('error');
    });
  });

  describe('GET /templates/meta/templates', () => {
    it('should retrieve Meta templates successfully', async () => {
      const mockMetaTemplates = [templateFixtures.mockMetaTemplate];

      const response = await request(app.getHttpServer())
        .get('/templates/meta/templates')
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.templates).toBeDefined();
    });

    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer())
        .get('/templates/meta/templates')
        .expect(401);
    });
  });

  describe('GET /templates/workspace/:workspaceId', () => {
    it('should retrieve workspace templates successfully', async () => {
      const workspaceId = 'workspace-123';
      const mockTemplates = [templateFixtures.mockTemplate];

      mockTemplateModel.find.mockReturnValue({
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockTemplates),
      });
      mockTemplateModel.countDocuments.mockResolvedValue(1);

      const response = await request(app.getHttpServer())
        .get(`/templates/workspace/${workspaceId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.templates).toBeDefined();
    });

    it('should return 400 for invalid workspace ID', async () => {
      const workspaceId = '';

      const response = await request(app.getHttpServer())
        .get(`/templates/workspace/${workspaceId}`)
        .set('Authorization', 'Bearer mock-jwt-token')
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });
});
