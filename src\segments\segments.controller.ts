import { 
  Body, 
  Controller, 
  Get, 
  Param, 
  Post, 
  Req, 
  UseGuards, 
  HttpCode,
  HttpStatus,
  Query
} from '@nestjs/common';
import { SegmentsService } from './segments.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { CreateSegmentDto, SegmentQueryDto } from './dto';

/**
 * Refactored SegmentsController with improved structure and consistent response handling
 */
@Controller('segments')
export class SegmentsController {
  constructor(private readonly segmentsService: SegmentsService) {}

  /**
   * Create segment endpoint
   */
  @Post()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async createSegment(@Body() createDto: CreateSegmentDto, @Req() req: any) {
    return await this.segmentsService.createSegment(createDto, req);
  }

  /**
   * Get all segments for workspace endpoint
   */
  @Get()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getSegments(@Req() req: any) {
    return await this.segmentsService.getSegmentsForWorkspace(req);
  }

  /**
   * Get contacts by segment endpoint
   */
  @Get(':id/contacts')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getContactsBySegment(
    @Param('id') id: string,
    @Query() query: SegmentQueryDto,
    @Req() req: any
  ) {
    return await this.segmentsService.getContactsBySegment(id, query, req);
  }

  /**
   * Get segment operators endpoint
   */
  @Get('operators/list')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getSegmentOperators() {
    return await this.segmentsService.getSegmentOperators();
  }
}


