import { Controller, Get, Post, Query, Body, HttpStatus, HttpException } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';

@Controller('webhooks')
export class WebhooksController {
  constructor(private readonly webhooksService: WebhooksService) {}

  @Get()
  async verifyWebhook(
    @Query('hub.mode') mode: string,
    @Query('hub.verify_token') verifyToken: string,
    @Query('hub.challenge') challenge: string,
  ) {
    try {
      const isValid = await this.webhooksService.verifyWebhook(mode, verifyToken);
      
      if (isValid) {
        return challenge;
      } else {
        throw new HttpException('Verification failed', HttpStatus.BAD_REQUEST);
      }
    } catch (error) {
      throw new HttpException('Verification failed', HttpStatus.BAD_REQUEST);
    }
  }

  @Post()
  async handleWebhook(@Body() body: any) {
    try {
      await this.webhooksService.processWebhook(body);
      return { status: 'ok' };
    } catch (error) {
      console.error('Webhook processing error:', error);
      throw new HttpException('Webhook processing failed', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
} 