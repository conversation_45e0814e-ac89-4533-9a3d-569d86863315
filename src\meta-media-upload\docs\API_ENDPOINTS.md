# 📡 API Endpoints Documentation

## 📋 Overview
This document provides detailed information about all API endpoints available in the Meta Media Upload module.

## 🔐 Authentication
All endpoints require authentication using JWT tokens through the AuthGuard.

**Required Header**:
```
Authorization: Bearer <jwt_token>
```

## 📁 Base Path
All endpoints are prefixed with `/meta-media-upload`

---

## 🚀 1. Start Upload Session

### **Endpoint**
```http
POST /meta-media-upload/start-session
```

### **Purpose**
Creates a new upload session with Meta's Resumable Upload API for uploading large files.

### **Headers**
| Header | Required | Description |
|--------|----------|-------------|
| `Authorization` | ✅ | Bearer token for user authentication |
| `x-meta-app-id` | ✅ | Meta App ID for the upload session |
| `Content-Type` | ✅ | Must be `application/json` |

**Note**: Meta access token is automatically retrieved from user's stored credentials in the workspace.

### **Request Body**
```typescript
{
  "fileName": string;      // Name of the file to upload
  "fileLength": number;    // File size in bytes (1 - 100MB)
  "fileType": string;      // MIME type (application/pdf, image/jpeg, image/jpg, image/png, video/mp4)
}
```

### **Request Example**
```json
{
  "fileName": "document.pdf",
  "fileLength": 1024000,
  "fileType": "application/pdf"
}
```

### **Response**
```typescript
{
  "success": boolean;
  "message": string;
  "data": {
    "id": string;          // Upload session ID (e.g., "upload:abc123")
    "success": boolean;
    "message": string;
  };
  "status_code": number;
  "timestamp": string;
}
```

### **Response Example**
```json
{
  "success": true,
  "message": "Upload session created successfully",
  "data": {
    "id": "upload:abc123def456",
    "success": true,
    "message": "Upload session created successfully"
  },
  "status_code": 201,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### **Error Responses**
| Status Code | Error | Description |
|-------------|-------|-------------|
| 400 | Invalid file type | Unsupported MIME type |
| 400 | File too large | File size exceeds 100MB |
| 400 | Invalid file name | File name contains invalid characters |
| 401 | Invalid token | Meta access token expired or invalid |
| 403 | Permission denied | Insufficient permissions for Meta API |
| 500 | Meta API error | Meta API temporarily unavailable |

---

## 📤 2. Upload File

### **Endpoint**
```http
POST /meta-media-upload/upload
```

### **Purpose**
Uploads file data to an existing upload session. Supports resuming from a specific offset.

### **Headers**
| Header | Required | Description |
|--------|----------|-------------|
| `Authorization` | ✅ | Bearer token for user authentication |
| `Content-Type` | ✅ | Must be `multipart/form-data` |

**Note**: Meta access token is automatically retrieved from user's stored credentials in the workspace.

### **Form Data**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `file` | File | ✅ | Binary file data to upload |
| `uploadSessionId` | string | ✅ | Upload session ID from start-session |
| `fileOffset` | number | ❌ | Byte offset to resume from (default: 0) |

### **Request Example**
```javascript
const formData = new FormData();
formData.append('file', fileBlob);
formData.append('uploadSessionId', 'upload:abc123def456');
formData.append('fileOffset', '0');
```

### **Response**
```typescript
{
  "success": boolean;
  "message": string;
  "data": {
    "handleId": string;        // File handle ID for publishing
    "success": boolean;
    "message": string;
    "uploadSessionId": string; // Original upload session ID
  };
  "status_code": number;
  "timestamp": string;
}
```

### **Response Example**
```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "handleId": "2:c2FtcGxlRG9jdW1lbnQ...",
    "success": true,
    "message": "File uploaded successfully",
    "uploadSessionId": "upload:abc123def456"
  },
  "status_code": 200,
  "timestamp": "2024-01-15T10:31:00.000Z"
}
```

### **Error Responses**
| Status Code | Error | Description |
|-------------|-------|-------------|
| 400 | File required | No file provided in request |
| 400 | Invalid session ID | Upload session ID format invalid |
| 400 | Invalid offset | File offset value invalid |
| 401 | Invalid token | Meta access token expired or invalid |
| 404 | Session not found | Upload session not found or expired |
| 500 | Upload failed | File upload failed due to Meta API error |

---

## 🔄 3. Resume Upload Session

### **Endpoint**
```http
GET /meta-media-upload/resume/:uploadSessionId
```

### **Purpose**
Retrieves the current status of an upload session, including the file offset for resuming interrupted uploads.

### **Headers**
| Header | Required | Description |
|--------|----------|-------------|
| `Authorization` | ✅ | Bearer token for user authentication |

**Note**: Meta access token is automatically retrieved from user's stored credentials in the workspace.

### **Path Parameters**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `uploadSessionId` | string | ✅ | Upload session ID to check status |

### **Request Example**
```http
GET /meta-media-upload/resume/upload:abc123def456
Authorization: Bearer <jwt_token>
x-meta-access-token: <meta_access_token>
```

### **Response**
```typescript
{
  "success": boolean;
  "message": string;
  "data": {
    "id": string;          // Upload session ID
    "fileOffset": number;  // Current byte offset in upload
    "success": boolean;
    "message": string;
  };
  "status_code": number;
  "timestamp": string;
}
```

### **Response Example**
```json
{
  "success": true,
  "message": "Upload session status retrieved successfully",
  "data": {
    "id": "upload:abc123def456",
    "fileOffset": 512000,
    "success": true,
    "message": "Upload session status retrieved successfully"
  },
  "status_code": 200,
  "timestamp": "2024-01-15T10:32:00.000Z"
}
```

### **Error Responses**
| Status Code | Error | Description |
|-------------|-------|-------------|
| 400 | Invalid session ID | Upload session ID format invalid |
| 401 | Invalid token | Meta access token expired or invalid |
| 404 | Session not found | Upload session not found or expired |
| 500 | Resume failed | Failed to retrieve session status |

---

## 🏥 4. Health Check

### **Endpoint**
```http
GET /meta-media-upload/health
```

### **Purpose**
Checks the health status of the Meta Media Upload service.

### **Headers**
| Header | Required | Description |
|--------|----------|-------------|
| `Authorization` | ✅ | Bearer token for user authentication |

### **Response**
```typescript
{
  "success": boolean;
  "message": string;
  "data": {
    "status": string;      // Service status
    "service": string;     // Service name
  };
  "status_code": number;
  "timestamp": string;
}
```

### **Response Example**
```json
{
  "success": true,
  "message": "Meta Media Upload service is running",
  "data": {
    "status": "healthy",
    "service": "meta-media-upload"
  },
  "status_code": 200,
  "timestamp": "2024-01-15T10:33:00.000Z"
}
```

---

## 📝 Complete Upload Flow Example

### **Step 1: Start Upload Session**
```bash
curl -X POST "http://localhost:3000/meta-media-upload/start-session" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "x-meta-app-id: <meta_app_id>" \
  -H "Content-Type: application/json" \
  -d '{
    "fileName": "document.pdf",
    "fileLength": 1024000,
    "fileType": "application/pdf"
  }'
```

### **Step 2: Upload File**
```bash
curl -X POST "http://localhost:3000/meta-media-upload/upload" \
  -H "Authorization: Bearer <jwt_token>" \
  -F "file=@document.pdf" \
  -F "uploadSessionId=upload:abc123def456" \
  -F "fileOffset=0"
```

### **Step 3: Resume Upload (if needed)**
```bash
curl -X GET "http://localhost:3000/meta-media-upload/resume/upload:abc123def456" \
  -H "Authorization: Bearer <jwt_token>"
```

## 🔧 JavaScript/TypeScript Usage

### **Complete Upload Implementation**
```typescript
class MetaMediaUploadClient {
  private baseUrl: string;
  private authToken: string;
  private metaAppId: string;
  private metaAccessToken: string;

  constructor(config: {
    baseUrl: string;
    authToken: string;
    metaAppId: string;
    metaAccessToken: string;
  }) {
    this.baseUrl = config.baseUrl;
    this.authToken = config.authToken;
    this.metaAppId = config.metaAppId;
    this.metaAccessToken = config.metaAccessToken;
  }

  async uploadFile(file: File): Promise<string> {
    // 1. Start upload session
    const sessionResponse = await this.startUploadSession(file);
    const uploadSessionId = sessionResponse.data.id;

    // 2. Upload file
    const uploadResponse = await this.uploadFileData(file, uploadSessionId);
    return uploadResponse.data.handleId;
  }

  private async startUploadSession(file: File) {
    const response = await fetch(`${this.baseUrl}/meta-media-upload/start-session`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'x-meta-app-id': this.metaAppId,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fileName: file.name,
        fileLength: file.size,
        fileType: file.type,
      }),
    });

    return response.json();
  }

  private async uploadFileData(file: File, uploadSessionId: string) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('uploadSessionId', uploadSessionId);

    const response = await fetch(`${this.baseUrl}/meta-media-upload/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
      },
      body: formData,
    });

    return response.json();
  }
}
```

## ⚠️ Important Notes

1. **File Size Limit**: Maximum file size is 100MB
2. **Supported Formats**: Only PDF, JPEG, JPG, PNG, and MP4 files are supported
3. **Session Expiry**: Upload sessions may expire after a certain period
4. **Resume Capability**: Use the resume endpoint to check upload progress
5. **Error Handling**: Always handle potential errors and retry logic
6. **Authentication**: Ensure Meta access tokens are valid and have necessary permissions
