export type SegmentOperator = {
  key: string;
  label: string;
  valueType: 'string' | 'boolean' | 'array' | 'any' | 'none';
  description: string;
  applicableFields?: string[]; // hints
};

export const SEGMENT_OPERATORS: SegmentOperator[] = [
  { key: 'equals', label: 'Equals', valueType: 'any', description: 'Field value equals the given value' },
  { key: 'notEquals', label: 'Not equals', valueType: 'any', description: 'Field value is not equal to the given value' },
  { key: 'contains', label: 'Contains', valueType: 'string', description: 'Field contains the given substring (case-insensitive)', applicableFields: ['firstName','lastName','chatName','email'] },
  { key: 'notContains', label: 'Does not contain', valueType: 'string', description: 'Field does not contain the given substring (case-insensitive)', applicableFields: ['firstName','lastName','chatName','email'] },
  { key: 'startsWith', label: 'Starts with', valueType: 'string', description: 'Field starts with the given prefix (case-insensitive)' },
  { key: 'endsWith', label: 'Ends with', valueType: 'string', description: 'Field ends with the given suffix (case-insensitive)' },
  { key: 'regex', label: 'Regex', valueType: 'string', description: 'Field matches the provided regular expression' },
  { key: 'exists', label: 'Exists', valueType: 'boolean', description: 'Checks whether the field exists' },
  { key: 'in', label: 'In list', valueType: 'array', description: 'Field value is one of the provided values' },
  { key: 'hasAnyTag', label: 'Has any tag', valueType: 'array', description: 'Contact has any of the provided tag IDs', applicableFields: ['tagsId'] },
  { key: 'hasAllTags', label: 'Has all tags', valueType: 'array', description: 'Contact has all of the provided tag IDs', applicableFields: ['tagsId'] },
];




