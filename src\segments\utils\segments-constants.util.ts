/**
 * Constants for segments module
 */
export const SEGMENTS_CONSTANTS = {
  // Default values
  DEFAULTS: {
    MATCH_TYPE: 'all',
    PAGE: 1,
    LIMIT: 10,
    MAX_LIMIT: 100,
  },

  // Error messages
  ERROR_MESSAGES: {
    SEGMENT_CREATION_FAILED: 'Failed to create segment',
    SEGMENT_NOT_FOUND: 'Segment not found',
    SEGMENT_UPDATE_FAILED: 'Failed to update segment',
    SEGMENT_DELETE_FAILED: 'Failed to delete segment',
    SEGMENTS_FETCH_FAILED: 'Failed to fetch segments',
    USER_WORKSPACE_NOT_FOUND: 'User workspace not found',
    DUPLICATE_SEGMENT_NAME: 'Segment name already exists in workspace',
    INVALID_SEGMENT_RULES: 'Invalid segment rules',
    INVALID_SEGMENT_OPERATOR: 'Invalid segment operator',
    INVALID_SEGMENT_FIELD: 'Invalid segment field',
    MISSING_SEGMENT_NAME: 'Segment name is required',
    MISSING_SEGMENT_RULES: 'Segment rules are required',
    INVALID_MATCH_TYPE: 'Match type must be "all" or "any"',
    CONTACTS_FETCH_FAILED: 'Failed to fetch contacts for segment',
  },

  // Success messages
  SUCCESS_MESSAGES: {
    SEGMENT_CREATED: 'Segment created successfully',
    SEGMENT_UPDATED: 'Segment updated successfully',
    SEGMENT_DELETED: 'Segment deleted successfully',
    SEGMENTS_FETCHED: 'Segments fetched successfully',
    SEGMENT_FETCHED: 'Segment fetched successfully',
    CONTACTS_FETCHED: 'Contacts fetched successfully',
    OPERATORS_FETCHED: 'Operators fetched successfully',
  },

  // HTTP status codes
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    NOT_FOUND: 404,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
  },

  // Validation rules
  VALIDATION: {
    NAME_MIN_LENGTH: 1,
    NAME_MAX_LENGTH: 100,
    DESCRIPTION_MAX_LENGTH: 500,
    MAX_RULES_PER_SEGMENT: 20,
    MAX_RULE_VALUE_LENGTH: 1000,
  },

  // Segment operators
  OPERATORS: {
    EQUALS: 'equals',
    NOT_EQUALS: 'notEquals',
    CONTAINS: 'contains',
    NOT_CONTAINS: 'notContains',
    STARTS_WITH: 'startsWith',
    ENDS_WITH: 'endsWith',
    REGEX: 'regex',
    EXISTS: 'exists',
    IN: 'in',
    NOT_IN: 'notIn',
    HAS_ANY_TAG: 'hasAnyTag',
    HAS_ALL_TAGS: 'hasAllTags',
  },

  // Match types
  MATCH_TYPES: {
    ALL: 'all',
    ANY: 'any',
  },

  // Supported fields
  SUPPORTED_FIELDS: {
    FIRST_NAME: 'firstName',
    LAST_NAME: 'lastName',
    CHAT_NAME: 'chatName',
    EMAIL: 'email',
    PHONE_NUMBER: 'phoneNumber',
    COUNTRY_CODE: 'countryCode',
    SUBSCRIBED: 'subscribed',
    TAGS_ID: 'tagsId',
    SOURCE: 'source',
    CREATED_AT: 'createdAt',
    UPDATED_AT: 'updatedAt',
  },
} as const;

/**
 * Type for segments constants
 */
export type SegmentsConstants = typeof SEGMENTS_CONSTANTS;
