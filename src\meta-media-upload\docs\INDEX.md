# 📚 Meta Media Upload Module - Documentation Index

## 🎯 Overview
This document provides an index to all documentation available for the Meta Media Upload module, which implements Meta's Resumable Upload API for uploading large files to Meta's social graph.

## 📁 Documentation Structure

### **Core Documentation**
- **[README.md](../README.md)** - Main module documentation with overview, features, and quick start guide
- **[ARCHITECTURE.md](./ARCHITECTURE.md)** - Technical architecture, design patterns, and component details
- **[API_ENDPOINTS.md](./API_ENDPOINTS.md)** - Comprehensive API endpoint documentation with examples

### **Module Components**

#### **Source Files**
```
src/meta-media-upload/
├── meta-media-upload.module.ts       # Module configuration
├── meta-media-upload.controller.ts   # HTTP endpoints controller
├── meta-media-upload.service.ts      # Business logic service
├── dto/                              # Data Transfer Objects
│   ├── upload-session.dto.ts         # Upload session DTOs
│   ├── upload-file.dto.ts            # File upload DTOs
│   ├── resume-upload.dto.ts          # Resume upload DTOs
│   └── index.ts                      # DTO exports
└── utils/                            # Utility classes
    ├── upload-response.util.ts       # Response formatting
    ├── upload-validation.util.ts     # Input validation
    └── upload-constants.util.ts      # Constants and config
```

## 🚀 Quick Start Guide

### **1. API Endpoints**
- `POST /meta-media-upload/start-session` - Start upload session
- `POST /meta-media-upload/upload` - Upload file data
- `GET /meta-media-upload/resume/:sessionId` - Resume interrupted upload
- `GET /meta-media-upload/health` - Health check

### **2. Authentication Requirements**
- JWT token via `Authorization: Bearer <token>` header
- Meta App ID via `x-meta-app-id` header (for start session)
- Meta access token via `x-meta-access-token` header

### **3. Supported File Types**
- **Documents**: PDF
- **Images**: JPEG, JPG, PNG
- **Videos**: MP4
- **Size Limit**: 100MB maximum

## 📖 Detailed Documentation

### **API Reference**
For complete API documentation including request/response examples, error codes, and usage patterns, see:
- **[API_ENDPOINTS.md](./API_ENDPOINTS.md)**

### **Technical Details**
For architecture overview, design patterns, security considerations, and implementation details, see:
- **[ARCHITECTURE.md](./ARCHITECTURE.md)**

### **Module Overview**
For features, benefits, and integration guide, see:
- **[README.md](../README.md)**

## 🔧 Integration Examples

### **Basic Upload Flow**
```typescript
// 1. Start upload session
const session = await startUploadSession({
  fileName: 'document.pdf',
  fileLength: 1024000,
  fileType: 'application/pdf'
});

// 2. Upload file
const result = await uploadFile({
  uploadSessionId: session.id,
  file: fileBuffer,
  fileOffset: 0
});

// 3. Get handle ID for publishing
const handleId = result.handleId;
```

### **Resume Interrupted Upload**
```typescript
// Check upload status
const status = await resumeUpload(uploadSessionId);
const fileOffset = status.fileOffset;

// Resume upload from offset
const result = await uploadFile({
  uploadSessionId: uploadSessionId,
  file: fileBuffer.slice(fileOffset),
  fileOffset: fileOffset
});
```

## 🛡️ Security & Validation

### **Authentication**
- JWT token validation through AuthGuard
- User context extraction and validation
- Meta API token validation

### **File Validation**
- File type validation (PDF, JPEG, JPG, PNG, MP4)
- File size validation (up to 100MB)
- File name sanitization
- Upload session ID validation

### **Error Handling**
- Comprehensive Meta API error mapping
- Structured error responses
- Retry logic for transient failures
- Detailed logging and monitoring

## 📊 Monitoring & Observability

### **Logging**
- Structured logging with context
- Request/response logging
- Error tracking and reporting
- Performance metrics collection

### **Health Checks**
- Service health monitoring
- Meta API connectivity checks
- Upload session status tracking

## 🔄 Meta API Integration

### **Supported Operations**
- **Start Upload Session**: Create new upload session
- **Upload File Data**: Upload file with resume support
- **Resume Upload**: Check session status and resume

### **Meta API Version**
- **Base URL**: `https://graph.facebook.com/v23.0`
- **Authentication**: OAuth 2.0 with access tokens
- **Rate Limits**: Meta API rate limits apply
- **Error Handling**: Comprehensive error mapping

## 🧪 Testing

### **Test Coverage**
- Unit tests for all service methods
- Integration tests for API endpoints
- Error scenario testing
- Meta API mock testing

### **Test Files**
```
__tests__/
├── unit/
│   ├── meta-media-upload.service.spec.ts
│   ├── meta-media-upload.controller.spec.ts
│   └── utils/
├── integration/
│   ├── upload-flow.e2e.spec.ts
│   └── meta-api-integration.e2e.spec.ts
└── fixtures/
```

## 🚀 Deployment

### **Environment Configuration**
- Production and development settings
- Meta API timeout configuration
- Retry logic configuration
- Logging level configuration

### **Scalability**
- Stateless service design
- Horizontal scaling support
- Load balancing compatibility
- Resource optimization

## 📞 Support & Troubleshooting

### **Common Issues**
- **File size exceeded**: Check 100MB limit
- **Invalid file type**: Verify supported formats
- **Token expired**: Refresh Meta access token
- **Session not found**: Check upload session ID

### **Error Codes**
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (token issues)
- **403**: Forbidden (permission issues)
- **404**: Not Found (session not found)
- **500**: Internal Server Error (Meta API issues)

## 🔗 Related Resources

### **External Documentation**
- [Meta Resumable Upload API](https://developers.facebook.com/docs/graph-api/guides/upload)
- [Meta Graph API Overview](https://developers.facebook.com/docs/graph-api)
- [NestJS Documentation](https://docs.nestjs.com/)

### **Internal Modules**
- **Auth Module**: Authentication and authorization
- **Meta API Module**: Meta API integration utilities
- **Storage Module**: File storage management

## 📝 Changelog

### **Version 1.0.0**
- Initial implementation of Meta Media Upload module
- Support for PDF, JPEG, JPG, PNG, MP4 files
- Upload session management
- Resume capability for interrupted uploads
- Comprehensive error handling and validation
- Full documentation and testing coverage
