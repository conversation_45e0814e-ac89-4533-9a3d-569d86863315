import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ot<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Optional } from 'class-validator';

/**
 * DTO for uploading a file chunk to Meta's Resumable Upload API
 */
export class UploadFileDto {
  @IsString()
  @IsNotEmpty()
  uploadSessionId: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  fileOffset?: number = 0;

  // File data will be handled as binary in the controller
}

/**
 * Response DTO for file upload completion
 */
export class UploadFileResponseDto {
  handleId: string;
  success: boolean;
  message: string;
  uploadSessionId: string;
}
