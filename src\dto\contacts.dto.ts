
import { IsArray, IsBoolean, IsEmail, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsNumberString, IsOptional, IsString, Matches, MinLength, IsUUID, ValidateIf, ValidateNested } from "class-validator";
import { Type } from 'class-transformer';

export class ContactCustomFieldValueDto {
    @IsMongoId({ message: 'fieldId must be a valid Mongo ObjectId' })
    fieldId!: string;

    @IsOptional()
    value?: any;
}

export class ContactDto {
    @IsString({ message: 'First name must be a string' })
    @IsOptional()
    firstName?: string;

    @IsString({ message: 'Last name must be a string' })
    @IsOptional()
    lastName?: string;

    @IsString({ message: 'Chat name must be a string' })
    @IsOptional()
    chatName?: string;

    @IsEmail({}, { message: 'Email must be a valid email address' })
    @IsOptional()
    email?: string;

    @IsNumberString({}, { message: 'Phone number must be a number'})
    @IsOptional()
    @MinLength(10, { message: 'Phone number must be at least 10 digits'})
    @Matches(/^[0-9]+$/, { message: 'Phone number must contain only digits'})
    phoneNumber?: string;

    @IsNumberString({}, { message: 'Country code must be a number'})
    @IsNotEmpty({ message: 'Country code is required'})
    @MinLength(1, { message: 'Country code must be at least 1 digit'})
    countryCode: string;

    @IsString({ message: 'Source must be a string' })
    @IsNotEmpty({ message: 'Source is required' })
    source: string;

    @IsUUID(undefined, { message: 'createdBy must be a valid UUID' })
    @IsOptional()
    createdBy?: string;

    @IsArray({ message: 'tagsId must be an array' })
    @IsMongoId({ each: true, message: 'Each tag id must be a valid Mongo ObjectId' })
    @IsOptional()
    tagsId?: string[];

    @IsBoolean({ message: 'subscribed must be a boolean' })
    @IsOptional()
    subscribed?: boolean = true;

    @IsArray({ message: 'customFields must be an array of { fieldId, value }' })
    @ValidateNested({ each: true })
    @Type(() => ContactCustomFieldValueDto)
    @IsOptional()
    customFields?: ContactCustomFieldValueDto[];
}

