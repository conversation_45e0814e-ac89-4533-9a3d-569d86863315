# Kafka to Bull MQ Migration Guide

## Overview
This guide helps you migrate from Kafka to Bull MQ for better performance, cost-effectiveness, and simplicity.

## Why Bull MQ over Kafka?

### Cost Comparison
- **Kafka**: Requires separate server, high memory usage, complex setup
- **Bull MQ**: Uses existing Redis, lightweight, simple setup

### Performance Benefits
- **Faster Setup**: No need for separate Kafka server
- **Lower Resource Usage**: Redis is much lighter than Kafka
- **Better Monitoring**: Built-in Bull Dashboard
- **Easier Debugging**: Simple Redis commands

## Migration Steps

### 1. Install Redis (if not already installed)

```bash
# Using Docker (Recommended)
npm run redis:up

# Or install Redis locally
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis

# Windows
# Download from https://redis.io/download
```

### 2. Update Environment Variables

Create `.env` file from `env.example`:

```bash
cp env.example .env
```

Update Redis configuration:
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### 3. Remove Kafka Dependencies (Optional)

If you want to completely remove Kafka:

```bash
# Remove Kafka from package.json
npm uninstall kafkajs

# Remove Kafka Docker containers
npm run kafka:down
```

### 4. Test the Migration

```bash
# Start Redis
npm run redis:up

# Test queue connection
npm run test:queue

# Start your application
npm run start:dev
```

## API Changes

### Before (Kafka)
```typescript
// Old Kafka service usage
await this.kafkaService.sendCampaignMessage(message);
await this.kafkaService.sendCampaignBatch(batchMessage);
```

### After (Bull MQ)
```typescript
// New Bull MQ service usage
await this.queueService.sendCampaignMessage(message);
await this.queueService.sendCampaignBatch(batchMessage);
```

## New Queue Management Endpoints

### Get Queue Statistics
```http
GET /queue/stats
```

### Pause Queue
```http
POST /queue/pause/campaign-messages
```

### Resume Queue
```http
POST /queue/resume/campaign-messages
```

### Clear Queue
```http
POST /queue/clear/campaign-messages
```

## Queue Types

### 1. Campaign Messages Queue
- **Purpose**: Process immediate campaign messages
- **Priority**: Based on message priority (HIGH, NORMAL, LOW)
- **Retry**: 3 attempts with exponential backoff

### 2. Scheduled Campaign Messages Queue
- **Purpose**: Process scheduled/delayed messages
- **Features**: Built-in delay support
- **Retry**: 3 attempts with exponential backoff

### 3. Campaign Retry Messages Queue
- **Purpose**: Handle failed message retries
- **Features**: More retry attempts (5)
- **Backoff**: Longer delays for retry scenarios

## Monitoring

### Redis Commander
Access at: http://localhost:8081
- View all Redis data
- Monitor queue keys
- Debug queue issues

### Queue Statistics
```typescript
const stats = await queueService.getQueueStats();
console.log(stats);
// Output:
// {
//   campaignMessages: { waiting: 10, active: 2, completed: 100, failed: 5 },
//   scheduledMessages: { waiting: 5, active: 1, completed: 50, failed: 2 },
//   retryMessages: { waiting: 3, active: 0, completed: 20, failed: 1 }
// }
```

## Performance Tuning

### Redis Configuration
```yaml
# docker-compose.redis.yml
command: >
  redis-server
  --maxmemory 256mb
  --maxmemory-policy allkeys-lru
  --save 900 1
  --save 300 10
  --save 60 10000
```

### Queue Configuration
```typescript
// In queue.module.ts
defaultJobOptions: {
  removeOnComplete: 100,  // Keep last 100 completed jobs
  removeOnFail: 50,       // Keep last 50 failed jobs
  attempts: 3,            // Retry 3 times
  backoff: {
    type: 'exponential',
    delay: 2000,          // Start with 2 second delay
  },
}
```

## Troubleshooting

### Common Issues

#### 1. Redis Connection Failed
```bash
# Check if Redis is running
redis-cli ping

# Start Redis
npm run redis:up
```

#### 2. Queue Jobs Not Processing
```bash
# Check queue statistics
curl http://localhost:3000/queue/stats

# Check Redis keys
redis-cli keys "*bull*"
```

#### 3. High Memory Usage
```bash
# Check Redis memory
redis-cli info memory

# Clean old jobs
curl -X POST http://localhost:3000/queue/clear/campaign-messages
```

### Debug Commands

```bash
# View all queue keys
redis-cli keys "*bull*"

# View specific queue jobs
redis-cli llen bull:campaign-messages:waiting
redis-cli llen bull:campaign-messages:active
redis-cli llen bull:campaign-messages:completed
redis-cli llen bull:campaign-messages:failed

# View job details
redis-cli hgetall bull:campaign-messages:123
```

## Rollback Plan

If you need to rollback to Kafka:

1. **Restore Kafka Module**:
```typescript
// In app.module.ts
import { KafkaModule } from './kafka/kafka.module';
// Replace QueueModule with KafkaModule
```

2. **Update Campaign Module**:
```typescript
// In campaign.module.ts
import { KafkaModule } from '../kafka/kafka.module';
// Replace QueueModule with KafkaModule
```

3. **Update Campaign Processor**:
```typescript
// In campaign-processor.service.ts
import { KafkaService } from '../kafka/kafka.service';
// Replace QueueService with KafkaService
```

4. **Start Kafka**:
```bash
npm run kafka:up
```

## Benefits After Migration

### Cost Savings
- **No separate Kafka server needed**
- **Lower memory usage**
- **Simpler infrastructure**

### Performance Improvements
- **Faster message processing**
- **Better error handling**
- **Built-in retry mechanisms**

### Developer Experience
- **Easier debugging**
- **Better monitoring**
- **Simpler configuration**

## Support

For issues or questions:
1. Check Redis connection: `npm run test:queue`
2. View queue stats: `GET /queue/stats`
3. Check application logs
4. Review Redis Commander dashboard

## Next Steps

1. **Monitor Performance**: Track queue processing times
2. **Optimize Configuration**: Tune Redis and queue settings
3. **Add Monitoring**: Set up alerts for queue failures
4. **Scale**: Add more Redis instances if needed
