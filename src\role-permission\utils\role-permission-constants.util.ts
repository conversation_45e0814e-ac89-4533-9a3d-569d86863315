import { HttpStatus } from '@nestjs/common';

/**
 * Constants for Role-Permission module
 */
export const ROLE_PERMISSION_CONSTANTS = {
  // HTTP Status Codes
  HTTP_STATUS: {
    OK: HttpStatus.OK,
    CREATED: HttpStatus.CREATED,
    BAD_REQUEST: HttpStatus.BAD_REQUEST,
    UNAUTHORIZED: HttpStatus.UNAUTHORIZED,
    FORBIDDEN: HttpStatus.FORBIDDEN,
    NOT_FOUND: HttpStatus.NOT_FOUND,
    CONFLICT: HttpStatus.CONFLICT,
    INTERNAL_SERVER_ERROR: HttpStatus.INTERNAL_SERVER_ERROR,
  },

  // Success Messages
  SUCCESS_MESSAGES: {
    ROLE_CREATED: 'Role created successfully',
    ROLE_UPDATED: 'Role updated successfully',
    ROLE_DELETED: 'Role deleted successfully',
    ROLE_FETCHED: 'Role retrieved successfully',
    R<PERSON><PERSON>_FETCHED: 'Roles retrieved successfully',
    PERMI<PERSON>ION_CREATED: 'Permission created successfully',
    PERMI<PERSON>ION_UPDATED: 'Permission updated successfully',
    PERMISSION_DELETED: 'Permission deleted successfully',
    PERMISSION_FETCHED: 'Permission retrieved successfully',
    PERMISSIONS_FETCHED: 'Permissions retrieved successfully',
    PERMISSION_ASSIGNED: 'Permission assigned to role successfully',
    PERMISSION_REVOKED: 'Permission revoked from role successfully',
    ROLE_PERMISSIONS_FETCHED: 'Role permissions retrieved successfully',
  },

  // Error Messages
  ERROR_MESSAGES: {
    USER_NOT_FOUND: 'User not found',
    USER_PROFILE_NOT_FOUND: 'User profile not found',
    WORKSPACE_ACCESS_DENIED: 'You do not have access to this workspace',
    ROLE_NOT_FOUND: 'Role not found',
    PERMISSION_NOT_FOUND: 'Permission not found',
    DUPLICATE_ROLE_NAME: 'Role with this name already exists in the workspace',
    DUPLICATE_PERMISSION_NAME: 'Permission with this name already exists',
    ROLE_CREATION_FAILED: 'Failed to create role',
    ROLE_UPDATE_FAILED: 'Failed to update role',
    ROLE_DELETION_FAILED: 'Failed to delete role',
    PERMISSION_CREATION_FAILED: 'Failed to create permission',
    PERMISSION_UPDATE_FAILED: 'Failed to update permission',
    PERMISSION_DELETION_FAILED: 'Failed to delete permission',
    PERMISSION_ASSIGNMENT_FAILED: 'Failed to assign permission to role',
    PERMISSION_REVOCATION_FAILED: 'Failed to revoke permission from role',
    INVALID_ROLE_DATA: 'Invalid role data provided',
    INVALID_PERMISSION_DATA: 'Invalid permission data provided',
    INVALID_PERMISSION_ASSIGNMENT: 'Invalid permission assignment data',
    ROLE_IN_USE: 'Cannot delete role as it is currently assigned to users',
    PERMISSION_IN_USE: 'Cannot delete permission as it is currently assigned to roles',
  },

  // Validation Rules
  VALIDATION: {
    ROLE_NAME_MIN_LENGTH: 1,
    ROLE_NAME_MAX_LENGTH: 100,
    ROLE_DESCRIPTION_MAX_LENGTH: 500,
    PERMISSION_NAME_MIN_LENGTH: 1,
    PERMISSION_NAME_MAX_LENGTH: 100,
    PERMISSION_DESCRIPTION_MAX_LENGTH: 500,
    PERMISSION_RESOURCE_MAX_LENGTH: 100,
    PERMISSION_ACTION_MAX_LENGTH: 50,
  },

  // Default Values
  DEFAULTS: {
    ROLE_STATUS: 'active',
    PERMISSION_STATUS: 'active',
    PAGINATION_LIMIT: 10,
    PAGINATION_MAX_LIMIT: 100,
  },

  // Permission Actions
  PERMISSION_ACTIONS: {
    CREATE: 'create',
    READ: 'read',
    UPDATE: 'update',
    DELETE: 'delete',
    MANAGE: 'manage',
  },

  // Permission Resources
  PERMISSION_RESOURCES: {
    USERS: 'users',
    ROLES: 'roles',
    PERMISSIONS: 'permissions',
    WORKSPACES: 'workspaces',
    CONTACTS: 'contacts',
    MESSAGES: 'messages',
    TEMPLATES: 'templates',
    CAMPAIGNS: 'campaigns',
    ANALYTICS: 'analytics',
    SETTINGS: 'settings',
  },
} as const;
