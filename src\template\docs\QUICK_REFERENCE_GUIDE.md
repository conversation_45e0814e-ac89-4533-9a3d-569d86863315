# Template API Quick Reference Guide

## 🚀 Quick Start

### Base URL & Authentication
```javascript
const API_BASE = 'http://localhost:3000';
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
};
```

### Essential Endpoints
```javascript
const endpoints = {
  create: 'POST /templates',
  update: 'PUT /templates/:id',
  delete: 'DELETE /templates/:id',
  get: 'GET /templates/:id',
  list: 'GET /templates',
  ai: 'POST /templates/ai',
  voice: 'POST /templates/voice'
};
```

## 📝 Template Components

### Basic Structure
```javascript
{
  name: 'template_name',
  description: 'Template description',
  category: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION',
  language: 'en',
  waba_id: 'your_waba_id',
  components: [...]
}
```

### Component Types

#### 1. HEADER Component
```javascript
// Text Header
{
  type: 'HEADER',
  format: 'TEXT',
  text: '{{1}}',  // With variable
  example: {
    header_text: ['Appointment Confirmation']
  }
}

// Static Header
{
  type: 'HEADER',
  format: 'TEXT',
  text: 'Appointment Confirmation'  // No example needed
}

// Image Header
{
  type: 'HEADER',
  format: 'IMAGE',
  text: '{{1}}',
  example: {
    header_text: ['Product Launch']
  }
}
```

#### 2. BODY Component
```javascript
// With Variables
{
  type: 'BODY',
  text: 'Hi {{1}}, your appointment is on {{2}}.',
  example: {
    body_text: [['John', '2:00 PM']]  // Nested array!
  }
}

// Static Body
{
  type: 'BODY',
  text: 'Welcome to our service!'
}
```

#### 3. FOOTER Component
```javascript
{
  type: 'FOOTER',
  text: 'Thank you for choosing us!'
}
```

#### 4. BUTTONS Component
```javascript
{
  type: 'BUTTONS',
  buttons: [
    {
      type: 'QUICK_REPLY',
      text: 'View Products'
    },
    {
      type: 'URL',
      text: 'Visit Website',
      url: 'https://example.com'
    }
  ]
}
```

## 🔥 Common Templates

### 1. Simple Text Template
```javascript
{
  name: 'welcome_message',
  description: 'Welcome new users',
  category: 'MARKETING',
  language: 'en',
  waba_id: 'your_waba_id',
  components: [
    {
      type: 'HEADER',
      format: 'TEXT',
      text: 'Welcome!'
    },
    {
      type: 'BODY',
      text: 'Hi {{1}}, welcome to our platform!',
      example: {
        body_text: [['John']]
      }
    },
    {
      type: 'FOOTER',
      text: 'Start exploring now!'
    }
  ]
}
```

### 2. Interactive Template
```javascript
{
  name: 'product_catalog',
  description: 'Show products with buttons',
  category: 'MARKETING',
  language: 'en',
  waba_id: 'your_waba_id',
  components: [
    {
      type: 'HEADER',
      format: 'TEXT',
      text: '{{1}}',
      example: {
        header_text: ['New Products Available']
      }
    },
    {
      type: 'BODY',
      text: 'Check out our latest products!'
    },
    {
      type: 'BUTTONS',
      buttons: [
        { type: 'QUICK_REPLY', text: 'View Products' },
        { type: 'URL', text: 'Visit Store', url: 'https://example.com' }
      ]
    }
  ]
}
```

### 3. Order Confirmation
```javascript
{
  name: 'order_confirmation',
  description: 'Confirm customer orders',
  category: 'UTILITY',
  language: 'en',
  waba_id: 'your_waba_id',
  components: [
    {
      type: 'HEADER',
      format: 'TEXT',
      text: '{{1}}',
      example: {
        header_text: ['Order Confirmation']
      }
    },
    {
      type: 'BODY',
      text: 'Hi {{1}}, your order #{{2}} is confirmed. Delivery: {{3}}.',
      example: {
        body_text: [['John', 'ORD-12345', '2024-01-15']]
      }
    },
    {
      type: 'FOOTER',
      text: 'Track your order online!'
    }
  ]
}
```

## 🤖 AI Generation

### Basic AI Request
```javascript
{
  prompt: 'Create a welcome message for new customers',
  waba_id: 'your_waba_id',
  category: 'MARKETING',
  language: 'en'
}
```

### Advanced AI Prompts
```javascript
// Order confirmation
{
  prompt: 'Create an order confirmation template with order number, customer name, and delivery date variables',
  waba_id: 'your_waba_id',
  category: 'UTILITY'
}

// Promotional message
{
  prompt: 'Create a flash sale announcement with discount percentage and product variables, include interactive buttons',
  waba_id: 'your_waba_id',
  category: 'MARKETING'
}

// Appointment reminder
{
  prompt: 'Create an appointment reminder with customer name, appointment time, and reschedule option',
  waba_id: 'your_waba_id',
  category: 'UTILITY'
}
```

## 🎤 Voice to Template

### FormData Request
```javascript
const formData = new FormData();
formData.append('file', audioFile);  // Audio file
formData.append('waba_id', 'your_waba_id');

// Don't set Content-Type header for FormData
fetch('/templates/voice', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` },
  body: formData
});
```

## 📊 List & Filter Templates

### Basic List
```javascript
GET /templates
```

### With Filters
```javascript
// Marketing templates only
GET /templates?category=MARKETING

// Approved templates
GET /templates?status=APPROVED

// AI generated templates
GET /templates?ai_generated=true

// Search by name
GET /templates?search=welcome

// Pagination
GET /templates?page=2&limit=20

// Combined filters
GET /templates?category=MARKETING&status=APPROVED&language=en&page=1&limit=10
```

## ⚠️ Common Pitfalls

### 1. Variable Examples Format
```javascript
// ❌ Wrong - Missing nested array
example: {
  body_text: ['John', '2:00 PM']
}

// ✅ Correct - Nested array
example: {
  body_text: [['John', '2:00 PM']]
}
```

### 2. Header with Variables
```javascript
// ❌ Wrong - No example for variable
{
  type: 'HEADER',
  format: 'TEXT',
  text: '{{1}}'
}

// ✅ Correct - Include example
{
  type: 'HEADER',
  format: 'TEXT',
  text: '{{1}}',
  example: {
    header_text: ['Appointment Confirmation']
  }
}
```

### 3. Static Headers
```javascript
// ❌ Wrong - Unnecessary example
{
  type: 'HEADER',
  format: 'TEXT',
  text: 'Welcome!',
  example: {
    header_text: ['Welcome!']
  }
}

// ✅ Correct - No example needed
{
  type: 'HEADER',
  format: 'TEXT',
  text: 'Welcome!'
}
```

## 🛠️ Helper Functions

### JavaScript/TypeScript
```javascript
// Create header component
const createHeader = (text, hasVariable = false, example = '') => {
  const component = {
    type: 'HEADER',
    format: 'TEXT',
    text: text
  };
  
  if (hasVariable && example) {
    component.example = { header_text: [example] };
  }
  
  return component;
};

// Create body component
const createBody = (text, variables = []) => {
  const component = { type: 'BODY', text: text };
  
  if (variables.length > 0) {
    component.example = { body_text: [variables] };
  }
  
  return component;
};

// Create footer component
const createFooter = (text) => ({
  type: 'FOOTER',
  text: text
});

// Create buttons component
const createButtons = (buttons) => ({
  type: 'BUTTONS',
  buttons: buttons
});

// Build complete template
const buildTemplate = (name, description, category, language, wabaId, components) => ({
  name,
  description,
  category,
  language,
  waba_id: wabaId,
  components
});
```

### Usage Example
```javascript
const welcomeTemplate = buildTemplate(
  'welcome_new_user',
  'Welcome new users to the platform',
  'MARKETING',
  'en',
  'your_waba_id',
  [
    createHeader('Welcome to {{1}}!', true, 'Our Store'),
    createBody('Hi {{1}}, welcome! We\'re excited to have you.', ['John']),
    createFooter('Start exploring now!'),
    createButtons([
      { type: 'QUICK_REPLY', text: 'Get Started' },
      { type: 'URL', text: 'Visit Website', url: 'https://example.com' }
    ])
  ]
);
```

## 📱 React Hooks Example

```javascript
import { useState, useEffect } from 'react';

const useTemplateAPI = (token) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const apiCall = async (endpoint, options = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`http://localhost:3000${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'API Error');
      }
      
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = (templateData) => 
    apiCall('/templates', {
      method: 'POST',
      body: JSON.stringify(templateData)
    });

  const updateTemplate = (id, templateData) =>
    apiCall(`/templates/${id}`, {
      method: 'PUT',
      body: JSON.stringify(templateData)
    });

  const deleteTemplate = (id, wabaId) =>
    apiCall(`/templates/${id}?waba_id=${wabaId}`, {
      method: 'DELETE'
    });

  const getTemplate = (id) => apiCall(`/templates/${id}`);
  
  const listTemplates = (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/templates?${queryString}`);
  };

  const generateAITemplate = (prompt, wabaId, category = 'MARKETING') =>
    apiCall('/templates/ai', {
      method: 'POST',
      body: JSON.stringify({ prompt, waba_id: wabaId, category })
    });

  return {
    loading,
    error,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    getTemplate,
    listTemplates,
    generateAITemplate
  };
};

// Usage in component
const TemplateManager = () => {
  const { createTemplate, loading, error } = useTemplateAPI(token);
  
  const handleCreateTemplate = async () => {
    try {
      const template = await createTemplate({
        name: 'test_template',
        description: 'Test template',
        category: 'UTILITY',
        language: 'en',
        waba_id: 'your_waba_id',
        components: [
          {
            type: 'BODY',
            text: 'Hello {{1}}!',
            example: { body_text: [['World']] }
          }
        ]
      });
      
      console.log('Template created:', template);
    } catch (err) {
      console.error('Failed to create template:', err);
    }
  };

  return (
    <div>
      {loading && <p>Creating template...</p>}
      {error && <p>Error: {error}</p>}
      <button onClick={handleCreateTemplate}>
        Create Template
      </button>
    </div>
  );
};
```

## 🔍 Testing

### cURL Examples
```bash
# Create template
curl -X POST http://localhost:3000/templates \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test_template",
    "description": "Test template",
    "category": "UTILITY",
    "language": "en",
    "waba_id": "your_waba_id",
    "components": [
      {
        "type": "BODY",
        "text": "Hello {{1}}!",
        "example": {
          "body_text": [["World"]]
        }
      }
    ]
  }'

# List templates
curl -X GET "http://localhost:3000/templates?category=MARKETING" \
  -H "Authorization: Bearer your_token"

# Generate AI template
curl -X POST http://localhost:3000/templates/ai \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Create a welcome message for new customers",
    "waba_id": "your_waba_id",
    "category": "MARKETING"
  }'
```

This quick reference guide provides everything needed to get started with the Template API efficiently!
