import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { getModelToken } from '@nestjs/mongoose';
import { WebhooksService } from './webhooks.service';
import { WhatsAppService } from '../whatsapp/whatsapp.service';
import { WhatsAppMessage } from '../schema/whatsapp-message.schema';
import { WhatsAppStatus } from '../schema/whatsapp-status.schema';

describe('WebhooksService', () => {
  let service: WebhooksService;
  let configService: ConfigService;
  let whatsappService: WhatsAppService;

  const mockWhatsAppMessageModel = {
    new: jest.fn().mockReturnValue({
      save: jest.fn().mockResolvedValue({}),
    }),
  };

  const mockWhatsAppStatusModel = {
    new: jest.fn().mockReturnValue({
      save: jest.fn().mockResolvedValue({}),
    }),
  };

  const mockWhatsAppService = {
    sendTextMessage: jest.fn().mockResolvedValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhooksService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-token'),
          },
        },
        {
          provide: getModelToken(WhatsAppMessage.name),
          useValue: mockWhatsAppMessageModel,
        },
        {
          provide: getModelToken(WhatsAppStatus.name),
          useValue: mockWhatsAppStatusModel,
        },
        {
          provide: WhatsAppService,
          useValue: mockWhatsAppService,
        },
      ],
    }).compile();

    service = module.get<WebhooksService>(WebhooksService);
    configService = module.get<ConfigService>(ConfigService);
    whatsappService = module.get<WhatsAppService>(WhatsAppService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('verifyWebhook', () => {
    it('should return true for valid verification', async () => {
      const result = await service.verifyWebhook('subscribe', 'test-token');
      expect(result).toBe(true);
    });

    it('should return false for invalid mode', async () => {
      const result = await service.verifyWebhook('invalid', 'test-token');
      expect(result).toBe(false);
    });

    it('should return false for invalid token', async () => {
      const result = await service.verifyWebhook('subscribe', 'wrong-token');
      expect(result).toBe(false);
    });
  });

  describe('processWebhook', () => {
    it('should process messages correctly', async () => {
      const mockBody = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'test-entry',
            changes: [
              {
                field: 'messages',
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '+**********',
                    phone_number_id: 'test-phone-id',
                  },
                  messages: [
                    {
                      from: '**********',
                      timestamp: '**********',
                      type: 'text',
                      text: { body: 'Hello' },
                    },
                  ],
                },
              },
            ],
          },
        ],
      };

      await service.processWebhook(mockBody);
      expect(mockWhatsAppMessageModel.new).toHaveBeenCalled();
    });

    it('should process statuses correctly', async () => {
      const mockBody = {
        object: 'whatsapp_business_account',
        entry: [
          {
            id: 'test-entry',
            changes: [
              {
                field: 'statuses',
                value: {
                  messaging_product: 'whatsapp',
                  metadata: {
                    display_phone_number: '+**********',
                    phone_number_id: 'test-phone-id',
                  },
                  statuses: [
                    {
                      id: 'test-message-id',
                      status: 'delivered',
                      timestamp: '**********',
                      recipient_id: '**********',
                    },
                  ],
                },
              },
            ],
          },
        ],
      };

      await service.processWebhook(mockBody);
      expect(mockWhatsAppStatusModel.new).toHaveBeenCalled();
    });
  });
}); 