import { BadRequestException, NotFoundException } from '@nestjs/common';
import { TAGS_CONSTANTS } from './tags-constants.util';

/**
 * Utility class for tags-related validations
 */
export class TagsValidationUtil {
  /**
   * Validates tag name
   */
  static validateTagName(name: string): void {
    if (!name || typeof name !== 'string') {
      throw new BadRequestException('Tag name is required and must be a string');
    }

    const trimmedName = name.trim();
    if (trimmedName.length < TAGS_CONSTANTS.VALIDATION.NAME_MIN_LENGTH) {
      throw new BadRequestException('Tag name must be at least 1 character long');
    }

    if (trimmedName.length > TAGS_CONSTANTS.VALIDATION.NAME_MAX_LENGTH) {
      throw new BadRequestException('Tag name must not exceed 50 characters');
    }

    // Check for valid characters (alphanumeric, spaces, hyphens, underscores)
    const nameRegex = /^[a-zA-Z0-9\s\-_]+$/;
    if (!nameRegex.test(trimmedName)) {
      throw new BadRequestException('Tag name can only contain letters, numbers, spaces, hyphens, and underscores');
    }
  }

  /**
   * Validates color format
   */
  static validateColorFormat(color: string | undefined, fieldName: string): string | undefined {
    if (!color) return undefined;

    if (typeof color !== 'string') {
      throw new BadRequestException(`${fieldName} must be a string`);
    }

    const trimmedColor = color.trim();
    if (!TAGS_CONSTANTS.VALIDATION.COLOR_REGEX.test(trimmedColor)) {
      throw new BadRequestException(TAGS_CONSTANTS.ERROR_MESSAGES.INVALID_COLOR_FORMAT);
    }

    return trimmedColor.toUpperCase();
  }

  /**
   * Validates user context from request
   */
  static validateUserContext(req: any): any {
    const user = req.user;
    if (!user || !user.id) {
      throw new BadRequestException('User context not found in request');
    }
    return user;
  }

  /**
   * Validates user profile and extracts workspace ID
   */
  static validateUserProfile(userProfile: any, userProfileError: any): number {
    if (userProfileError) {
      throw new BadRequestException('Failed to fetch user profile');
    }

    if (!userProfile) {
      throw new BadRequestException(TAGS_CONSTANTS.ERROR_MESSAGES.USER_WORKSPACE_NOT_FOUND);
    }

    if (!userProfile.workspace_id) {
      throw new BadRequestException(TAGS_CONSTANTS.ERROR_MESSAGES.USER_WORKSPACE_NOT_FOUND);
    }

    return userProfile.workspace_id;
  }

  /**
   * Validates tag exists and belongs to workspace
   */
  static validateTagExists(tag: any, tagId: string): void {
    if (!tag) {
      throw new NotFoundException(TAGS_CONSTANTS.ERROR_MESSAGES.TAG_NOT_FOUND);
    }
  }

  /**
   * Validates tag creation data
   */
  static validateTagCreationData(dto: any, userId: string, workspaceId: number): any {
    // Validate name
    this.validateTagName(dto.name);

    // Validate colors
    const textColor = this.validateColorFormat(dto.text_color, 'Text color');
    const backgroundColor = this.validateColorFormat(dto.background_color, 'Background color');

    return {
      name: dto.name.trim(),
      text_color: textColor || TAGS_CONSTANTS.DEFAULTS.TEXT_COLOR,
      background_color: backgroundColor || TAGS_CONSTANTS.DEFAULTS.BACKGROUND_COLOR,
      createdBy: userId,
      workspaceId,
    };
  }

  /**
   * Validates tag update data
   */
  static validateTagUpdateData(dto: any): any {
    const updateData: any = {};

    if (dto.name !== undefined) {
      this.validateTagName(dto.name);
      updateData.name = dto.name.trim();
    }

    if (dto.text_color !== undefined) {
      updateData.text_color = this.validateColorFormat(dto.text_color, 'Text color');
    }

    if (dto.background_color !== undefined) {
      updateData.background_color = this.validateColorFormat(dto.background_color, 'Background color');
    }

    return updateData;
  }
}
