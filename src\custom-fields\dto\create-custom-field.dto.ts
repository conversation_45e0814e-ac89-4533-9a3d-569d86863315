import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString, Min<PERSON>ength, <PERSON>Length, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { CustomFieldTypeEnum } from '../../schema/custom-field.schema';

/**
 * DTO for creating a custom field
 */
export class CreateCustomFieldDto {
  @IsString({ message: 'Label must be a string' })
  @IsNotEmpty({ message: 'Label is required' })
  @MinLength(1, { message: 'Label must be at least 1 character long' })
  @MaxLength(100, { message: 'Label must not exceed 100 characters' })
  label: string;

  @IsEnum(CustomFieldTypeEnum, { 
    message: 'Type must be one of: text, number, date, datetime, dropdown, bool' 
  })
  @IsNotEmpty({ message: 'Type is required' })
  type: CustomFieldTypeEnum;

  @IsOptional()
  @IsArray({ message: 'Options must be an array' })
  @IsString({ each: true, message: 'Each option must be a string' })
  @ArrayMaxSize(20, { message: 'Maximum 20 options allowed per field' })
  options?: string[];

  @IsOptional()
  @IsBoolean({ message: 'Show on contact must be a boolean' })
  showOnContact?: boolean;

  @IsOptional()
  @IsBoolean({ message: 'Show on chat must be a boolean' })
  showOnChat?: boolean;
}


