import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types, SchemaTypes, Document } from "mongoose";

export type ContactDocument = Contact & Document;

@Schema({ _id: false })
export class ContactCustomFieldValue {
    @Prop({ type: Types.ObjectId, ref: 'CustomField', required: true })
    fieldId!: Types.ObjectId;

    @Prop({ type: SchemaTypes.Mixed })
    value?: any;
}

export const ContactCustomFieldValueSchema = SchemaFactory.createForClass(ContactCustomFieldValue);

@Schema({ timestamps: true })
export class Contact {
    @Prop()
    firstName?: string;

    @Prop()
    lastName?: string;

    @Prop()
    chatName?: string;

    @Prop()
    email?: string;

    @Prop()
    phoneNumber?: string;

    @Prop({ required: true })
    countryCode: string;

    @Prop({ required: true })
    source: string;

    @Prop({ required: true, type: Number })
    workspaceId: number;

    @Prop({ required: true, type: String })
    createdBy: string;

    @Prop({ type: [{ type: Types.ObjectId, ref: 'Tag' }], default: [] })
    tagsId?: Types.ObjectId[];

    @Prop({ type: Boolean, default: true })
    subscribed?: boolean;

    @Prop({ type: [ContactCustomFieldValueSchema], default: [] })
    customFields?: ContactCustomFieldValue[];
}

export const ContactSchema = SchemaFactory.createForClass(Contact);

// Ensure unique phoneNumber within a workspace (only when phoneNumber exists)
ContactSchema.index(
  { workspaceId: 1, phoneNumber: 1 },
  {
    unique: true,
    partialFilterExpression: { phoneNumber: { $exists: true, $type: 'string' } },
  }
);