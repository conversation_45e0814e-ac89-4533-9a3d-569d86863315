# Custom Fields API Endpoints

## Base URL

All custom fields endpoints are prefixed with `/custom-fields`

## Authentication

All endpoints require authentication via <PERSON><PERSON> token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## Response Format

All responses follow a consistent format:

```json
{
  "status": "success|error",
  "code": 200,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Endpoints

### 1. Create Custom Field

Creates a new custom field for the authenticated user's workspace.

**Endpoint:** `POST /custom-fields`

**Request Body:**
```json
{
  "label": "Company Size",
  "type": "text",
  "options": ["Small", "Medium", "Large"],
  "showOnContact": true,
  "showOnChat": false
}
```

**Field Descriptions:**
- `label` (required): Display name for the custom field (1-100 characters)
- `type` (required): Field type - one of: `text`, `number`, `date`, `datetime`, `dropdown`, `bool`
- `options` (optional): Array of options for dropdown fields (required for dropdown type)
- `showOnContact` (optional): Whether to show field on contact forms (default: false)
- `showOnChat` (optional): Whether to show field in chat interface (default: false)

**Success Response (201):**
```json
{
  "status": "success",
  "code": 201,
  "message": "Custom field created successfully",
  "data": {
    "customField": {
      "id": "64f8b2c1a1b2c3d4e5f6g7h8",
      "label": "Company Size",
      "type": "text",
      "options": ["Small", "Medium", "Large"],
      "showOnContact": true,
      "showOnChat": false,
      "workspaceId": 123,
      "createdBy": "user123",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
- `400` - Validation error
- `401` - Unauthorized
- `409` - Duplicate label in workspace

### 2. Get All Custom Fields

Retrieves all custom fields for the authenticated user's workspace.

**Endpoint:** `GET /custom-fields`

**Query Parameters:** None

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Custom fields fetched successfully",
  "data": {
    "customFields": [
      {
        "id": "64f8b2c1a1b2c3d4e5f6g7h8",
        "label": "Company Size",
        "type": "text",
        "options": ["Small", "Medium", "Large"],
        "showOnContact": true,
        "showOnChat": false,
        "workspaceId": 123,
        "createdBy": "user123",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
- `401` - Unauthorized

### 3. Get Custom Field by ID

Retrieves a specific custom field by its ID.

**Endpoint:** `GET /custom-fields/:id`

**Path Parameters:**
- `id` (required): Custom field ID

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Custom field fetched successfully",
  "data": {
    "customField": {
      "id": "64f8b2c1a1b2c3d4e5f6g7h8",
      "label": "Company Size",
      "type": "text",
      "options": ["Small", "Medium", "Large"],
      "showOnContact": true,
      "showOnChat": false,
      "workspaceId": 123,
      "createdBy": "user123",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
- `401` - Unauthorized
- `404` - Custom field not found

### 4. Update Custom Field

Updates an existing custom field.

**Endpoint:** `PUT /custom-fields/:id`

**Path Parameters:**
- `id` (required): Custom field ID

**Request Body:**
```json
{
  "label": "Updated Company Size",
  "type": "dropdown",
  "options": ["Startup", "Small", "Medium", "Large", "Enterprise"],
  "showOnContact": true,
  "showOnChat": true
}
```

**Field Descriptions:**
- All fields are optional for updates
- `label`: New display name (1-100 characters)
- `type`: New field type
- `options`: New options array (required for dropdown type)
- `showOnContact`: New visibility setting
- `showOnChat`: New chat visibility setting

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Custom field updated successfully",
  "data": {
    "customField": {
      "id": "64f8b2c1a1b2c3d4e5f6g7h8",
      "label": "Updated Company Size",
      "type": "dropdown",
      "options": ["Startup", "Small", "Medium", "Large", "Enterprise"],
      "showOnContact": true,
      "showOnChat": true,
      "workspaceId": 123,
      "createdBy": "user123",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
- `400` - Validation error
- `401` - Unauthorized
- `404` - Custom field not found
- `409` - Duplicate label in workspace

### 5. Delete Custom Field

Deletes a custom field.

**Endpoint:** `DELETE /custom-fields/:id`

**Path Parameters:**
- `id` (required): Custom field ID

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Custom field deleted successfully",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
- `401` - Unauthorized
- `404` - Custom field not found

## Custom Field Types

### Text Field
```json
{
  "label": "Company Name",
  "type": "text",
  "showOnContact": true,
  "showOnChat": false
}
```

### Number Field
```json
{
  "label": "Employee Count",
  "type": "number",
  "showOnContact": true,
  "showOnChat": false
}
```

### Date Field
```json
{
  "label": "Birth Date",
  "type": "date",
  "showOnContact": true,
  "showOnChat": false
}
```

### DateTime Field
```json
{
  "label": "Last Contact",
  "type": "datetime",
  "showOnContact": true,
  "showOnChat": false
}
```

### Dropdown Field
```json
{
  "label": "Industry",
  "type": "dropdown",
  "options": ["Technology", "Healthcare", "Finance", "Education"],
  "showOnContact": true,
  "showOnChat": true
}
```

### Boolean Field
```json
{
  "label": "Is VIP Customer",
  "type": "bool",
  "showOnContact": true,
  "showOnChat": false
}
```

## Error Codes and Messages

### Validation Errors (400)

| Error | Message |
|-------|---------|
| Invalid label | Label must be at least 1 character long |
| Invalid type | Type must be one of: text, number, date, datetime, dropdown, bool |
| Missing options | At least one option is required for dropdown type |
| Invalid options | Options must be a non-empty array of strings |
| Duplicate options | Duplicate options are not allowed |

### Authentication Errors (401)

| Error | Message |
|-------|---------|
| Missing token | Authorization header is missing |
| Invalid token | Invalid or expired token |
| User context | User context not found in request |

### Not Found Errors (404)

| Error | Message |
|-------|---------|
| Custom field not found | Custom field not found |

### Conflict Errors (409)

| Error | Message |
|-------|---------|
| Duplicate label | A custom field with this label already exists in this workspace |

## Rate Limiting

- **Create/Update/Delete**: 100 requests per minute per user
- **Read operations**: 1000 requests per minute per user

## Examples

### Complete Workflow Example

1. **Create a text field:**
```bash
curl -X POST /custom-fields \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "label": "Company Name",
    "type": "text",
    "showOnContact": true,
    "showOnChat": false
  }'
```

2. **Create a dropdown field:**
```bash
curl -X POST /custom-fields \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "label": "Industry",
    "type": "dropdown",
    "options": ["Technology", "Healthcare", "Finance"],
    "showOnContact": true,
    "showOnChat": true
  }'
```

3. **Get all custom fields:**
```bash
curl -X GET /custom-fields \
  -H "Authorization: Bearer <token>"
```

4. **Update a custom field:**
```bash
curl -X PUT /custom-fields/64f8b2c1a1b2c3d4e5f6g7h8 \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "label": "Updated Industry",
    "options": ["Technology", "Healthcare", "Finance", "Education"]
  }'
```

5. **Delete a custom field:**
```bash
curl -X DELETE /custom-fields/64f8b2c1a1b2c3d4e5f6g7h8 \
  -H "Authorization: Bearer <token>"
```

## SDK Examples

### JavaScript/TypeScript

```typescript
class CustomFieldsAPI {
  constructor(private baseURL: string, private token: string) {}

  async createCustomField(data: CreateCustomFieldDto) {
    const response = await fetch(`${this.baseURL}/custom-fields`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }

  async getCustomFields() {
    const response = await fetch(`${this.baseURL}/custom-fields`, {
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });
    return response.json();
  }
}
```

### Python

```python
import requests

class CustomFieldsAPI:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def create_custom_field(self, data: dict):
        response = requests.post(
            f'{self.base_url}/custom-fields',
            json=data,
            headers=self.headers
        )
        return response.json()

    def get_custom_fields(self):
        response = requests.get(
            f'{self.base_url}/custom-fields',
            headers=self.headers
        )
        return response.json()
```
