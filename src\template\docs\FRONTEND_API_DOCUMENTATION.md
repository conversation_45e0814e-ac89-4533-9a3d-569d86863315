# Template API Documentation for Frontend Team

## Overview

This document provides comprehensive API documentation for the Template module, specifically designed for frontend developers. All endpoints use Meta's WhatsApp Business API format for maximum compatibility and efficiency.

## Base Configuration

```javascript
const API_BASE_URL = 'http://localhost:3000'; // Update for production
const TEMPLATE_ENDPOINTS = {
  BASE: '/templates',
  CREATE: '/templates',
  UPDATE: '/templates/:id',
  DELETE: '/templates/:id',
  GET_BY_ID: '/templates/:id',
  LIST: '/templates',
  AI_GENERATE: '/templates/ai',
  VOICE_GENERATE: '/templates/voice',
  META_SYNC: '/templates/meta/sync',
  META_LIST: '/templates/meta'
};

// Authentication header
const getAuthHeaders = (token) => ({
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
});
```

## 1. Create Template

### Endpoint
```
POST /templates
```

### Request Body
```typescript
interface CreateTemplateRequest {
  name: string;                    // Template name (required)
  description?: string;            // Template description (optional)
  category: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION'; // Template category (required)
  language: string;               // Language code (required, e.g., 'en', 'es')
  waba_id: string;               // WhatsApp Business Account ID (required)
  components: TemplateComponent[]; // Template components (required)
}

interface TemplateComponent {
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';
  format?: 'TEXT' | 'IMAGE' | 'VIDEO' | 'DOCUMENT' | 'LOCATION'; // For HEADER only
  text?: string;                  // Component text content
  example?: {
    header_text?: string[];       // For HEADER with variables
    body_text?: string[][];       // For BODY with variables (nested array)
  };
  buttons?: TemplateButton[];     // For BUTTONS component
}

interface TemplateButton {
  type: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER' | 'COPY_CODE' | 'OTP' | 'CATALOG' | 'MPM';
  text: string;
  url?: string;                   // For URL buttons
  phone_number?: string;          // For PHONE_NUMBER buttons
  otp_type?: 'COPY_CODE' | 'ONE_TAP'; // For OTP buttons
  // ... other button-specific fields
}
```

### Example Request
```javascript
const createTemplate = async (templateData, token) => {
  const response = await fetch(`${API_BASE_URL}/templates`, {
    method: 'POST',
    headers: getAuthHeaders(token),
    body: JSON.stringify({
      name: 'appointment_confirmation',
      description: 'Confirm customer appointments',
      category: 'UTILITY',
      language: 'en',
      waba_id: '****************',
      components: [
        {
          type: 'HEADER',
          format: 'TEXT',
          text: '{{1}}',
          example: {
            header_text: ['Appointment Confirmation']
          }
        },
        {
          type: 'BODY',
          text: 'Hi {{1}}, your appointment is confirmed for {{2}}. Please contact us if you need to reschedule.',
          example: {
            body_text: [['John', '2:00 PM']]
          }
        },
        {
          type: 'FOOTER',
          text: 'Thank you for choosing us!'
        }
      ]
    })
  });
  
  return await response.json();
};
```

### Response
```typescript
interface CreateTemplateResponse {
  status: 'success';
  code: 201;
  message: string;
  data: {
    template: {
      id: string;
      name: string;
      description: string;
      language: string;
      components: TemplateComponent[];
      meta_template_id: string;
      meta_template_status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'REJECTED';
      meta_template_category: string;
      workspace_id: number;
      created_by: string;
      is_active: boolean;
      ai_generated: boolean;
      waba_id: string;
      created_at: string;
      updated_at: string;
    };
  };
  timestamp: string;
}
```

## 2. Update Template

### Endpoint
```
PUT /templates/:id
```

### Request Body
```typescript
interface UpdateTemplateRequest {
  name?: string;
  description?: string;
  language?: string;
  components?: TemplateComponent[];
  category?: string;
  waba_id?: string;
}
```

### Example Request
```javascript
const updateTemplate = async (templateId, updateData, token) => {
  const response = await fetch(`${API_BASE_URL}/templates/${templateId}`, {
    method: 'PUT',
    headers: getAuthHeaders(token),
    body: JSON.stringify({
      name: 'updated_appointment_confirmation',
      components: [
        {
          type: 'HEADER',
          format: 'TEXT',
          text: '{{1}}',
          example: {
            header_text: ['Updated Appointment Confirmation']
          }
        },
        {
          type: 'BODY',
          text: 'Hi {{1}}, your appointment is confirmed for {{2}}. Please contact us if you need to reschedule.',
          example: {
            body_text: [['John', '2:00 PM']]
          }
        }
      ]
    })
  });
  
  return await response.json();
};
```

## 3. Get Template by ID

### Endpoint
```
GET /templates/:id
```

### Example Request
```javascript
const getTemplate = async (templateId, token) => {
  const response = await fetch(`${API_BASE_URL}/templates/${templateId}`, {
    method: 'GET',
    headers: getAuthHeaders(token)
  });
  
  return await response.json();
};
```

## 4. List Templates

### Endpoint
```
GET /templates
```

### Query Parameters
```typescript
interface TemplateQueryParams {
  page?: number;          // Page number (default: 1)
  limit?: number;         // Items per page (default: 10)
  search?: string;        // Search by name or description
  category?: string;      // Filter by category
  status?: string;        // Filter by Meta template status
  language?: string;      // Filter by language
  ai_generated?: boolean; // Filter by AI generation
  is_active?: boolean;    // Filter by active status
}
```

### Example Request
```javascript
const listTemplates = async (params = {}, token) => {
  const queryString = new URLSearchParams(params).toString();
  const response = await fetch(`${API_BASE_URL}/templates?${queryString}`, {
    method: 'GET',
    headers: getAuthHeaders(token)
  });
  
  return await response.json();
};

// Usage examples
const allTemplates = await listTemplates({}, token);
const marketingTemplates = await listTemplates({ category: 'MARKETING' }, token);
const approvedTemplates = await listTemplates({ status: 'APPROVED' }, token);
const aiGeneratedTemplates = await listTemplates({ ai_generated: true }, token);
```

### Response
```typescript
interface ListTemplatesResponse {
  status: 'success';
  code: 200;
  message: string;
  data: {
    templates: Template[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
  timestamp: string;
}
```

## 5. Delete Template

### Endpoint
```
DELETE /templates/:id?waba_id=:waba_id
```

### Example Request
```javascript
const deleteTemplate = async (templateId, wabaId, token) => {
  const response = await fetch(`${API_BASE_URL}/templates/${templateId}?waba_id=${wabaId}`, {
    method: 'DELETE',
    headers: getAuthHeaders(token)
  });
  
  return await response.json();
};
```

## 6. AI Template Generation

### Endpoint
```
POST /templates/ai
```

### Request Body
```typescript
interface CreateAiTemplateRequest {
  prompt: string;         // AI prompt for template generation (required)
  waba_id: string;       // WhatsApp Business Account ID (required)
  category?: string;      // Template category (optional)
  language?: string;      // Language code (optional, default: 'en')
}
```

### Example Request
```javascript
const generateAiTemplate = async (promptData, token) => {
  const response = await fetch(`${API_BASE_URL}/templates/ai`, {
    method: 'POST',
    headers: getAuthHeaders(token),
    body: JSON.stringify({
      prompt: 'Create a welcome message for new customers joining our e-commerce platform',
      waba_id: '****************',
      category: 'MARKETING',
      language: 'en'
    })
  });
  
  return await response.json();
};
```

## 7. Voice-to-Template Generation

### Endpoint
```
POST /templates/voice
```

### Request Body (FormData)
```typescript
// FormData with file and waba_id
const formData = new FormData();
formData.append('file', audioFile); // Audio file
formData.append('waba_id', '****************');
```

### Example Request
```javascript
const generateTemplateFromVoice = async (audioFile, wabaId, token) => {
  const formData = new FormData();
  formData.append('file', audioFile);
  formData.append('waba_id', wabaId);
  
  const response = await fetch(`${API_BASE_URL}/templates/voice`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
      // Don't set Content-Type for FormData
    },
    body: formData
  });
  
  return await response.json();
};
```

## 8. Meta Templates Sync

### Endpoint
```
POST /templates/meta/sync
```

### Request Body
```typescript
interface MetaSyncRequest {
  waba_id: string; // WhatsApp Business Account ID (required)
}
```

### Example Request
```javascript
const syncMetaTemplates = async (wabaId, token) => {
  const response = await fetch(`${API_BASE_URL}/templates/meta/sync`, {
    method: 'POST',
    headers: getAuthHeaders(token),
    body: JSON.stringify({
      waba_id: wabaId
    })
  });
  
  return await response.json();
};
```

## 9. Get Meta Templates

### Endpoint
```
GET /templates/meta
```

### Example Request
```javascript
const getMetaTemplates = async (token) => {
  const response = await fetch(`${API_BASE_URL}/templates/meta`, {
    method: 'GET',
    headers: getAuthHeaders(token)
  });
  
  return await response.json();
};
```

## 10. Sync Single Template with Meta

### Endpoint
```
POST /templates/:id/sync
```

### Example Request
```javascript
const syncSingleTemplate = async (templateId, token) => {
  const response = await fetch(`${API_BASE_URL}/templates/${templateId}/sync`, {
    method: 'POST',
    headers: getAuthHeaders(token)
  });
  
  return await response.json();
};
```

## Error Handling

### Common Error Responses
```typescript
interface ErrorResponse {
  status: 'error';
  code: number;
  message: string;
  error?: {
    details?: string;
    field?: string;
    code?: string;
  };
  timestamp: string;
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate template)
- `500` - Internal Server Error

### Example Error Handling
```javascript
const handleApiResponse = async (response) => {
  const data = await response.json();
  
  if (!response.ok) {
    switch (response.status) {
      case 400:
        console.error('Validation Error:', data.message);
        // Handle validation errors
        break;
      case 401:
        console.error('Authentication Error:', data.message);
        // Redirect to login
        break;
      case 404:
        console.error('Not Found:', data.message);
        // Show not found message
        break;
      case 409:
        console.error('Conflict:', data.message);
        // Handle duplicate template
        break;
      default:
        console.error('Server Error:', data.message);
        // Show generic error
    }
    throw new Error(data.message);
  }
  
  return data;
};
```

## Component Examples

### Text Template with Variables
```javascript
const textTemplate = {
  name: 'order_confirmation',
  description: 'Confirm customer orders',
  category: 'UTILITY',
  language: 'en',
  waba_id: '****************',
  components: [
    {
      type: 'HEADER',
      format: 'TEXT',
      text: '{{1}}',
      example: {
        header_text: ['Order Confirmation']
      }
    },
    {
      type: 'BODY',
      text: 'Hi {{1}}, your order #{{2}} has been confirmed and will be delivered on {{3}}.',
      example: {
        body_text: [['John', 'ORD-12345', '2024-01-15']]
      }
    },
    {
      type: 'FOOTER',
      text: 'Thank you for shopping with us!'
    }
  ]
};
```

### Interactive Template with Buttons
```javascript
const interactiveTemplate = {
  name: 'product_catalog',
  description: 'Show product catalog',
  category: 'MARKETING',
  language: 'en',
  waba_id: '****************',
  components: [
    {
      type: 'HEADER',
      format: 'TEXT',
      text: '{{1}}',
      example: {
        header_text: ['New Products Available']
      }
    },
    {
      type: 'BODY',
      text: 'Check out our latest products! {{1}}',
      example: {
        body_text: [['Click the buttons below to explore']]
      }
    },
    {
      type: 'BUTTONS',
      buttons: [
        {
          type: 'QUICK_REPLY',
          text: 'View Products'
        },
        {
          type: 'URL',
          text: 'Visit Website',
          url: 'https://example.com'
        }
      ]
    }
  ]
};
```

### Image Template
```javascript
const imageTemplate = {
  name: 'product_announcement',
  description: 'Announce new product with image',
  category: 'MARKETING',
  language: 'en',
  waba_id: '****************',
  components: [
    {
      type: 'HEADER',
      format: 'IMAGE',
      text: '{{1}}',
      example: {
        header_text: ['New Product Launch']
      }
    },
    {
      type: 'BODY',
      text: 'Introducing our latest product: {{1}}! Available now for just {{2}}.',
      example: {
        body_text: [['Smartphone X', '$599']]
      }
    },
    {
      type: 'FOOTER',
      text: 'Limited time offer!'
    }
  ]
};
```

## React/JavaScript Helper Functions

```javascript
// Template validation helper
const validateTemplate = (template) => {
  const errors = [];
  
  if (!template.name || template.name.trim() === '') {
    errors.push('Template name is required');
  }
  
  if (!template.category) {
    errors.push('Template category is required');
  }
  
  if (!template.language) {
    errors.push('Template language is required');
  }
  
  if (!template.waba_id) {
    errors.push('WABA ID is required');
  }
  
  if (!template.components || template.components.length === 0) {
    errors.push('At least one component is required');
  }
  
  // Validate components
  template.components?.forEach((component, index) => {
    if (!component.type) {
      errors.push(`Component ${index + 1} must have a type`);
    }
    
    if (component.type === 'HEADER' && !component.format) {
      errors.push(`Header component ${index + 1} must have a format`);
    }
  });
  
  return errors;
};

// Template creation helper
const createTemplateWithValidation = async (templateData, token) => {
  const errors = validateTemplate(templateData);
  
  if (errors.length > 0) {
    throw new Error(`Validation failed: ${errors.join(', ')}`);
  }
  
  return await createTemplate(templateData, token);
};

// Component builder helpers
const createHeaderComponent = (text, hasVariables = false, exampleValue = '') => {
  const component = {
    type: 'HEADER',
    format: 'TEXT',
    text: text
  };
  
  if (hasVariables && exampleValue) {
    component.example = {
      header_text: [exampleValue]
    };
  }
  
  return component;
};

const createBodyComponent = (text, hasVariables = false, exampleValues = []) => {
  const component = {
    type: 'BODY',
    text: text
  };
  
  if (hasVariables && exampleValues.length > 0) {
    component.example = {
      body_text: [exampleValues]
    };
  }
  
  return component;
};

const createFooterComponent = (text) => ({
  type: 'FOOTER',
  text: text
});

const createButtonsComponent = (buttons) => ({
  type: 'BUTTONS',
  buttons: buttons
});

// Usage example
const buildWelcomeTemplate = () => ({
  name: 'welcome_new_customer',
  description: 'Welcome new customers',
  category: 'MARKETING',
  language: 'en',
  waba_id: '****************',
  components: [
    createHeaderComponent('Welcome to {{1}}!', true, 'Our Store'),
    createBodyComponent('Hi {{1}}, welcome to our platform! We\'re excited to have you.', true, ['John']),
    createFooterComponent('Start shopping now!'),
    createButtonsComponent([
      { type: 'QUICK_REPLY', text: 'Browse Products' },
      { type: 'URL', text: 'Visit Store', url: 'https://example.com' }
    ])
  ]
});
```

## Testing

### Postman Collection
Import the following collection for API testing:

```json
{
  "info": {
    "name": "Template API",
    "description": "Template management API endpoints"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:3000"
    },
    {
      "key": "token",
      "value": "your_jwt_token_here"
    }
  ],
  "item": [
    {
      "name": "Create Template",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{token}}"
          },
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"name\": \"test_template\",\n  \"description\": \"Test template\",\n  \"category\": \"UTILITY\",\n  \"language\": \"en\",\n  \"waba_id\": \"****************\",\n  \"components\": [\n    {\n      \"type\": \"BODY\",\n      \"text\": \"Hello {{1}}, this is a test message.\",\n      \"example\": {\n        \"body_text\": [[\"World\"]]\n      }\n    }\n  ]\n}"
        },
        "url": {
          "raw": "{{baseUrl}}/templates",
          "host": ["{{baseUrl}}"],
          "path": ["templates"]
        }
      }
    }
  ]
}
```

This documentation provides everything the frontend team needs to integrate with the Template API effectively.
