/**
 * Constants for workspace module
 */
export const WOR<PERSON><PERSON>CE_CONSTANTS = {
  // Default values
  DEFAULTS: {
    LANGUAGE: 'en',
    TIME_ZONE: 'Asia/Kolkata',
    TRIAL_DAYS: 14,
    STATUS: 'active',
    <PERSON><PERSON><PERSON>: 'Admin',
  },

  // Error messages
  ERROR_MESSAGES: {
    WORKSPACE_CREATION_FAILED: 'Workspace creation failed. Please try again.',
    WORKSPACE_NOT_FOUND: 'Workspace not found',
    USER_PROFILE_NOT_FOUND: 'User profile not found',
    USER_ALREADY_HAS_WORKSPACE: 'User already has a workspace',
    WORKSPACE_UPDATE_FAILED: 'Workspace update failed. Please try again.',
    MEMBER_ADDITION_FAILED: 'Failed to add member to workspace',
    MEMBER_ALREADY_EXISTS: 'User is already a member of this workspace',
    USER_ALREADY_EXISTS: 'User with this email already exists',
    MEMBERS_FETCH_FAILED: 'Failed to fetch workspace members',
    NO_WORKSPACE_ASSOCIATED: 'You are not associated with any workspace',
    R<PERSON><PERSON>_ID_REQUIRED: 'Role ID is required when waba_access is true',
    WABA_ACCESS_SETUP_FAILED: 'Failed to setup waba access',
    USER_ACCOUNT_CREATION_FAILED: 'Failed to create user account',
    USER_ID_NOT_FOUND: 'Failed to get user ID from auth creation',
    USER_PROFILE_CREATION_FAILED: 'Failed to create user profile',
    WORKSPACE_MEMBER_CREATION_FAILED: 'Failed to add member to workspace',
    AUTOMATE_MEMBER_CREATION_FAILED: 'Failed to add user to automate whatsapp members',
    ADMIN_ROLE_CREATION_FAILED: 'Failed to create admin role',
    CRITICAL_ERROR: 'Critical error: Workspace created but profile update failed and rollback also failed. Please contact support immediately.',
    ROLLBACK_FAILED: 'Failed to create workspace due to user profile update error. Please try again.',
    MEMBER_ROLLBACK_FAILED: 'Failed to create workspace due to member creation error. Please try again.',
    ROLE_ROLLBACK_FAILED: 'Failed to create workspace due to role creation error. Please try again.',
    AUTOMATE_ROLLBACK_FAILED: 'Failed to create workspace due to automate member creation error. Please try again.',
  },

  // Success messages
  SUCCESS_MESSAGES: {
    WORKSPACE_CREATED: 'Workspace created successfully with user as admin',
    MEMBER_ADDED: 'User registered and added to workspace successfully',
    MEMBERS_FETCHED: 'Workspace members retrieved successfully',
    WORKSPACE_UPDATED: 'Workspace updated successfully',
    MEMBER_UPDATED: 'Workspace member updated successfully',
    MEMBER_REMOVED: 'Workspace member removed successfully',
  },

  // HTTP status codes
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    NOT_FOUND: 404,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
  },

  // Pagination defaults
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100,
  },

  // Member roles
  MEMBER_ROLES: {
    ADMIN: 'Admin',
    MANAGER: 'Manager',
    USER: 'User',
  },

  // Member status
  MEMBER_STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    PENDING: 'pending',
  },
} as const;

/**
 * Type for workspace constants
 */
export type WorkspaceConstants = typeof WORKSPACE_CONSTANTS;

