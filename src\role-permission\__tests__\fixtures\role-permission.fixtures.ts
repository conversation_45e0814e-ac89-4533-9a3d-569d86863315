/**
 * Test fixtures for Role-Permission module
 */

export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  name: 'Test User'
};

export const mockUserProfile = {
  id: 'user-123',
  workspace_id: 1,
  email: '<EMAIL>',
  name: 'Test User',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

export const mockRole = {
  id: 'role-123',
  name: 'Admin',
  description: 'Administrator role with full access',
  status: 'active',
  workspace_id: 1,
  created_by: 'user-123',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

export const mockPermission = {
  id: 'permission-123',
  name: 'manage_users',
  resource: 'users',
  action: 'manage',
  description: 'Manage user accounts',
  status: 'active',
  workspace_id: 1,
  created_by: 'user-123',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

export const mockRolePermission = {
  id: 'role-permission-123',
  role_id: 'role-123',
  permission_id: 'permission-123',
  workspace_id: 1,
  assigned_by: 'user-123',
  assigned_at: '2023-01-01T00:00:00Z'
};

export const mockCreateRoleDto = {
  name: 'Content Manager',
  description: 'Manages content and templates',
  status: 'active' as const
};

export const mockUpdateRoleDto = {
  name: 'Updated Content Manager',
  description: 'Updated description'
};

export const mockCreatePermissionDto = {
  name: 'create_templates',
  resource: 'templates',
  action: 'create',
  description: 'Create message templates',
  status: 'active' as const
};

export const mockUpdatePermissionDto = {
  name: 'updated_create_templates',
  description: 'Updated description'
};

export const mockAssignPermissionDto = {
  roleId: 'role-123',
  permissionId: 'permission-123'
};

export const mockRolePermissionQueryDto = {
  page: 1,
  limit: 10,
  status: 'active' as const
};

export const mockRolesList = [
  mockRole,
  {
    id: 'role-456',
    name: 'Content Manager',
    description: 'Manages content and templates',
    status: 'active',
    workspace_id: 1,
    created_by: 'user-123',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  }
];

export const mockPermissionsList = [
  mockPermission,
  {
    id: 'permission-456',
    name: 'create_templates',
    resource: 'templates',
    action: 'create',
    description: 'Create message templates',
    status: 'active',
    workspace_id: 1,
    created_by: 'user-123',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  }
];

export const mockRolePermissionsList = [
  {
    ...mockRolePermission,
    permissions: mockPermission
  }
];

export const mockPagination = {
  page: 1,
  limit: 10,
  total: 2,
  totalPages: 1
};

export const mockRequest = {
  user: mockUser,
  headers: {
    authorization: 'Bearer mock-jwt-token'
  }
};

export const mockSupabaseError = {
  message: 'Database error',
  code: 'PGRST001',
  details: 'Connection failed'
};

export const mockValidationError = {
  message: 'Validation failed',
  details: ['Name is required', 'Status must be active or inactive']
};
