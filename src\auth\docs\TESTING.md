# 🧪 Auth Module Testing Guide

## 📋 Overview
This document covers testing strategies, test cases, and best practices for the auth module.

## 🎯 Testing Strategy

### **1. Testing Pyramid**
```
    ┌─────────────┐
    │   E2E Tests │  ← Few, High-level
    └─────────────┘
   ┌─────────────────┐
   │ Integration     │  ← Some, Medium-level
   │ Tests           │
   └─────────────────┘
  ┌─────────────────────┐
  │ Unit Tests          │  ← Many, Low-level
  └─────────────────────┘
```

### **2. Test Categories**
- **Unit Tests**: Individual functions and methods
- **Integration Tests**: Component interactions
- **E2E Tests**: Complete user workflows
- **Contract Tests**: API contract validation

## 🛠️ Test Setup

### **1. Testing Dependencies**
```json
{
  "devDependencies": {
    "@nestjs/testing": "^10.0.0",
    "@nestjs/jest": "^10.0.0",
    "jest": "^29.0.0",
    "supertest": "^6.0.0",
    "ts-jest": "^29.0.0"
  }
}
```

### **2. Jest Configuration**
```javascript
// jest.config.js
module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: 'src',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    '**/*.(t|j)s',
    '!**/*.spec.ts',
    '!**/*.interface.ts',
    '!**/node_modules/**',
  ],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  moduleNameMapping: {
    '^src/(.*)$': '<rootDir>/$1',
  },
};
```

### **3. Test Database Setup**
```typescript
// test-database.setup.ts
import { Test } from '@nestjs/testing';
import { SupabaseService } from '../supabase/supabase.service';

export const createTestModule = async () => {
  const module = await Test.createTestingModule({
    providers: [
      {
        provide: SupabaseService,
        useValue: {
          signUp: jest.fn(),
          signIn: jest.fn(),
          getUser: jest.fn(),
          // ... other methods
        },
      },
    ],
  }).compile();

  return module;
};
```

## 🔧 Unit Tests

### **1. AuthService Tests**

#### **SignUp Tests**
```typescript
// auth.service.spec.ts
describe('AuthService - signUp', () => {
  let service: AuthService;
  let supabaseService: jest.Mocked<SupabaseService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: SupabaseService,
          useValue: {
            getUserProfileByEmail: jest.fn(),
            signUp: jest.fn(),
            insertUserProfile: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    supabaseService = module.get(SupabaseService);
  });

  it('should register a new user successfully', async () => {
    // Arrange
    const signUpDto: SignUpDto = {
      email: '<EMAIL>',
      password: 'Password123',
      first_name: 'John',
      last_name: 'Doe',
      phoneNumber: '**********',
      countrycode: '+1',
      country: 'USA',
      terms_conditions: true,
    };

    supabaseService.getUserProfileByEmail.mockResolvedValue(null);
    supabaseService.signUp.mockResolvedValue({
      data: { user: mockUser, session: mockSession },
      error: null,
    });
    supabaseService.insertUserProfile.mockResolvedValue({
      data: {},
      error: null,
    });

    // Act
    const result = await service.signUp(signUpDto);

    // Assert
    expect(result).toHaveProperty('user');
    expect(result).toHaveProperty('access_token');
    expect(supabaseService.signUp).toHaveBeenCalledWith(
      signUpDto.email,
      signUpDto.password,
      expect.objectContaining({
        first_name: signUpDto.first_name,
        last_name: signUpDto.last_name,
      })
    );
  });

  it('should throw ConflictException if user already exists', async () => {
    // Arrange
    const signUpDto: SignUpDto = {
      email: '<EMAIL>',
      password: 'Password123',
      first_name: 'John',
      last_name: 'Doe',
      phoneNumber: '**********',
      countrycode: '+1',
      country: 'USA',
      terms_conditions: true,
    };

    supabaseService.getUserProfileByEmail.mockResolvedValue(mockUser);

    // Act & Assert
    await expect(service.signUp(signUpDto)).rejects.toThrow(ConflictException);
  });

  it('should handle Supabase signup errors', async () => {
    // Arrange
    const signUpDto: SignUpDto = {
      email: '<EMAIL>',
      password: 'Password123',
      first_name: 'John',
      last_name: 'Doe',
      phoneNumber: '**********',
      countrycode: '+1',
      country: 'USA',
      terms_conditions: true,
    };

    supabaseService.getUserProfileByEmail.mockResolvedValue(null);
    supabaseService.signUp.mockResolvedValue({
      data: null,
      error: { message: 'Email already registered' },
    });

    // Act & Assert
    await expect(service.signUp(signUpDto)).rejects.toThrow(BadRequestException);
  });
});
```

#### **SignIn Tests**
```typescript
describe('AuthService - signIn', () => {
  it('should authenticate user successfully', async () => {
    // Arrange
    const signInDto: SignInDto = {
      email: '<EMAIL>',
      password: 'Password123',
    };

    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    supabaseService.signIn.mockResolvedValue({
      data: { user: mockUser, session: mockSession },
      error: null,
    });
    supabaseService.getUserProfileByEmail.mockResolvedValue(mockUserProfile);

    // Act
    await service.signIn(signInDto, mockRes);

    // Assert
    expect(mockRes.status).toHaveBeenCalledWith(200);
    expect(mockRes.json).toHaveBeenCalledWith(
      expect.objectContaining({
        status: 'success',
        message: 'Sign in successful',
        data: expect.objectContaining({
          access_token: expect.any(String),
          refresh_token: expect.any(String),
        }),
      })
    );
  });

  it('should throw UnauthorizedException for invalid credentials', async () => {
    // Arrange
    const signInDto: SignInDto = {
      email: '<EMAIL>',
      password: 'WrongPassword',
    };

    supabaseService.signIn.mockResolvedValue({
      data: null,
      error: { message: 'Invalid credentials' },
    });

    // Act & Assert
    await expect(service.signIn(signInDto, mockRes)).rejects.toThrow(
      UnauthorizedException
    );
  });
});
```

### **2. AuthController Tests**

```typescript
// auth.controller.spec.ts
describe('AuthController', () => {
  let controller: AuthController;
  let service: jest.Mocked<AuthService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: {
            signUp: jest.fn(),
            signIn: jest.fn(),
            signOut: jest.fn(),
            getProfile: jest.fn(),
            updateProfile: jest.fn(),
            changePassword: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    service = module.get(AuthService);
  });

  it('should call service signUp method', async () => {
    // Arrange
    const signUpDto: SignUpDto = {
      email: '<EMAIL>',
      password: 'Password123',
      first_name: 'John',
      last_name: 'Doe',
      phoneNumber: '**********',
      countrycode: '+1',
      country: 'USA',
      terms_conditions: true,
    };

    service.signUp.mockResolvedValue(mockSignUpResponse);

    // Act
    const result = await controller.signUp(signUpDto);

    // Assert
    expect(service.signUp).toHaveBeenCalledWith(signUpDto);
    expect(result).toEqual(mockSignUpResponse);
  });
});
```

### **3. AuthGuard Tests**

```typescript
// auth.guard.spec.ts
describe('AuthGuard', () => {
  let guard: AuthGuard;
  let service: jest.Mocked<AuthService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        AuthGuard,
        {
          provide: AuthService,
          useValue: {
            validateToken: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<AuthGuard>(AuthGuard);
    service = module.get(AuthService);
  });

  it('should allow access with valid token', async () => {
    // Arrange
    const mockRequest = {
      headers: {
        authorization: 'Bearer valid-token',
      },
    };

    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
    } as ExecutionContext;

    service.validateToken.mockResolvedValue(mockUser);

    // Act
    const result = await guard.canActivate(mockContext);

    // Assert
    expect(result).toBe(true);
    expect(mockRequest.user).toEqual(mockUser);
  });

  it('should deny access with invalid token', async () => {
    // Arrange
    const mockRequest = {
      headers: {
        authorization: 'Bearer invalid-token',
      },
    };

    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
    } as ExecutionContext;

    service.validateToken.mockRejectedValue(new UnauthorizedException());

    // Act & Assert
    await expect(guard.canActivate(mockContext)).rejects.toThrow(
      UnauthorizedException
    );
  });

  it('should deny access without token', async () => {
    // Arrange
    const mockRequest = {
      headers: {},
    };

    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => mockRequest,
      }),
    } as ExecutionContext;

    // Act & Assert
    await expect(guard.canActivate(mockContext)).rejects.toThrow(
      UnauthorizedException
    );
  });
});
```

## 🔗 Integration Tests

### **1. End-to-End Authentication Flow**

```typescript
// auth.e2e-spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../app.module';

describe('Auth (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/auth/signup (POST)', () => {
    it('should register a new user', () => {
      return request(app.getHttpServer())
        .post('/auth/signup')
        .send({
          email: '<EMAIL>',
          password: 'Password123',
          first_name: 'John',
          last_name: 'Doe',
          phoneNumber: '**********',
          countrycode: '+1',
          country: 'USA',
          terms_conditions: true,
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'success');
          expect(res.body).toHaveProperty('data');
          expect(res.body.data).toHaveProperty('access_token');
        });
    });

    it('should reject invalid email', () => {
      return request(app.getHttpServer())
        .post('/auth/signup')
        .send({
          email: 'invalid-email',
          password: 'Password123',
          first_name: 'John',
          last_name: 'Doe',
          phoneNumber: '**********',
          countrycode: '+1',
          country: 'USA',
          terms_conditions: true,
        })
        .expect(400);
    });
  });

  describe('/auth/signin (POST)', () => {
    it('should authenticate user', async () => {
      // First register a user
      await request(app.getHttpServer())
        .post('/auth/signup')
        .send({
          email: '<EMAIL>',
          password: 'Password123',
          first_name: 'John',
          last_name: 'Doe',
          phoneNumber: '**********',
          countrycode: '+1',
          country: 'USA',
          terms_conditions: true,
        });

      // Then sign in
      return request(app.getHttpServer())
        .post('/auth/signin')
        .send({
          email: '<EMAIL>',
          password: 'Password123',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'success');
          expect(res.body.data).toHaveProperty('access_token');
        });
    });
  });

  describe('/auth/profile (GET)', () => {
    it('should get user profile with valid token', async () => {
      // Register and sign in
      const signupResponse = await request(app.getHttpServer())
        .post('/auth/signup')
        .send({
          email: '<EMAIL>',
          password: 'Password123',
          first_name: 'John',
          last_name: 'Doe',
          phoneNumber: '**********',
          countrycode: '+1',
          country: 'USA',
          terms_conditions: true,
        });

      const token = signupResponse.body.data.access_token;

      return request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'success');
          expect(res.body.data).toHaveProperty('user_profile');
        });
    });

    it('should reject request without token', () => {
      return request(app.getHttpServer())
        .get('/auth/profile')
        .expect(401);
    });
  });
});
```

## 📊 Test Coverage

### **1. Coverage Configuration**
```javascript
// jest.config.js
module.exports = {
  collectCoverageFrom: [
    '**/*.(t|j)s',
    '!**/*.spec.ts',
    '!**/*.interface.ts',
    '!**/node_modules/**',
    '!**/dist/**',
  ],
  coverageDirectory: '../coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### **2. Coverage Reports**
```bash
# Generate coverage report
npm run test:cov

# View HTML coverage report
open coverage/lcov-report/index.html
```

## 🚀 Test Execution

### **1. Running Tests**
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:cov

# Run specific test file
npm test auth.service.spec.ts

# Run tests matching pattern
npm test -- --testNamePattern="signUp"
```

### **2. Test Scripts**
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:cov": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "test:e2e": "jest --config ./test/jest-e2e.json"
  }
}
```

## 🧪 Test Data Management

### **1. Test Fixtures**
```typescript
// test/fixtures/auth.fixtures.ts
export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  first_name: 'John',
  last_name: 'Doe',
  workspace_id: 1,
};

export const mockSession = {
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  expires_in: 3600,
};

export const mockSignUpDto: SignUpDto = {
  email: '<EMAIL>',
  password: 'Password123',
  first_name: 'John',
  last_name: 'Doe',
  phoneNumber: '**********',
  countrycode: '+1',
  country: 'USA',
  terms_conditions: true,
};
```

### **2. Test Database**
```typescript
// test/database/test-database.ts
import { createClient } from '@supabase/supabase-js';

export const createTestSupabaseClient = () => {
  return createClient(
    process.env.SUPABASE_TEST_URL,
    process.env.SUPABASE_TEST_ANON_KEY
  );
};

export const cleanupTestData = async () => {
  const supabase = createTestSupabaseClient();
  await supabase.from('user_profile').delete().neq('id', '');
};
```

## 🔍 Test Best Practices

### **1. Test Structure (AAA Pattern)**
```typescript
it('should do something', async () => {
  // Arrange - Set up test data and mocks
  const input = { ... };
  const expectedOutput = { ... };
  mockService.method.mockResolvedValue(expectedOutput);

  // Act - Execute the method being tested
  const result = await service.method(input);

  // Assert - Verify the results
  expect(result).toEqual(expectedOutput);
  expect(mockService.method).toHaveBeenCalledWith(input);
});
```

### **2. Test Naming**
```typescript
// Good test names
describe('AuthService', () => {
  describe('signUp', () => {
    it('should register a new user successfully');
    it('should throw ConflictException if user already exists');
    it('should handle Supabase signup errors');
  });
});
```

### **3. Mock Management**
```typescript
beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  jest.restoreAllMocks();
});
```

## 📈 Performance Testing

### **1. Load Testing**
```typescript
// performance/auth.load-test.ts
import { performance } from 'perf_hooks';

describe('Auth Performance', () => {
  it('should handle concurrent signup requests', async () => {
    const start = performance.now();
    
    const promises = Array.from({ length: 100 }, (_, i) =>
      request(app.getHttpServer())
        .post('/auth/signup')
        .send({
          email: `test${i}@example.com`,
          password: 'Password123',
          first_name: 'John',
          last_name: 'Doe',
          phoneNumber: '**********',
          countrycode: '+1',
          country: 'USA',
          terms_conditions: true,
        })
    );

    await Promise.all(promises);
    
    const end = performance.now();
    const duration = end - start;
    
    expect(duration).toBeLessThan(5000); // Should complete in under 5 seconds
  });
});
```
