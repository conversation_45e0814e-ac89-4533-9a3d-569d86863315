# Role-Permission Module Documentation

## Overview

The Role-Permission module provides comprehensive role-based access control (RBAC) functionality for the WhatsApp automation platform. It manages roles, permissions, and their assignments within workspace contexts, ensuring secure and granular access control.

## Table of Contents

1. [Architecture](#architecture)
2. [API Endpoints](#api-endpoints)
3. [Data Models](#data-models)
4. [Security](#security)
5. [Usage Examples](#usage-examples)
6. [Testing](#testing)
7. [Deployment](#deployment)

## Architecture

### Module Structure

```
role-permission/
├── dto/                          # Data Transfer Objects
│   ├── create-role.dto.ts
│   ├── update-role.dto.ts
│   ├── create-permission.dto.ts
│   ├── update-permission.dto.ts
│   ├── assign-permission.dto.ts
│   ├── role-permission-query.dto.ts
│   └── index.ts
├── utils/                        # Utility Classes
│   ├── role-permission-constants.util.ts
│   ├── role-permission-validation.util.ts
│   └── role-permission-response.util.ts
├── docs/                         # Documentation
│   └── INDEX.md
├── role-permission.controller.ts  # Controller
├── role-permission.service.ts     # Service
├── role-permission.module.ts      # Module
└── README.md                      # Module Summary
```

### Key Components

1. **Controller**: Handles HTTP requests and responses
2. **Service**: Contains business logic and data operations
3. **DTOs**: Validate and structure incoming data
4. **Utilities**: Provide reusable validation, constants, and response formatting
5. **Constants**: Centralized configuration and messages

### Design Patterns

- **Repository Pattern**: Data access abstraction through Supabase
- **DTO Pattern**: Data validation and transformation
- **Utility Pattern**: Reusable business logic
- **Response Standardization**: Consistent API responses
- **Error Handling**: Comprehensive error management

## API Endpoints

### Role Management

#### Create Role
```http
POST /role-permission/roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Admin",
  "description": "Administrator role with full access",
  "status": "active"
}
```

#### Get All Roles
```http
GET /role-permission/roles?page=1&limit=10&status=active
Authorization: Bearer <token>
```

#### Get Role by ID
```http
GET /role-permission/roles/{id}
Authorization: Bearer <token>
```

#### Update Role
```http
PUT /role-permission/roles/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Updated Admin",
  "description": "Updated description"
}
```

#### Delete Role
```http
DELETE /role-permission/roles/{id}
Authorization: Bearer <token>
```

### Permission Management

#### Create Permission
```http
POST /role-permission/permissions
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "manage_users",
  "resource": "users",
  "action": "manage",
  "description": "Manage user accounts"
}
```

#### Get All Permissions
```http
GET /role-permission/permissions?page=1&limit=10&status=active
Authorization: Bearer <token>
```

#### Get Permission by ID
```http
GET /role-permission/permissions/{id}
Authorization: Bearer <token>
```

#### Update Permission
```http
PUT /role-permission/permissions/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "updated_manage_users",
  "description": "Updated description"
}
```

#### Delete Permission
```http
DELETE /role-permission/permissions/{id}
Authorization: Bearer <token>
```

### Role-Permission Assignment

#### Assign Permission to Role
```http
POST /role-permission/assign
Authorization: Bearer <token>
Content-Type: application/json

{
  "roleId": "role-uuid",
  "permissionId": "permission-uuid"
}
```

#### Revoke Permission from Role
```http
POST /role-permission/revoke
Authorization: Bearer <token>
Content-Type: application/json

{
  "roleId": "role-uuid",
  "permissionId": "permission-uuid"
}
```

#### Get Role Permissions
```http
GET /role-permission/roles/{id}/permissions
Authorization: Bearer <token>
```

## Data Models

### Role
```typescript
interface Role {
  id: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  workspace_id: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}
```

### Permission
```typescript
interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
  status: 'active' | 'inactive';
  workspace_id: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}
```

### Role-Permission Assignment
```typescript
interface RolePermission {
  id: string;
  role_id: string;
  permission_id: string;
  workspace_id: number;
  assigned_by: string;
  assigned_at: string;
}
```

## Security

### Authentication & Authorization
- All endpoints require valid JWT authentication
- Workspace-scoped access control
- User context validation for all operations

### Input Validation
- Comprehensive DTO validation using class-validator
- Business logic validation in utility classes
- SQL injection prevention through parameterized queries

### Data Protection
- Sensitive data encryption in transit
- Access token security for external API calls
- Audit logging for all operations

### Security Best Practices
- Principle of least privilege
- Role-based access control
- Input sanitization and validation
- Error message sanitization

## Usage Examples

### Creating a Complete Role-Permission Setup

```typescript
// 1. Create a role
const role = await rolePermissionService.createRole({
  name: 'Content Manager',
  description: 'Manages content and templates',
  status: 'active'
}, req);

// 2. Create permissions
const permissions = await Promise.all([
  rolePermissionService.createPermission({
    name: 'create_templates',
    resource: 'templates',
    action: 'create',
    description: 'Create message templates'
  }, req),
  rolePermissionService.createPermission({
    name: 'manage_templates',
    resource: 'templates',
    action: 'manage',
    description: 'Full template management'
  }, req)
]);

// 3. Assign permissions to role
await Promise.all([
  rolePermissionService.assignPermissionToRole({
    roleId: role.data.role.id,
    permissionId: permissions[0].data.permission.id
  }, req),
  rolePermissionService.assignPermissionToRole({
    roleId: role.data.role.id,
    permissionId: permissions[1].data.permission.id
  }, req)
]);
```

### Querying with Filters

```typescript
// Get active roles with pagination
const activeRoles = await rolePermissionService.getRolesForWorkspace(req, {
  page: 1,
  limit: 20,
  status: 'active'
});

// Get permissions for a specific resource
const templatePermissions = await rolePermissionService.getPermissionsForWorkspace(req, {
  page: 1,
  limit: 50
});
```

## Testing

### Unit Tests
- Service method testing with mocked dependencies
- Validation utility testing
- Response utility testing
- Error handling testing

### Integration Tests
- End-to-end API testing
- Database interaction testing
- Authentication flow testing

### Test Coverage
- Minimum 80% code coverage
- All critical paths tested
- Error scenarios covered

## Deployment

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...

# Authentication
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Logging
LOG_LEVEL=info
```

### Database Setup
1. Ensure Supabase tables exist:
   - `roles`
   - `permissions`
   - `role_permissions`
   - `user_roles`

2. Set up proper indexes for performance
3. Configure row-level security policies

### Monitoring
- Application performance monitoring
- Error tracking and alerting
- Database performance monitoring
- Security event logging

### Scaling Considerations
- Database connection pooling
- Caching for frequently accessed data
- Rate limiting for API endpoints
- Load balancing for high availability
