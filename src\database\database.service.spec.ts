import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { getConnectionToken } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { DatabaseService } from './database.service';

describe('DatabaseService', () => {
  let service: DatabaseService;

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockConnection = {
    on: jest.fn(),
    db: {
      admin: jest.fn().mockReturnValue({
        ping: jest.fn().mockResolvedValue(true),
        serverInfo: jest.fn().mockResolvedValue({ version: '6.0.0' }),
      }),
      stats: jest.fn().mockResolvedValue({
        collections: 5,
        objects: 100,
        dataSize: 1024,
        storageSize: 2048,
      }),
      databaseName: 'test-db',
    },
    readyState: 1,
    close: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DatabaseService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: getConnectionToken(),
          useValue: mockConnection,
        },
      ],
    }).compile();

    service = module.get<DatabaseService>(DatabaseService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should check database connection', async () => {
    const isConnected = await service.isDatabaseConnected();
    expect(isConnected).toBe(true);
  });

  it('should get connection status', () => {
    const status = service.getConnectionStatus();
    expect(status).toHaveProperty('connected');
    expect(status).toHaveProperty('readyState');
  });

  it('should get database info', async () => {
    const info = await service.getDatabaseInfo();
    expect(info).toHaveProperty('server');
    expect(info).toHaveProperty('database');
    expect(info).toHaveProperty('collections');
  });
}); 