import { Injectable, BadRequestException, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';
import { CreateRoleDto, UpdateRoleDto, CreatePermissionDto, UpdatePermissionDto, AssignPermissionDto, RolePermissionQueryDto } from './dto';
import { RolePermissionResponseUtil } from './utils/role-permission-response.util';
import { RolePermissionValidationUtil } from './utils/role-permission-validation.util';
import { ROLE_PERMISSION_CONSTANTS } from './utils/role-permission-constants.util';

/**
 * Refactored RolePermissionService with improved structure and consistent response handling
 */
@Injectable()
export class RolePermissionService {
  private readonly logger = new Logger(RolePermissionService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
  ) {}

  // ==================== ROLE METHODS ====================

  /**
   * Create role
   */
  async createRole(createDto: CreateRoleDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting role creation process');

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Validate and prepare data
      const payload = RolePermissionValidationUtil.validateRoleCreationData(createDto, user.id, workspaceId);

      // Check for duplicate name in workspace
      const { data: existing, error: existingError } = await this.supabaseService.getClient()
        .from('roles')
        .select('id')
        .eq('workspace_id', workspaceId)
        .eq('name', payload.name)
        .single();

      if (existing && !existingError) {
        return RolePermissionResponseUtil.createDuplicateErrorResponse(
          ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.DUPLICATE_ROLE_NAME
        );
      }

      const { data: role, error } = await this.supabaseService.getClient()
        .from('roles')
        .insert(payload)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to create role', error);
        throw new BadRequestException(ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.ROLE_CREATION_FAILED);
      }

      this.logger.log(`Role created successfully: ${role.id}`);

      const responseData = RolePermissionResponseUtil.createRoleCreationData(role);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLE_CREATED,
        ROLE_PERMISSION_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Create role failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Get all roles for workspace
   */
  async getRolesForWorkspace(req: any, queryDto: RolePermissionQueryDto): Promise<any> {
    try {
      this.logger.log('Starting get roles for workspace process');

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      const { page, limit } = RolePermissionValidationUtil.validatePaginationParams(queryDto.page, queryDto.limit);
      const skip = (page - 1) * limit;

      let query = this.supabaseService.getClient()
        .from('roles')
        .select('*', { count: 'exact' })
        .eq('workspace_id', workspaceId);

      if (queryDto.status) {
        query = query.eq('status', queryDto.status);
      }

      const { data: roles, error, count } = await query
        .order('created_at', { ascending: false })
        .range(skip, skip + limit - 1);

      if (error) {
        this.logger.error('Failed to fetch roles', error);
        throw new BadRequestException('Failed to fetch roles');
      }

      this.logger.log(`Roles fetched successfully: ${roles?.length || 0} total roles`);

      const pagination = RolePermissionResponseUtil.createPaginationMetadata(page, limit, count || 0);
      const responseData = RolePermissionResponseUtil.createRolesListData(roles || [], pagination);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLES_FETCHED
      );

    } catch (error) {
      this.logger.error('Get roles for workspace failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get role process for ID: ${id}`);

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      const { data: role, error } = await this.supabaseService.getClient()
        .from('roles')
        .select('*')
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .single();

      if (error || !role) {
        throw new NotFoundException(`Role with ID ${id} not found`);
      }

      this.logger.log(`Role fetched successfully: ${id}`);

      const responseData = RolePermissionResponseUtil.createRoleCreationData(role);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLE_FETCHED
      );

    } catch (error) {
      this.logger.error('Get role failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Update role
   */
  async updateRole(id: string, updateDto: UpdateRoleDto, req: any): Promise<any> {
    try {
      this.logger.log(`Starting update role process for ID: ${id}`);

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Get current role to validate existence
      const { data: currentRole, error: currentError } = await this.supabaseService.getClient()
        .from('roles')
        .select('*')
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .single();

      if (currentError || !currentRole) {
        throw new NotFoundException(`Role with ID ${id} not found`);
      }

      // Validate update data
      const updateData = RolePermissionValidationUtil.validateRoleUpdateData(updateDto);

      // Check for duplicate name if name is being updated
      if (updateData.name) {
        const { data: existing, error: existingError } = await this.supabaseService.getClient()
          .from('roles')
          .select('id')
          .eq('workspace_id', workspaceId)
          .eq('name', updateData.name)
          .neq('id', id)
          .single();

        if (existing && !existingError) {
          return RolePermissionResponseUtil.createDuplicateErrorResponse(
            ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.DUPLICATE_ROLE_NAME
          );
        }
      }

      const { data: updated, error } = await this.supabaseService.getClient()
        .from('roles')
        .update(updateData)
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to update role', error);
        throw new BadRequestException(ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.ROLE_UPDATE_FAILED);
      }

      this.logger.log(`Role updated successfully: ${id}`);

      const responseData = RolePermissionResponseUtil.createRoleCreationData(updated);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLE_UPDATED
      );

    } catch (error) {
      this.logger.error('Update role failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Delete role
   */
  async deleteRole(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting delete role process for ID: ${id}`);

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Check if role is in use
      const { data: roleUsers, error: roleUsersError } = await this.supabaseService.getClient()
        .from('user_roles')
        .select('id')
        .eq('role_id', id)
        .limit(1);

      if (roleUsersError) {
        this.logger.error('Failed to check role usage', roleUsersError);
        throw new BadRequestException('Failed to check role usage');
      }

      if (roleUsers && roleUsers.length > 0) {
        return RolePermissionResponseUtil.createErrorResponse(
          ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.ROLE_IN_USE,
          ROLE_PERMISSION_CONSTANTS.HTTP_STATUS.CONFLICT
        );
      }

      const { data: deleted, error } = await this.supabaseService.getClient()
        .from('roles')
        .delete()
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .select()
        .single();

      if (error || !deleted) {
        throw new NotFoundException(`Role with ID ${id} not found`);
      }

      this.logger.log(`Role deleted successfully: ${id}`);

      return RolePermissionResponseUtil.createSuccessResponse(
        {},
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLE_DELETED
      );

    } catch (error) {
      this.logger.error('Delete role failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  // ==================== PERMISSION METHODS ====================

  /**
   * Create permission
   */
  async createPermission(createDto: CreatePermissionDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting permission creation process');

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Validate and prepare data
      const payload = RolePermissionValidationUtil.validatePermissionCreationData(createDto, user.id, workspaceId);

      // Check for duplicate name in workspace
      const { data: existing, error: existingError } = await this.supabaseService.getClient()
        .from('permissions')
        .select('id')
        .eq('workspace_id', workspaceId)
        .eq('name', payload.name)
        .single();

      if (existing && !existingError) {
        return RolePermissionResponseUtil.createDuplicateErrorResponse(
          ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.DUPLICATE_PERMISSION_NAME
        );
      }

      const { data: permission, error } = await this.supabaseService.getClient()
        .from('permissions')
        .insert(payload)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to create permission', error);
        throw new BadRequestException(ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.PERMISSION_CREATION_FAILED);
      }

      this.logger.log(`Permission created successfully: ${permission.id}`);

      const responseData = RolePermissionResponseUtil.createPermissionCreationData(permission);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSION_CREATED,
        ROLE_PERMISSION_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Create permission failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Get all permissions for workspace
   */
  async getPermissionsForWorkspace(req: any, queryDto: RolePermissionQueryDto): Promise<any> {
    try {
      this.logger.log('Starting get permissions for workspace process');

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      const { page, limit } = RolePermissionValidationUtil.validatePaginationParams(queryDto.page, queryDto.limit);
      const skip = (page - 1) * limit;

      let query = this.supabaseService.getClient()
        .from('permissions')
        .select('*', { count: 'exact' })
        .eq('workspace_id', workspaceId);

      if (queryDto.status) {
        query = query.eq('status', queryDto.status);
      }

      const { data: permissions, error, count } = await query
        .order('created_at', { ascending: false })
        .range(skip, skip + limit - 1);

      if (error) {
        this.logger.error('Failed to fetch permissions', error);
        throw new BadRequestException('Failed to fetch permissions');
      }

      this.logger.log(`Permissions fetched successfully: ${permissions?.length || 0} total permissions`);

      const pagination = RolePermissionResponseUtil.createPaginationMetadata(page, limit, count || 0);
      const responseData = RolePermissionResponseUtil.createPermissionsListData(permissions || [], pagination);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSIONS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get permissions for workspace failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Get permission by ID
   */
  async getPermissionById(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get permission process for ID: ${id}`);

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      const { data: permission, error } = await this.supabaseService.getClient()
        .from('permissions')
        .select('*')
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .single();

      if (error || !permission) {
        throw new NotFoundException(`Permission with ID ${id} not found`);
      }

      this.logger.log(`Permission fetched successfully: ${id}`);

      const responseData = RolePermissionResponseUtil.createPermissionCreationData(permission);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSION_FETCHED
      );

    } catch (error) {
      this.logger.error('Get permission failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Update permission
   */
  async updatePermission(id: string, updateDto: UpdatePermissionDto, req: any): Promise<any> {
    try {
      this.logger.log(`Starting update permission process for ID: ${id}`);

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Get current permission to validate existence
      const { data: currentPermission, error: currentError } = await this.supabaseService.getClient()
        .from('permissions')
        .select('*')
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .single();

      if (currentError || !currentPermission) {
        throw new NotFoundException(`Permission with ID ${id} not found`);
      }

      // Validate update data
      const updateData = RolePermissionValidationUtil.validatePermissionUpdateData(updateDto);

      // Check for duplicate name if name is being updated
      if (updateData.name) {
        const { data: existing, error: existingError } = await this.supabaseService.getClient()
          .from('permissions')
          .select('id')
          .eq('workspace_id', workspaceId)
          .eq('name', updateData.name)
          .neq('id', id)
          .single();

        if (existing && !existingError) {
          return RolePermissionResponseUtil.createDuplicateErrorResponse(
            ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.DUPLICATE_PERMISSION_NAME
          );
        }
      }

      const { data: updated, error } = await this.supabaseService.getClient()
        .from('permissions')
        .update(updateData)
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to update permission', error);
        throw new BadRequestException(ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.PERMISSION_UPDATE_FAILED);
      }

      this.logger.log(`Permission updated successfully: ${id}`);

      const responseData = RolePermissionResponseUtil.createPermissionCreationData(updated);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSION_UPDATED
      );

    } catch (error) {
      this.logger.error('Update permission failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Delete permission
   */
  async deletePermission(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting delete permission process for ID: ${id}`);

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Check if permission is in use
      const { data: rolePermissions, error: rolePermissionsError } = await this.supabaseService.getClient()
        .from('role_permissions')
        .select('id')
        .eq('permission_id', id)
        .limit(1);

      if (rolePermissionsError) {
        this.logger.error('Failed to check permission usage', rolePermissionsError);
        throw new BadRequestException('Failed to check permission usage');
      }

      if (rolePermissions && rolePermissions.length > 0) {
        return RolePermissionResponseUtil.createErrorResponse(
          ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.PERMISSION_IN_USE,
          ROLE_PERMISSION_CONSTANTS.HTTP_STATUS.CONFLICT
        );
      }

      const { data: deleted, error } = await this.supabaseService.getClient()
        .from('permissions')
        .delete()
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .select()
        .single();

      if (error || !deleted) {
        throw new NotFoundException(`Permission with ID ${id} not found`);
      }

      this.logger.log(`Permission deleted successfully: ${id}`);

      return RolePermissionResponseUtil.createSuccessResponse(
        {},
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSION_DELETED
      );

    } catch (error) {
      this.logger.error('Delete permission failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  // ==================== ROLE-PERMISSION ASSIGNMENT METHODS ====================

  /**
   * Assign permission to role
   */
  async assignPermissionToRole(assignDto: AssignPermissionDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting assign permission to role process');

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Validate assignment data
      const { roleId, permissionId } = RolePermissionValidationUtil.validatePermissionAssignmentData(assignDto);

      // Verify role exists and belongs to workspace
      const { data: role, error: roleError } = await this.supabaseService.getClient()
        .from('roles')
        .select('id')
        .eq('id', roleId)
        .eq('workspace_id', workspaceId)
        .single();

      if (roleError || !role) {
        throw new NotFoundException(`Role with ID ${roleId} not found`);
      }

      // Verify permission exists and belongs to workspace
      const { data: permission, error: permissionError } = await this.supabaseService.getClient()
        .from('permissions')
        .select('id')
        .eq('id', permissionId)
        .eq('workspace_id', workspaceId)
        .single();

      if (permissionError || !permission) {
        throw new NotFoundException(`Permission with ID ${permissionId} not found`);
      }

      // Check if assignment already exists
      const { data: existing, error: existingError } = await this.supabaseService.getClient()
        .from('role_permissions')
        .select('id')
        .eq('role_id', roleId)
        .eq('permission_id', permissionId)
        .single();

      if (existing && !existingError) {
        return RolePermissionResponseUtil.createErrorResponse(
          'Permission is already assigned to this role',
          ROLE_PERMISSION_CONSTANTS.HTTP_STATUS.CONFLICT
        );
      }

      const { data: assignment, error } = await this.supabaseService.getClient()
        .from('role_permissions')
        .insert({
          role_id: roleId,
          permission_id: permissionId,
          workspace_id: workspaceId,
          assigned_by: user.id,
        })
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to assign permission to role', error);
        throw new BadRequestException(ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.PERMISSION_ASSIGNMENT_FAILED);
      }

      this.logger.log(`Permission assigned to role successfully: ${roleId} -> ${permissionId}`);

      return RolePermissionResponseUtil.createSuccessResponse(
        { assignment },
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSION_ASSIGNED
      );

    } catch (error) {
      this.logger.error('Assign permission to role failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Revoke permission from role
   */
  async revokePermissionFromRole(assignDto: AssignPermissionDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting revoke permission from role process');

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Validate assignment data
      const { roleId, permissionId } = RolePermissionValidationUtil.validatePermissionAssignmentData(assignDto);

      const { data: deleted, error } = await this.supabaseService.getClient()
        .from('role_permissions')
        .delete()
        .eq('role_id', roleId)
        .eq('permission_id', permissionId)
        .eq('workspace_id', workspaceId)
        .select()
        .single();

      if (error || !deleted) {
        throw new NotFoundException('Permission assignment not found');
      }

      this.logger.log(`Permission revoked from role successfully: ${roleId} -> ${permissionId}`);

      return RolePermissionResponseUtil.createSuccessResponse(
        {},
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSION_REVOKED
      );

    } catch (error) {
      this.logger.error('Revoke permission from role failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  /**
   * Get role permissions
   */
  async getRolePermissions(roleId: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get role permissions process for role ID: ${roleId}`);

      const user = RolePermissionValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = RolePermissionValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Verify role exists and belongs to workspace
      const { data: role, error: roleError } = await this.supabaseService.getClient()
        .from('roles')
        .select('id')
        .eq('id', roleId)
        .eq('workspace_id', workspaceId)
        .single();

      if (roleError || !role) {
        throw new NotFoundException(`Role with ID ${roleId} not found`);
      }

      const { data: rolePermissions, error } = await this.supabaseService.getClient()
        .from('role_permissions')
        .select(`
          *,
          permissions (
            id,
            name,
            resource,
            action,
            description,
            status
          )
        `)
        .eq('role_id', roleId)
        .eq('workspace_id', workspaceId);

      if (error) {
        this.logger.error('Failed to fetch role permissions', error);
        throw new BadRequestException('Failed to fetch role permissions');
      }

      this.logger.log(`Role permissions fetched successfully: ${rolePermissions?.length || 0} permissions for role ${roleId}`);

      const responseData = RolePermissionResponseUtil.createRolePermissionsData(rolePermissions || []);
      return RolePermissionResponseUtil.createSuccessResponse(
        responseData,
        ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLE_PERMISSIONS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get role permissions failed:', error);
      this.handleRolePermissionError(error);
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Handles role-permission-related errors
   */
  private handleRolePermissionError(error: any): void {
    if (error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException) {
      throw error;
    }

    this.logger.error('Unexpected role-permission error:', error);
    throw new BadRequestException('Internal server error');
  }
}
