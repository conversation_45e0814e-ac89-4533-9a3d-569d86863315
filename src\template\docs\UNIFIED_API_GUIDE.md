# Unified Template API Guide

## Overview

The Template API now provides a unified, clean interface for creating WhatsApp Business templates using Meta's exact format requirements. This eliminates confusion and provides a single, consistent way to create templates.

## Key Features

- ✅ **Single Endpoint**: One endpoint for all template creation
- ✅ **Meta Format**: Direct Meta API format support
- ✅ **No Conversion**: No backend format conversion needed
- ✅ **Clean DTO**: Single, well-validated DTO
- ✅ **Type Safe**: Full TypeScript support
- ✅ **Comprehensive Validation**: All validation in DTO layer

## API Endpoint

```
POST /templates
```

## Request Format

### Headers
```http
Content-Type: application/json
Authorization: Bearer <jwt_token>
```

### Request Body
```json
{
  "name": "appointment_confirmation",
  "description": "Confirm customer appointments",
  "category": "UTILITY",
  "language": "en",
  "waba_id": "1159844736009453",
  "components": [
    {
      "type": "HEADER",
      "format": "TEXT",
      "text": "{{1}}",
      "example": {
        "header_text": ["Appointment Confirmation"]
      }
    },
    {
      "type": "BODY",
      "text": "Hi {{1}}, your appointment is confirmed for {{2}}. Please contact us if you need to reschedule.",
      "example": {
        "body_text": [["John", "2:00 PM"]]
      }
    },
    {
      "type": "FOOTER",
      "text": "Thank you for choosing us!"
    }
  ]
}
```

## Field Specifications

### Required Fields

| Field | Type | Description | Validation |
|-------|------|-------------|------------|
| `name` | string | Template name | Max 512 chars, unique per workspace |
| `category` | string | Template category | MARKETING, UTILITY, or AUTHENTICATION |
| `language` | string | Template language | Standard language codes (en_US, es_ES, etc.) |
| `waba_id` | string | WhatsApp Business Account ID | Max 50 chars |
| `components` | array | Template components | Min 1, Max 4 components |

### Optional Fields

| Field | Type | Description | Validation |
|-------|------|-------------|------------|
| `description` | string | Template description | Max 1024 chars |

## Component Types

### 1. HEADER Component

**Required for:** Templates with headers (text, image, video, document, location)

```json
{
  "type": "HEADER",
  "format": "TEXT",
  "text": "{{1}}",
  "example": {
    "header_text": ["Sample Header"]
  }
}
```

**Validation Rules:**
- `format` is required
- `text` is required for TEXT format
- `example.header_text` is required if text contains variables
- Only one HEADER component allowed per template

### 2. BODY Component

**Required for:** All templates (mandatory by Meta)

```json
{
  "type": "BODY",
  "text": "Hi {{1}}, your appointment is confirmed.",
  "example": {
    "body_text": [["Rahul"]]
  }
}
```

**Validation Rules:**
- Exactly one BODY component required
- `text` is mandatory
- `example.body_text` is required if text contains variables
- Format: `[["value1", "value2", ...]]` (nested array)

### 3. FOOTER Component

**Optional:** For templates with footer text

```json
{
  "type": "FOOTER",
  "text": "Thank you for choosing us!"
}
```

**Validation Rules:**
- Only one FOOTER component allowed
- `text` is required if component exists
- Max 60 characters

### 4. BUTTONS Component

**Optional:** For interactive templates

```json
{
  "type": "BUTTONS",
  "buttons": [
    {
      "type": "QUICK_REPLY",
      "text": "Yes"
    },
    {
      "type": "URL",
      "text": "Visit Website",
      "url": "https://example.com"
    }
  ]
}
```

**Validation Rules:**
- Multiple BUTTONS components allowed
- Each button must have `type` and `text`
- URL buttons require `url` field
- Phone buttons require `phone_number` field

## Variable Format

### Meta Requirements

1. **Independent Numbering**: Each component starts from `{{1}}`
2. **Sequential Variables**: No gaps ({{1}}, {{2}}, {{3}})
3. **No Special Characters**: No #, $, % in variables
4. **No Dangling Parameters**: Template cannot start/end with {{}}

### Examples

**Correct:**
```json
{
  "text": "Hi {{1}}, your {{2}} is confirmed.",
  "example": {
    "body_text": [["John", "appointment"]]
  }
}
```

**Incorrect:**
```json
{
  "text": "{{1}} starts here",
  "example": {
    "body_text": [["John"]]
  }
}
```

## Response Format

### Success Response (201 Created)

```json
{
  "status": "success",
  "code": 201,
  "message": "Template created successfully",
  "data": {
    "template": {
      "id": "template_uuid",
      "name": "appointment_confirmation",
      "meta_template_id": "*********",
      "meta_template_status": "PENDING",
      "category": "UTILITY",
      "language": "en",
      "components": [...],
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response (400 Bad Request)

```json
{
  "status": "error",
  "code": 400,
  "message": "Validation failed",
  "data": {
    "errors": [
      "BODY component is required",
      "At least one component is required"
    ]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Common Error Messages

| Error Code | Message | Solution |
|------------|---------|----------|
| 400 | BODY component is required | Add a BODY component to your template |
| 400 | At least one component is required | Add components array to your request |
| 400 | Maximum 4 components allowed | Reduce number of components |
| 400 | Template name already exists | Use a different template name |
| 400 | Meta credentials not found | Ensure WABA is properly connected |
| 400 | Invalid template data | Check Meta API format compliance |

## Best Practices

### 1. Template Naming
- Use descriptive, unique names
- Include version numbers for updates
- Follow naming conventions (lowercase, underscores)

### 2. Variable Usage
- Keep variables minimal and meaningful
- Use clear example values
- Test with actual data

### 3. Content Guidelines
- Keep messages concise and clear
- Follow WhatsApp Business Policy
- Use appropriate language codes

### 4. Testing
- Always test templates before production
- Use the test endpoint for validation
- Monitor template approval status

## Template Categories

### UTILITY
- Transaction confirmations
- Account updates
- Service notifications
- Appointment reminders

### MARKETING
- Promotional messages
- Product announcements
- Event invitations
- Newsletter content

### AUTHENTICATION
- OTP codes
- Login notifications
- Security alerts
- Verification codes

## Language Codes

| Language | Code |
|----------|------|
| English (US) | en_US |
| English (UK) | en_GB |
| Spanish | es_ES |
| French | fr_FR |
| German | de_DE |
| Hindi | hi_IN |

## Rate Limits

- **Template Creation**: 100 requests per hour per workspace
- **Template Updates**: 50 requests per hour per workspace
- **Template Deletion**: 20 requests per hour per workspace

## Usage Examples

### Basic Template
```typescript
const response = await fetch('/templates', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    name: 'welcome_message',
    category: 'UTILITY',
    language: 'en',
    waba_id: '*********',
    components: [
      {
        type: 'BODY',
        text: 'Welcome {{1}} to our platform!',
        example: {
          body_text: [['John']]
        }
      }
    ]
  })
});
```

### Advanced Template with Header
```typescript
const response = await fetch('/templates', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    name: 'order_confirmation',
    description: 'Confirm customer orders',
    category: 'UTILITY',
    language: 'en',
    waba_id: '*********',
    components: [
      {
        type: 'HEADER',
        format: 'TEXT',
        text: '{{1}}',
        example: {
          header_text: ['Order Confirmation']
        }
      },
      {
        type: 'BODY',
        text: 'Hi {{1}}, your order #{{2}} has been confirmed.',
        example: {
          body_text: [['John', '12345']]
        }
      },
      {
        type: 'FOOTER',
        text: 'Thank you for your order!'
      }
    ]
  })
});
```

## Support

For issues or questions:
- Check error messages for specific validation failures
- Review Meta's official documentation
- Contact support with template ID and error details

---

**Note**: This unified API provides a clean, consistent interface for template creation. All validation is handled at the DTO level, ensuring type safety and proper format compliance.
