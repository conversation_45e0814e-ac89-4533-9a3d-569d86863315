/**
 * Test fixtures for Meta-Onboarding module
 */

export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  name: 'Test User'
};

export const mockUserProfile = {
  id: 'user-123',
  workspace_id: 1,
  email: '<EMAIL>',
  name: 'Test User',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

export const mockCredentials = {
  id: 'credentials-123',
  whatsapp_business_id: '123456789',
  phone_number_id: '987654321',
  access_token: 'mock-access-token-123456789',
  status: 'Active',
  workspace_id: 1,
  created_by: 'user-123',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

export const mockCreateCredentialsDto = {
  whatsapp_business_id: '123456789',
  phone_number_id: '987654321',
  access_token: 'mock-access-token-123456789',
  status: 'Active' as const
};

export const mockUpdateCredentialsDto = {
  whatsapp_business_id: 'updated-business-id',
  status: 'Inactive' as const
};

export const mockCredentialsQueryDto = {
  page: 1,
  limit: 10
};

export const mockCredentialsList = [
  mockCredentials,
  {
    id: 'credentials-456',
    whatsapp_business_id: '987654321',
    phone_number_id: '123456789',
    access_token: 'another-access-token',
    status: 'Inactive',
    workspace_id: 1,
    created_by: 'user-123',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  }
];

export const mockPagination = {
  page: 1,
  limit: 10,
  total: 2,
  totalPages: 1
};

export const mockRequest = {
  user: mockUser,
  headers: {
    authorization: 'Bearer mock-jwt-token'
  }
};

export const mockWorkspaceMember = {
  id: 'member-123',
  workspace_id: 1,
  user_id: 'user-123',
  role: 'admin',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
};

export const mockSupabaseError = {
  message: 'Database error',
  code: 'PGRST001',
  details: 'Connection failed'
};

export const mockValidationError = {
  message: 'Validation failed',
  details: [
    'WhatsApp Business ID is required',
    'Phone Number ID is required',
    'Access token is required'
  ]
};

export const mockInvalidCredentialsDto = {
  whatsapp_business_id: '', // Invalid: empty string
  phone_number_id: 'a'.repeat(51), // Invalid: too long
  access_token: 'short', // Invalid: too short
  status: 'Invalid' // Invalid: not in enum
};

export const mockValidCredentialsDto = {
  whatsapp_business_id: '123456789',
  phone_number_id: '987654321',
  access_token: 'valid-access-token-123456789',
  status: 'Active' as const
};

export const mockUpdatedCredentials = {
  ...mockCredentials,
  whatsapp_business_id: 'updated-business-id',
  status: 'Inactive',
  updated_at: '2023-01-02T00:00:00Z'
};
