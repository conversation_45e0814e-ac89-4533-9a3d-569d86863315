# Queue Module Deployment Guide

## Overview

This document provides comprehensive deployment instructions for the Queue Module, including environment setup, configuration, and production deployment strategies.

## Prerequisites

### System Requirements

- **Node.js**: v18.x or higher
- **Redis**: v7.x or higher
- **Memory**: Minimum 2GB RAM
- **Storage**: Minimum 10GB free space
- **Network**: Port 6379 (Redis), 3000 (API)

### Dependencies

```bash
# Core dependencies
npm install @nestjs/bull bull redis

# Development dependencies
npm install --save-dev @types/bull
```

## Environment Setup

### Environment Variables

```bash
# .env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Queue Configuration
QUEUE_CONCURRENCY=5
QUEUE_MAX_ATTEMPTS=3
QUEUE_BACKOFF_DELAY=2000
QUEUE_REMOVE_ON_COMPLETE=100
QUEUE_REMOVE_ON_FAIL=50

# Application Configuration
NODE_ENV=production
PORT=3000
JWT_SECRET=your_jwt_secret

# Monitoring
ENABLE_QUEUE_MONITORING=true
QUEUE_METRICS_INTERVAL=60000
```

### Redis Configuration

```yaml
# docker-compose.redis.yml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: redis
    ports:
      - "6379:6379"
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --tcp-keepalive 60
      --timeout 300
    volumes:
      - redis-data:/data
    networks:
      - queue-network
    restart: unless-stopped

  redis-commander:
    image: rediscommander/redis-commander:latest
    hostname: redis-commander
    container_name: redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD}
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - queue-network
    restart: unless-stopped

volumes:
  redis-data:
    driver: local

networks:
  queue-network:
    driver: bridge
```

## Local Development

### Quick Start

```bash
# Clone repository
git clone <repository-url>
cd automate-whatsapp-backend

# Install dependencies
npm install

# Start Redis
npm run redis:up

# Start development server
npm run start:dev

# Test queue connection
npm run test:queue
```

### Development Commands

```bash
# Start Redis
npm run redis:up

# Stop Redis
npm run redis:down

# Test queue functionality
npm run test:queue

# Run tests
npm test

# Build application
npm run build

# Start production server
npm run start:prod
```

## Docker Deployment

### Dockerfile

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Copy built application
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# Switch to non-root user
USER nestjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node dist/health-check.js

# Start application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/main"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: redis
    ports:
      - "6379:6379"
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    networks:
      - app-network
    restart: unless-stopped

  app:
    build: .
    hostname: app
    container_name: app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - redis
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    hostname: nginx
    container_name: nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - app-network
    restart: unless-stopped

volumes:
  redis-data:
    driver: local

networks:
  app-network:
    driver: bridge
```

### Nginx Configuration

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:3000;
    }

    server {
        listen 80;
        server_name your-domain.com;

        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # Security Headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # Rate Limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req zone=api burst=20 nodelay;

        # Proxy Configuration
        location / {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Health Check
        location /health {
            proxy_pass http://app/health;
            access_log off;
        }
    }
}
```

## Kubernetes Deployment

### Namespace

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: queue-system
```

### ConfigMap

```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: queue-config
  namespace: queue-system
data:
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  QUEUE_CONCURRENCY: "5"
  QUEUE_MAX_ATTEMPTS: "3"
  QUEUE_BACKOFF_DELAY: "2000"
  NODE_ENV: "production"
```

### Secret

```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: queue-secrets
  namespace: queue-system
type: Opaque
data:
  REDIS_PASSWORD: <base64-encoded-password>
  JWT_SECRET: <base64-encoded-jwt-secret>
```

### Redis Deployment

```yaml
# k8s/redis-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: queue-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        - --maxmemory
        - 256mb
        - --maxmemory-policy
        - allkeys-lru
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: queue-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: queue-system
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: queue-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
```

### Application Deployment

```yaml
# k8s/app-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: queue-app
  namespace: queue-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: queue-app
  template:
    metadata:
      labels:
        app: queue-app
    spec:
      containers:
      - name: app
        image: your-registry/queue-app:latest
        ports:
        - containerPort: 3000
        env:
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: queue-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: queue-config
              key: REDIS_PORT
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: queue-secrets
              key: REDIS_PASSWORD
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: queue-secrets
              key: JWT_SECRET
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: queue-service
  namespace: queue-system
spec:
  selector:
    app: queue-app
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

### Horizontal Pod Autoscaler

```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: queue-hpa
  namespace: queue-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: queue-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Production Deployment

### Environment Preparation

```bash
# Create production environment
mkdir -p /opt/queue-app
cd /opt/queue-app

# Clone repository
git clone <repository-url> .

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Set up environment
cp .env.example .env
# Edit .env with production values
```

### Systemd Service

```ini
# /etc/systemd/system/queue-app.service
[Unit]
Description=Queue Application
After=network.target redis.service

[Service]
Type=simple
User=queue-app
WorkingDirectory=/opt/queue-app
ExecStart=/usr/bin/node dist/main
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=REDIS_HOST=localhost
Environment=REDIS_PORT=6379

[Install]
WantedBy=multi-user.target
```

### Service Management

```bash
# Enable and start service
sudo systemctl enable queue-app
sudo systemctl start queue-app

# Check status
sudo systemctl status queue-app

# View logs
sudo journalctl -u queue-app -f

# Restart service
sudo systemctl restart queue-app
```

### Process Management

```bash
# Install PM2
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'queue-app',
    script: 'dist/main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      REDIS_HOST: 'localhost',
      REDIS_PORT: 6379
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# Start application
pm2 start ecosystem.config.js

# Monitor application
pm2 monit

# Save PM2 configuration
pm2 save
pm2 startup
```

## Monitoring and Logging

### Health Checks

```typescript
// src/health/health.controller.ts
import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService, RedisHealthIndicator } from '@nestjs/terminus';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private redis: RedisHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.redis.checkHealth('redis'),
    ]);
  }
}
```

### Logging Configuration

```typescript
// src/main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // Configure logging
  const logger = new Logger('Bootstrap');
  logger.log('Application starting...');

  await app.listen(3000);
  logger.log('Application is running on: http://localhost:3000');
}
bootstrap();
```

### Metrics Collection

```typescript
// src/metrics/metrics.service.ts
import { Injectable } from '@nestjs/common';
import { Counter, Histogram, register } from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly messageCounter = new Counter({
    name: 'queue_messages_total',
    help: 'Total number of messages processed',
    labelNames: ['queue', 'status'],
  });

  private readonly processingTime = new Histogram({
    name: 'queue_processing_duration_seconds',
    help: 'Time spent processing messages',
    labelNames: ['queue'],
  });

  incrementMessageCounter(queue: string, status: string) {
    this.messageCounter.labels({ queue, status }).inc();
  }

  recordProcessingTime(queue: string, duration: number) {
    this.processingTime.labels({ queue }).observe(duration);
  }

  async getMetrics() {
    return register.metrics();
  }
}
```

## Backup and Recovery

### Redis Backup

```bash
# Create backup script
cat > backup-redis.sh << EOF
#!/bin/bash
BACKUP_DIR="/opt/backups/redis"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="redis_backup_$DATE.rdb"

mkdir -p $BACKUP_DIR

# Create backup
redis-cli --rdb $BACKUP_DIR/$BACKUP_FILE

# Compress backup
gzip $BACKUP_DIR/$BACKUP_FILE

# Remove old backups (keep last 7 days)
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_FILE.gz"
EOF

chmod +x backup-redis.sh

# Schedule backup (crontab)
# 0 2 * * * /opt/backups/redis/backup-redis.sh
```

### Application Backup

```bash
# Create application backup script
cat > backup-app.sh << EOF
#!/bin/bash
BACKUP_DIR="/opt/backups/app"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="app_backup_$DATE.tar.gz"

mkdir -p $BACKUP_DIR

# Create backup
tar -czf $BACKUP_DIR/$BACKUP_FILE \
  --exclude=node_modules \
  --exclude=dist \
  --exclude=.git \
  /opt/queue-app

# Remove old backups (keep last 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Application backup completed: $BACKUP_FILE"
EOF

chmod +x backup-app.sh
```

## Security Hardening

### Firewall Configuration

```bash
# UFW Firewall
sudo ufw allow 22/tcp          # SSH
sudo ufw allow 80/tcp          # HTTP
sudo ufw allow 443/tcp         # HTTPS
sudo ufw allow 3000/tcp        # API (if needed)
sudo ufw deny 6379/tcp         # Redis (internal only)
sudo ufw enable
```

### SSL/TLS Configuration

```bash
# Generate SSL certificate
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Or use Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

### Security Headers

```typescript
// src/main.ts
import helmet from 'helmet';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Security headers
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  }));

  await app.listen(3000);
}
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   ```bash
   # Check Redis status
   sudo systemctl status redis
   
   # Check Redis logs
   sudo journalctl -u redis -f
   
   # Test Redis connection
   redis-cli ping
   ```

2. **Queue Processing Stopped**
   ```bash
   # Check application logs
   sudo journalctl -u queue-app -f
   
   # Check queue statistics
   curl http://localhost:3000/queue/stats
   
   # Restart application
   sudo systemctl restart queue-app
   ```

3. **High Memory Usage**
   ```bash
   # Check memory usage
   free -h
   
   # Check Redis memory
   redis-cli info memory
   
   # Clear completed jobs
   curl -X POST http://localhost:3000/queue/clear/campaign-messages
   ```

### Performance Tuning

```bash
# Redis optimization
redis-cli CONFIG SET maxmemory 256mb
redis-cli CONFIG SET maxmemory-policy allkeys-lru

# Application optimization
export NODE_OPTIONS="--max-old-space-size=512"
export UV_THREADPOOL_SIZE=16
```

### Debug Mode

```bash
# Enable debug logging
export DEBUG=queue:*
export LOG_LEVEL=debug

# Start application with debug
npm run start:dev
```

## Maintenance

### Regular Maintenance Tasks

1. **Daily**
   - Monitor queue statistics
   - Check error logs
   - Verify Redis health

2. **Weekly**
   - Review performance metrics
   - Clean up old logs
   - Update dependencies

3. **Monthly**
   - Security updates
   - Backup verification
   - Capacity planning

### Update Procedure

```bash
# Create backup
./backup-app.sh

# Pull latest changes
git pull origin main

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Restart service
sudo systemctl restart queue-app

# Verify deployment
curl http://localhost:3000/health
```

## Support and Documentation

### Log Locations

- **Application Logs**: `/var/log/queue-app/`
- **System Logs**: `journalctl -u queue-app`
- **Redis Logs**: `journalctl -u redis`
- **Nginx Logs**: `/var/log/nginx/`

### Monitoring Endpoints

- **Health Check**: `GET /health`
- **Queue Stats**: `GET /queue/stats`
- **Metrics**: `GET /metrics`

### Contact Information

- **Technical Support**: <EMAIL>
- **Documentation**: https://docs.yourcompany.com
- **Issue Tracker**: https://github.com/yourcompany/issues
