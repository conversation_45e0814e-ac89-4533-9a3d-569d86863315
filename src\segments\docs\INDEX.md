# Segments Module Documentation

## Overview

The Segments module provides functionality for creating and managing contact segments within workspaces. Segments allow users to group contacts based on specific criteria using flexible rule-based filtering.

## Table of Contents

- [Architecture](ARCHITECTURE.md) - Module architecture and design patterns
- [API Endpoints](API_ENDPOINTS.md) - Complete API documentation
- [Security](SECURITY.md) - Security considerations and best practices
- [Testing](TESTING.md) - Testing strategies and test cases
- [Deployment](DEPLOYMENT.md) - Deployment guidelines

## Features

- ✅ Create segments with flexible rule-based filtering
- ✅ Support for multiple operators (equals, contains, regex, etc.)
- ✅ Tag-based filtering (hasAnyTag, hasAllTags)
- ✅ Custom field filtering
- ✅ Legacy condition support for backward compatibility
- ✅ Get contacts by segment with pagination
- ✅ Get available segment operators
- ✅ Workspace-scoped access control
- ✅ Comprehensive validation
- ✅ Consistent error handling
- ✅ Detailed logging

## Quick Start

### Prerequisites

- User must be authenticated
- User must belong to a workspace
- Proper permissions for segment management

### Basic Usage

```typescript
// Create a simple segment
POST /segments
{
  "name": "VIP Customers",
  "description": "High-value customers",
  "rules": [
    {
      "field": "subscribed",
      "operator": "equals",
      "value": true
    }
  ],
  "match": "all"
}

// Create a complex segment with multiple rules
POST /segments
{
  "name": "Active Users",
  "description": "Users who are active and have specific tags",
  "rules": [
    {
      "field": "subscribed",
      "operator": "equals",
      "value": true
    },
    {
      "field": "tagsId",
      "operator": "hasAnyTag",
      "value": ["tag1", "tag2"]
    }
  ],
  "match": "all"
}
```

## Module Structure

```
segments/
├── segments.controller.ts    # API endpoints
├── segments.service.ts       # Business logic
├── segments.module.ts        # Module configuration
├── operators.ts              # Segment operators definition
├── dto/                      # Data transfer objects
│   ├── create-segment.dto.ts
│   ├── update-segment.dto.ts
│   ├── segment-query.dto.ts
│   └── index.ts
├── utils/                    # Utility classes
│   ├── segments-constants.util.ts
│   ├── segments-validation.util.ts
│   └── segments-response.util.ts
└── docs/                     # Documentation
    ├── INDEX.md
    ├── ARCHITECTURE.md
    ├── API_ENDPOINTS.md
    ├── SECURITY.md
    ├── TESTING.md
    └── DEPLOYMENT.md
```

## Segment Rules

Segments use a flexible rule-based system for filtering contacts:

### Rule Structure

```typescript
{
  "field": "fieldName",        // Contact field to filter on
  "operator": "operatorType",  // How to compare the field
  "value": "comparisonValue"   // Value to compare against
}
```

### Supported Fields

- **Basic Fields**: `firstName`, `lastName`, `chatName`, `email`, `phoneNumber`, `countryCode`
- **Status Fields**: `subscribed`, `source`
- **Tag Fields**: `tagsId`
- **Date Fields**: `createdAt`, `updatedAt`
- **Custom Fields**: `custom.fieldName` (for custom field filtering)

### Supported Operators

| Operator | Description | Value Type | Example |
|----------|-------------|------------|---------|
| `equals` | Field equals value | any | `{"field": "subscribed", "operator": "equals", "value": true}` |
| `notEquals` | Field not equal to value | any | `{"field": "source", "operator": "notEquals", "value": "manual"}` |
| `contains` | Field contains substring | string | `{"field": "firstName", "operator": "contains", "value": "John"}` |
| `notContains` | Field doesn't contain substring | string | `{"field": "email", "operator": "notContains", "value": "test"}` |
| `startsWith` | Field starts with prefix | string | `{"field": "phoneNumber", "operator": "startsWith", "value": "+1"}` |
| `endsWith` | Field ends with suffix | string | `{"field": "email", "operator": "endsWith", "value": "@company.com"}` |
| `regex` | Field matches regex | string | `{"field": "email", "operator": "regex", "value": "^[a-z]+@company\\.com$"}` |
| `exists` | Field exists | boolean | `{"field": "custom.vip", "operator": "exists", "value": true}` |
| `in` | Field value in list | array | `{"field": "source", "operator": "in", "value": ["manual", "import"]}` |
| `hasAnyTag` | Contact has any of these tags | array | `{"field": "tagsId", "operator": "hasAnyTag", "value": ["tag1", "tag2"]}` |
| `hasAllTags` | Contact has all of these tags | array | `{"field": "tagsId", "operator": "hasAllTags", "value": ["tag1", "tag2"]}` |

### Match Types

- **`all`** (default): All rules must match (AND logic)
- **`any`**: Any rule can match (OR logic)

## API Endpoints

All endpoints follow the same patterns as the auth module:

- `POST /segments` - Create segment
- `GET /segments` - Get all segments
- `GET /segments/:id/contacts` - Get contacts by segment
- `GET /segments/operators/list` - Get available operators

## Response Format

All responses follow the standardized format:

```json
{
  "status": "success|error",
  "code": 200,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Security Features

- ✅ Authentication required for all endpoints
- ✅ Workspace-scoped access control
- ✅ Input validation and sanitization
- ✅ SQL injection prevention
- ✅ XSS protection
- ✅ Rate limiting support

## Performance Considerations

- Database indexes on workspaceId and name
- Efficient query building for complex rules
- Pagination for contact retrieval
- Lean queries for read operations

## Monitoring and Logging

- Comprehensive logging at service level
- Error tracking and monitoring
- Performance metrics collection
- Audit trail for segment operations

## Dependencies

- `@nestjs/common` - NestJS core functionality
- `@nestjs/mongoose` - MongoDB integration
- `class-validator` - DTO validation
- `mongoose` - MongoDB ODM
- `../auth/auth.guard` - Authentication
- `../supabase/supabase.service` - User profile management

## Version History

- **v1.0.0** - Initial implementation with basic segment creation
- **v1.1.0** - Added rule-based filtering system
- **v1.2.0** - Added comprehensive validation and error handling
- **v1.3.0** - Refactored following auth module patterns
- **v1.4.0** - Added complete documentation and test coverage
