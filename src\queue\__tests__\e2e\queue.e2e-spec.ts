import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../app.module';
import { QueueService } from '../../queue.service';

describe('Queue E2E', () => {
  let app: INestApplication;
  let queueService: QueueService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    queueService = moduleFixture.get<QueueService>(QueueService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/queue/stats (GET)', () => {
    it('should return queue statistics', () => {
      return request(app.getHttpServer())
        .get('/queue/stats')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'success');
          expect(res.body).toHaveProperty('data');
          expect(res.body.data).toHaveProperty('campaignMessages');
          expect(res.body.data).toHaveProperty('scheduledMessages');
          expect(res.body.data).toHaveProperty('retryMessages');
        });
    });
  });

  describe('/queue/pause/:queueName (POST)', () => {
    it('should pause queue successfully', () => {
      return request(app.getHttpServer())
        .post('/queue/pause/campaign-messages')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body.message).toContain('paused successfully');
        });
    });

    it('should return 400 for invalid queue name', () => {
      return request(app.getHttpServer())
        .post('/queue/pause/invalid-queue')
        .expect(400);
    });
  });

  describe('/queue/resume/:queueName (POST)', () => {
    it('should resume queue successfully', () => {
      return request(app.getHttpServer())
        .post('/queue/resume/campaign-messages')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body.message).toContain('resumed successfully');
        });
    });
  });

  describe('/queue/clear/:queueName (POST)', () => {
    it('should clear queue successfully', () => {
      return request(app.getHttpServer())
        .post('/queue/clear/campaign-messages')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body.message).toContain('cleared successfully');
        });
    });
  });
});
