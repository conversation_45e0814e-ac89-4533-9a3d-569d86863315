import { Response } from 'express';

/**
 * Standard response interface for consistent API responses
 */
export interface StandardCustomFieldsResponse {
  status: 'success' | 'error';
  code: number;
  message: string;
  data?: any;
  error?: any;
  timestamp: string;
}

/**
 * Custom fields-specific response data interfaces
 */
export interface CustomFieldsResponseData {
  customField?: any;
  customFields?: any[];
  pagination?: PaginationMetadata;
}

export interface PaginationMetadata {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Utility class for creating consistent custom fields responses
 */
export class CustomFieldsResponseUtil {
  /**
   * Creates a success response with custom fields data
   */
  static createSuccessResponse(
    data: CustomFieldsResponseData | any,
    message: string = 'Operation completed successfully',
    code: number = 200
  ): StandardCustomFieldsResponse {
    return {
      status: 'success',
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates an error response
   */
  static createErrorResponse(
    message: string,
    code: number = 400,
    error?: any
  ): StandardCustomFieldsResponse {
    return {
      status: 'error',
      code,
      message,
      error: error?.message || error,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates a duplicate error response
   */
  static createDuplicateErrorResponse(
    message: string,
    code: number = 409
  ): StandardCustomFieldsResponse {
    return {
      status: 'error',
      code,
      message,
      error: 'Duplicate entry',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates a validation error response
   */
  static createValidationErrorResponse(
    message: string,
    details?: any,
    code: number = 400
  ): StandardCustomFieldsResponse {
    return {
      status: 'error',
      code,
      message,
      error: details,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Sends response to client
   */
  static sendResponse(res: Response, response: StandardCustomFieldsResponse): void {
    res.status(response.code).json(response);
  }

  /**
   * Creates custom field creation data
   */
  static createCustomFieldCreationData(customField: any): CustomFieldsResponseData {
    return {
      customField: {
        id: customField._id,
        label: customField.label,
        type: customField.type,
        options: customField.options,
        showOnContact: customField.showOnContact,
        showOnChat: customField.showOnChat,
        workspaceId: customField.workspaceId,
        createdBy: customField.createdBy,
        createdAt: customField.createdAt,
        updatedAt: customField.updatedAt
      }
    };
  }

  /**
   * Creates custom fields list data
   */
  static createCustomFieldsListData(customFields: any[]): CustomFieldsResponseData {
    return {
      customFields: customFields.map(customField => ({
        id: customField._id,
        label: customField.label,
        type: customField.type,
        options: customField.options,
        showOnContact: customField.showOnContact,
        showOnChat: customField.showOnChat,
        workspaceId: customField.workspaceId,
        createdBy: customField.createdBy,
        createdAt: customField.createdAt,
        updatedAt: customField.updatedAt
      }))
    };
  }

  /**
   * Creates pagination metadata
   */
  static createPaginationMetadata(page: number, limit: number, total: number): PaginationMetadata {
    const totalPages = Math.ceil(total / limit);
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };
  }
}
