import { Is<PERSON>ptional, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';
import { TEMPLATE_CONSTANTS } from '../utils/template-constants.util';

export class TemplateQueryDto {
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number;

  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(TEMPLATE_CONSTANTS.PAGINATION.MAX_LIMIT)
  limit?: number;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(Object.values(TEMPLATE_CONSTANTS.TEMPLATE_CATEGORIES))
  category?: string;

  @IsOptional()
  @IsEnum(Object.values(TEMPLATE_CONSTANTS.TEMPLATE_TYPES))
  type?: string;

  @IsOptional()
  @IsString()
  language?: string;

  @IsOptional()
  @IsEnum(Object.values(TEMPLATE_CONSTANTS.TEMPLATE_STATUSES))
  status?: string;

  @IsOptional()
  @IsString()
  waba_id?: string;
}
