import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SupabaseModule } from '../supabase/supabase.module';
import { MetaMediaUploadController } from './meta-media-upload.controller';
import { MetaMediaUploadService } from './meta-media-upload.service';
import { AuthModule } from 'src/auth/auth.module';

/**
 * Meta Media Upload Module
 * 
 * Provides functionality for uploading large files to Meta's social graph
 * using the Resumable Upload API. Supports file uploads with resume capability
 * for interrupted sessions.
 */
@Module({
  imports: [ConfigModule, SupabaseModule, AuthModule],
  controllers: [MetaMediaUploadController],
  providers: [MetaMediaUploadService],
  exports: [MetaMediaUploadService],
})
export class MetaMediaUploadModule {}
