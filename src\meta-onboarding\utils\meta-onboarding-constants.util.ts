import { HttpStatus } from '@nestjs/common';

/**
 * Constants for Meta-Onboarding module
 */
export const META_ONBOARDING_CONSTANTS = {
  // HTTP Status Codes
  HTTP_STATUS: {
    OK: HttpStatus.OK,
    CREATED: HttpStatus.CREATED,
    BAD_REQUEST: HttpStatus.BAD_REQUEST,
    UNAUTHORIZED: HttpStatus.UNAUTHORIZED,
    FORBIDDEN: HttpStatus.FORBIDDEN,
    NOT_FOUND: HttpStatus.NOT_FOUND,
    CONFLICT: HttpStatus.CONFLICT,
    INTERNAL_SERVER_ERROR: HttpStatus.INTERNAL_SERVER_ERROR,
  },

  // Success Messages
  SUCCESS_MESSAGES: {
    CREDENTIALS_CREATED: 'Meta credentials created successfully',
    CREDENTIALS_UPDATED: 'Meta credentials updated successfully',
    CREDENTIALS_DELETED: 'Meta credentials deleted successfully',
    CREDENTIALS_FETCHED: 'Meta credentials retrieved successfully',
    CREDENTIALS_LIST_FETCHED: 'Meta credentials list retrieved successfully',
    WORKSPACE_CREDENTIALS_FETCHED: 'Workspace credentials retrieved successfully',
    USER_CREDENTIALS_FETCHED: 'User credentials retrieved successfully',
  },

  // Error Messages
  ERROR_MESSAGES: {
    USER_NOT_FOUND: 'User not found',
    USER_PROFILE_NOT_FOUND: 'User profile not found',
    WORKSPACE_ACCESS_DENIED: 'You do not have access to this workspace',
    CREDENTIALS_NOT_FOUND: 'Meta credentials not found',
    DUPLICATE_CREDENTIALS: 'Meta credentials already exist for this workspace',
    CREDENTIALS_CREATION_FAILED: 'Failed to create meta credentials',
    CREDENTIALS_UPDATE_FAILED: 'Failed to update meta credentials',
    CREDENTIALS_DELETION_FAILED: 'Failed to delete meta credentials',
    CREDENTIALS_FETCH_FAILED: 'Failed to fetch meta credentials',
    INVALID_CREDENTIALS_DATA: 'Invalid credentials data provided',
    INVALID_WHATSAPP_BUSINESS_ID: 'Invalid WhatsApp Business ID',
    INVALID_PHONE_NUMBER_ID: 'Invalid Phone Number ID',
    INVALID_ACCESS_TOKEN: 'Invalid access token',
    INVALID_STATUS: 'Invalid status value',
    // Database constraint violation errors
    PHONE_NUMBER_ALREADY_EXISTS: 'This phone number is already connected to another workspace. Please use a different phone number or contact support.',
    WHATSAPP_BUSINESS_ACCOUNT_ALREADY_EXISTS: 'This WhatsApp Business Account is already connected to another workspace. Please use a different account or contact support.',
    CREDENTIALS_ALREADY_IN_USE: 'The provided credentials are already in use. Please check your phone number and WhatsApp Business Account ID.',
    INVALID_WORKSPACE_REFERENCE: 'Invalid workspace or user reference. Please try again.',
  },

  // Validation Rules
  VALIDATION: {
    WHATSAPP_BUSINESS_ID_MIN_LENGTH: 1,
    WHATSAPP_BUSINESS_ID_MAX_LENGTH: 50,
    PHONE_NUMBER_ID_MIN_LENGTH: 1,
    PHONE_NUMBER_ID_MAX_LENGTH: 50,
    ACCESS_TOKEN_MIN_LENGTH: 10,
    ACCESS_TOKEN_MAX_LENGTH: 1000,
    CHANNEL_NAME_MAX_LENGTH: 100,
  },

  // Default Values
  DEFAULTS: {
    CREDENTIALS_STATUS: 'Active',
    PAGINATION_LIMIT: 10,
    PAGINATION_MAX_LIMIT: 100,
  },

  // Status Values
  STATUS_VALUES: {
    ACTIVE: 'Active',
    INACTIVE: 'Inactive',
  },

  // Database Tables
  TABLES: {
    META_CREDENTIALS: 'automate_whatsapp_meta_credentials',
  },
} as const;
