import { Response } from 'express';

/**
 * Standard response interface for consistent API responses
 */
export interface StandardTagsResponse {
  status: 'success' | 'error';
  code: number;
  message: string;
  data?: any;
  error?: any;
  timestamp: string;
}

/**
 * Tags-specific response data interfaces
 */
export interface TagsResponseData {
  tag?: any;
  tags?: any[];
}

/**
 * Utility class for creating consistent tags responses
 */
export class TagsResponseUtil {
  /**
   * Creates a success response with tags data
   */
  static createSuccessResponse(
    data: TagsResponseData | any,
    message: string = 'Operation completed successfully',
    code: number = 200
  ): StandardTagsResponse {
    return {
      status: 'success',
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates an error response
   */
  static createErrorResponse(
    message: string,
    code: number = 400,
    error?: any
  ): StandardTagsResponse {
    return {
      status: 'error',
      code,
      message,
      error: error?.message || error,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates a duplicate error response
   */
  static createDuplicateErrorResponse(
    message: string,
    code: number = 409
  ): StandardTagsResponse {
    return {
      status: 'error',
      code,
      message,
      error: 'Duplicate entry',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates a validation error response
   */
  static createValidationErrorResponse(
    message: string,
    details?: any,
    code: number = 400
  ): StandardTagsResponse {
    return {
      status: 'error',
      code,
      message,
      error: details,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Sends response to client
   */
  static sendResponse(res: Response, response: StandardTagsResponse): void {
    res.status(response.code).json(response);
  }

  /**
   * Creates tag creation data
   */
  static createTagCreationData(tag: any): TagsResponseData {
    return {
      tag: {
        id: tag._id,
        name: tag.name,
        text_color: tag.text_color,
        background_color: tag.background_color,
        workspaceId: tag.workspaceId,
        createdBy: tag.createdBy,
        createdAt: tag.createdAt,
        updatedAt: tag.updatedAt
      }
    };
  }

  /**
   * Creates tags list data
   */
  static createTagsListData(tags: any[]): TagsResponseData {
    return {
      tags: tags.map(tag => ({
        id: tag._id,
        name: tag.name,
        text_color: tag.text_color,
        background_color: tag.background_color,
        workspaceId: tag.workspaceId,
        createdBy: tag.createdBy,
        createdAt: tag.createdAt,
        updatedAt: tag.updatedAt
      }))
    };
  }
}
