# Custom Fields Module Deployment

## Overview

This document provides comprehensive deployment guidelines for the Custom Fields module, including environment setup, configuration, and deployment strategies.

## Prerequisites

### System Requirements

- **Node.js**: Version 18.x or higher
- **MongoDB**: Version 4.4 or higher
- **Memory**: Minimum 512MB RAM
- **Storage**: Minimum 1GB available space
- **Network**: HTTPS support for production

### Dependencies

- **NestJS**: ^11.0.1
- **Mongoose**: ^8.17.1
- **MongoDB**: ^4.4
- **Supabase**: ^2.51.0
- **Class-validator**: ^0.14.2

## Environment Configuration

### 1. Environment Variables

Create a `.env` file with the following variables:

```bash
# Application Configuration
NODE_ENV=production
PORT=3000
API_PREFIX=api/v1

# Database Configuration
MONGODB_URI=********************************:port/database
MONGODB_SSL=true
MONGODB_SSL_VALIDATE=false

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=https://yourdomain.com
CORS_CREDENTIALS=true

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Security Configuration
HELMET_ENABLED=true
TRUST_PROXY=true
```

### 2. Production Environment Variables

```bash
# Production-specific settings
NODE_ENV=production
PORT=3000

# Database (Production)
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
MONGODB_SSL=true
MONGODB_SSL_VALIDATE=true

# Supabase (Production)
SUPABASE_URL=https://your-production-project.supabase.co
SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key

# Security (Production)
JWT_SECRET=your-strong-production-secret
CORS_ORIGIN=https://your-production-domain.com
HELMET_ENABLED=true
TRUST_PROXY=true

# Monitoring
LOG_LEVEL=warn
SENTRY_DSN=your-sentry-dsn
```

## Database Setup

### 1. MongoDB Configuration

#### Production MongoDB Setup

```javascript
// mongodb.config.js
module.exports = {
  uri: process.env.MONGODB_URI,
  options: {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    ssl: process.env.MONGODB_SSL === 'true',
    sslValidate: process.env.MONGODB_SSL_VALIDATE === 'true',
    retryWrites: true,
    w: 'majority',
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    bufferMaxEntries: 0,
    bufferCommands: false,
  },
};
```

#### Database Indexes

```javascript
// Ensure proper indexes are created
db.customfields.createIndex({ "workspaceId": 1, "label": 1 }, { unique: true });
db.customfields.createIndex({ "workspaceId": 1, "createdAt": -1 });
db.customfields.createIndex({ "createdBy": 1 });
```

### 2. Database Migration

```typescript
// migration.ts
import { MongoClient } from 'mongodb';

async function runMigrations() {
  const client = new MongoClient(process.env.MONGODB_URI);
  
  try {
    await client.connect();
    const db = client.db();
    
    // Create indexes
    await db.collection('customfields').createIndex(
      { workspaceId: 1, label: 1 },
      { unique: true }
    );
    
    console.log('Migration completed successfully');
  } finally {
    await client.close();
  }
}

runMigrations().catch(console.error);
```

## Docker Deployment

### 1. Dockerfile

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Copy built application
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# Switch to non-root user
USER nestjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node dist/health-check.js

# Start application
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/main.js"]
```

### 2. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/customfields
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongodb
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  mongodb:
    image: mongo:4.4
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongodb_data:
  redis_data:
```

### 3. Nginx Configuration

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:3000;
    }

    server {
        listen 80;
        server_name yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        location / {
            proxy_pass http://app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        location /health {
            proxy_pass http://app/health;
            access_log off;
        }
    }
}
```

## Kubernetes Deployment

### 1. Deployment Manifest

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: custom-fields-api
  labels:
    app: custom-fields-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: custom-fields-api
  template:
    metadata:
      labels:
        app: custom-fields-api
    spec:
      containers:
      - name: custom-fields-api
        image: your-registry/custom-fields-api:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: custom-fields-secrets
              key: mongodb-uri
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: custom-fields-secrets
              key: supabase-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: custom-fields-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 2. Service Manifest

```yaml
# k8s-service.yaml
apiVersion: v1
kind: Service
metadata:
  name: custom-fields-service
spec:
  selector:
    app: custom-fields-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer
```

### 3. ConfigMap

```yaml
# k8s-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-fields-config
data:
  NODE_ENV: "production"
  PORT: "3000"
  LOG_LEVEL: "info"
  CORS_ORIGIN: "https://yourdomain.com"
```

### 4. Secrets

```yaml
# k8s-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: custom-fields-secrets
type: Opaque
data:
  mongodb-uri: <base64-encoded-mongodb-uri>
  supabase-url: <base64-encoded-supabase-url>
  supabase-anon-key: <base64-encoded-supabase-anon-key>
  jwt-secret: <base64-encoded-jwt-secret>
```

## CI/CD Pipeline

### 1. GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy Custom Fields API

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:4.4
        ports:
          - 27017:27017
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test
      
      - name: Run linting
        run: npm run lint
      
      - name: Build application
        run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Docker Registry
        uses: docker/login-action@v2
        with:
          registry: your-registry.com
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v3
        with:
          context: .
          push: true
          tags: |
            your-registry.com/custom-fields-api:latest
            your-registry.com/custom-fields-api:${{ github.sha }}
      
      - name: Deploy to Kubernetes
        uses: azure/k8s-deploy@v1
        with:
          manifests: |
            k8s-deployment.yaml
            k8s-service.yaml
            k8s-configmap.yaml
          images: |
            your-registry.com/custom-fields-api:${{ github.sha }}
```

### 2. Build Scripts

```bash
#!/bin/bash
# build.sh

set -e

echo "Building Custom Fields API..."

# Install dependencies
npm ci

# Run tests
npm run test

# Run linting
npm run lint

# Build application
npm run build

# Build Docker image
docker build -t custom-fields-api:latest .

echo "Build completed successfully!"
```

## Monitoring and Logging

### 1. Health Check Endpoint

```typescript
// health-check.controller.ts
import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService, MongooseHealthIndicator } from '@nestjs/terminus';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private mongoose: MongooseHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.mongoose.pingCheck('mongodb'),
    ]);
  }
}
```

### 2. Logging Configuration

```typescript
// logger.config.ts
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';

export const loggerConfig = WinstonModule.createLogger({
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.colorize(),
        winston.format.simple(),
      ),
    }),
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
      ),
    }),
    new winston.transports.File({
      filename: 'logs/combined.log',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
      ),
    }),
  ],
});
```

### 3. Prometheus Metrics

```typescript
// metrics.service.ts
import { Injectable } from '@nestjs/common';
import { register, Counter, Histogram } from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly customFieldCreatedCounter = new Counter({
    name: 'custom_fields_created_total',
    help: 'Total number of custom fields created',
    labelNames: ['workspace_id'],
  });

  private readonly customFieldRequestDuration = new Histogram({
    name: 'custom_fields_request_duration_seconds',
    help: 'Duration of custom fields requests',
    labelNames: ['method', 'route', 'status_code'],
  });

  constructor() {
    register.registerMetric(this.customFieldCreatedCounter);
    register.registerMetric(this.customFieldRequestDuration);
  }

  incrementCustomFieldCreated(workspaceId: number) {
    this.customFieldCreatedCounter.inc({ workspace_id: workspaceId.toString() });
  }

  observeRequestDuration(method: string, route: string, statusCode: number, duration: number) {
    this.customFieldRequestDuration.observe(
      { method, route, status_code: statusCode.toString() },
      duration,
    );
  }
}
```

## Security Configuration

### 1. Security Headers

```typescript
// security.config.ts
import helmet from 'helmet';

export const securityConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
});
```

### 2. Rate Limiting

```typescript
// rate-limit.config.ts
import { ThrottlerModule } from '@nestjs/throttler';

export const rateLimitConfig = ThrottlerModule.forRoot({
  ttl: 60,
  limit: 100,
});
```

## Backup and Recovery

### 1. Database Backup

```bash
#!/bin/bash
# backup.sh

# MongoDB backup
mongodump --uri="$MONGODB_URI" --out="/backup/$(date +%Y%m%d_%H%M%S)"

# Compress backup
tar -czf "/backup/$(date +%Y%m%d_%H%M%S).tar.gz" "/backup/$(date +%Y%m%d_%H%M%S)"

# Upload to cloud storage
aws s3 cp "/backup/$(date +%Y%m%d_%H%M%S).tar.gz" s3://your-backup-bucket/
```

### 2. Recovery Script

```bash
#!/bin/bash
# restore.sh

# Download from cloud storage
aws s3 cp s3://your-backup-bucket/backup.tar.gz /restore/

# Extract backup
tar -xzf /restore/backup.tar.gz -C /restore/

# Restore database
mongorestore --uri="$MONGODB_URI" /restore/backup/
```

## Performance Optimization

### 1. Caching Strategy

```typescript
// cache.service.ts
import { Injectable } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';

@Injectable()
export class CacheService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: any) {}

  async getCustomFields(workspaceId: number) {
    const cacheKey = `custom-fields:${workspaceId}`;
    return await this.cacheManager.get(cacheKey);
  }

  async setCustomFields(workspaceId: number, data: any, ttl = 300) {
    const cacheKey = `custom-fields:${workspaceId}`;
    await this.cacheManager.set(cacheKey, data, ttl);
  }
}
```

### 2. Database Optimization

```typescript
// database optimization
// Ensure proper indexes
db.customfields.createIndex({ "workspaceId": 1, "label": 1 }, { unique: true });
db.customfields.createIndex({ "workspaceId": 1, "createdAt": -1 });
db.customfields.createIndex({ "createdBy": 1 });

// Use lean queries for read operations
const customFields = await this.customFieldModel
  .find({ workspaceId })
  .lean()
  .sort({ createdAt: -1 });
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check MongoDB URI format
   - Verify network connectivity
   - Check authentication credentials

2. **Authentication Issues**
   - Verify JWT secret configuration
   - Check Supabase configuration
   - Validate token expiration settings

3. **Performance Issues**
   - Check database indexes
   - Monitor memory usage
   - Review query performance

### Debug Commands

```bash
# Check application logs
docker logs custom-fields-api

# Check database connection
mongo $MONGODB_URI --eval "db.adminCommand('ping')"

# Check application health
curl http://localhost:3000/health

# Monitor resource usage
docker stats custom-fields-api
```

This deployment guide ensures a robust, scalable, and secure deployment of the Custom Fields module.
