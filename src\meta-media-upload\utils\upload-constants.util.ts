/**
 * Constants for Meta Media Upload operations
 */
export const UPLOAD_CONSTANTS = {
  // Meta API Configuration
  META_API_BASE_URL: 'https://graph.facebook.com/v23.0',
  
  // File Size Limits
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB in bytes
  MIN_FILE_SIZE: 1, // 1 byte minimum
  
  // Supported File Types
  SUPPORTED_FILE_TYPES: {
    PDF: 'application/pdf',
    JPEG: 'image/jpeg',
    JPG: 'image/jpg',
    PNG: 'image/png',
    MP4: 'video/mp4',
  },
  
  // File Extensions
  SUPPORTED_EXTENSIONS: ['pdf', 'jpg', 'jpeg', 'png', 'mp4'],
  
  // Success Messages
  SUCCESS_MESSAGES: {
    SESSION_CREATED: 'Upload session created successfully',
    FILE_UPLOADED: 'File uploaded successfully',
    UPLOAD_RESUMED: 'Upload session status retrieved successfully',
    UPLOAD_COMPLETED: 'File upload completed successfully',
  },
  
  // Error Messages
  ERROR_MESSAGES: {
    INVALID_FILE_TYPE: 'Invalid file type. Supported types: PDF, JPEG, JPG, PNG, MP4',
    FILE_TOO_LARGE: 'File size exceeds maximum limit of 100MB',
    INVALID_FILE_SIZE: 'Invalid file size',
    INVALID_FILE_NAME: 'Invalid file name',
    INVALID_SESSION_ID: 'Invalid upload session ID',
    SESSION_NOT_FOUND: 'Upload session not found',
    UPLOAD_FAILED: 'File upload failed',
    META_API_ERROR: 'Meta API error occurred',
    ACCESS_TOKEN_REQUIRED: 'Meta access token is required',
    INVALID_OFFSET: 'Invalid file offset value',
    UPLOAD_INTERRUPTED: 'Upload session was interrupted',
  },
  
  // Meta API Error Codes
  META_ERROR_CODES: {
    INVALID_TOKEN: 190,
    PERMISSION_DENIED: 200,
    INVALID_PARAMETER: 100,
    TEMPORARY_ISSUE: 368,
    SESSION_EXPIRED: 190,
  },
  
  // HTTP Status Codes
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500,
  },
  
  // Upload Session Configuration
  SESSION_CONFIG: {
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 second
  },
};
