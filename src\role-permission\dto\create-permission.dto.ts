import { Is<PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>E<PERSON>, <PERSON><PERSON>eng<PERSON>, Max<PERSON>ength } from 'class-validator';

/**
 * DTO for creating a permission
 */
export class CreatePermissionDto {
  @IsString({ message: 'Permission name must be a string' })
  @IsNotEmpty({ message: 'Permission name is required' })
  @MinLength(1, { message: 'Permission name must be at least 1 character long' })
  @MaxLength(100, { message: 'Permission name must not exceed 100 characters' })
  name: string;

  @IsString({ message: 'Permission resource must be a string' })
  @IsNotEmpty({ message: 'Permission resource is required' })
  @MaxLength(100, { message: 'Permission resource must not exceed 100 characters' })
  resource: string;

  @IsString({ message: 'Permission action must be a string' })
  @IsNotEmpty({ message: 'Permission action is required' })
  @MaxLength(50, { message: 'Permission action must not exceed 50 characters' })
  action: string;

  @IsOptional()
  @IsString({ message: 'Permission description must be a string' })
  @MaxLength(500, { message: 'Permission description must not exceed 500 characters' })
  description?: string;

  @IsOptional()
  @IsEnum(['active', 'inactive'], { message: 'Permission status must be either "active" or "inactive"' })
  status?: 'active' | 'inactive';
}
