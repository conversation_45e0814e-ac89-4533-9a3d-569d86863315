import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export type SegmentDocument = HydratedDocument<Segment>;

export type SegmentRule = {
  field: string; // e.g., firstName, lastName, chatName, email, subscribed, tagsId, phoneNumber, countryCode, custom.<path>
  operator: 'equals' | 'notEquals' | 'contains' | 'notContains' | 'in' | 'notIn' | 'exists' | 'startsWith' | 'endsWith' | 'regex' | 'hasAnyTag' | 'hasAllTags';
  value?: any; // depends on operator; in/hasAnyTag supports array
};

@Schema({ timestamps: true })
export class Segment {
  @Prop({ required: true })
  name: string;

  @Prop()
  description?: string;

  // New flexible rule-based filtering
  @Prop({ type: [{ field: String, operator: String, value: {} }] })
  rules?: SegmentRule[];

  // How to combine rules: all (AND) or any (OR)
  @Prop({ type: String, enum: ['all', 'any'], default: 'all' })
  match?: 'all' | 'any';

  // Legacy single condition support (optional, for backward compatibility)
  @Prop({ type: Object })
  condition?: any;

  @Prop({ type: String, required: true })
  createdBy: string;

  @Prop({ type: Number, required: true })
  workspaceId: number;
}

export const SegmentSchema = SchemaFactory.createForClass(Segment);
SegmentSchema.index({ workspaceId: 1, name: 1 }, { unique: true });


