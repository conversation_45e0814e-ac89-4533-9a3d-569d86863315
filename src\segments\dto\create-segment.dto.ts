import { IsString, IsNotEmpty, IsOptional, IsArray, IsEnum, MinLength, MaxLength, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for segment rule
 */
export class SegmentRuleDto {
  @IsString({ message: 'Field must be a string' })
  @IsNotEmpty({ message: 'Field is required' })
  field: string;

  @IsString({ message: 'Operator must be a string' })
  @IsNotEmpty({ message: 'Operator is required' })
  @IsEnum(['equals', 'notEquals', 'contains', 'notContains', 'in', 'notIn', 'exists', 'startsWith', 'endsWith', 'regex', 'hasAnyTag', 'hasAllTags'], {
    message: 'Operator must be one of: equals, notEquals, contains, notContains, in, notIn, exists, startsWith, endsWith, regex, hasAnyTag, hasAllTags'
  })
  operator: string;

  @IsOptional()
  value?: any;
}

/**
 * DTO for creating a segment
 */
export class CreateSegmentDto {
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  @MinLength(1, { message: 'Name must be at least 1 character long' })
  @MaxLength(100, { message: 'Name must not exceed 100 characters' })
  name: string;

  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;

  @IsOptional()
  @IsArray({ message: 'Rules must be an array' })
  @ValidateNested({ each: true })
  @Type(() => SegmentRuleDto)
  rules?: SegmentRuleDto[];

  @IsOptional()
  @IsEnum(['all', 'any'], { message: 'Match must be either "all" or "any"' })
  match?: 'all' | 'any';

  @IsOptional()
  @IsObject({ message: 'Condition must be an object' })
  condition?: any;
}
