# 🏗️ Auth Module Architecture

## 📋 Overview
This document describes the architecture, design patterns, and technical implementation of the auth module.

## 🎯 Design Principles

### 1. **Separation of Concerns**
- **Controller**: Handles HTTP requests/responses
- **Service**: Contains business logic
- **Guard**: Manages authentication/authorization
- **Utils**: Reusable utility functions
- **DTOs**: Data validation and transfer

### 2. **Single Responsibility Principle**
Each class has one clear responsibility:
- `AuthService`: Authentication business logic
- `AuthController`: HTTP endpoint handling
- `AuthGuard`: Request authentication
- `AuthResponseUtil`: Response formatting
- `AuthValidationUtil`: Input validation

### 3. **Dependency Injection**
All dependencies are injected through NestJS DI container:
```typescript
constructor(
  private readonly supabaseService: SupabaseService,
) {}
```

## 🏛️ Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    Auth Module                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Controller  │  │   Service   │  │    Guard    │        │
│  │             │  │             │  │             │        │
│  │ • signUp()  │  │ • signUp()  │  │ • canActivate() │    │
│  │ • signIn()  │  │ • signIn()  │  │ • validateToken() │   │
│  │ • signOut() │  │ • signOut() │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    DTOs     │  │    Utils    │  │  Constants  │        │
│  │             │  │             │  │             │        │
│  │ • SignUpDto │  │ • Response  │  │ • Messages  │        │
│  │ • SignInDto │  │ • Validation│  │ • Defaults  │        │
│  │ • UpdateDto │  │ • Constants │  │ • ErrorCodes│        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                External Dependencies                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  Supabase   │  │   Express   │  │  Class-Val  │        │
│  │   Service   │  │   Response  │  │  Validator  │        │
│  │             │  │             │  │             │        │
│  │ • Auth API  │  │ • HTTP Res  │  │ • DTO Valid │        │
│  │ • Database  │  │ • Status    │  │ • Messages  │        │
│  │ • Sessions  │  │ • JSON      │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Component Details

### **1. AuthController**
**Purpose**: HTTP request/response handling
**Responsibilities**:
- Route HTTP requests to appropriate service methods
- Handle request validation through DTOs
- Format and send HTTP responses
- Extract tokens from request headers

**Key Methods**:
```typescript
@Post('signup')
async signUp(@Body() signUpDto: SignUpDto)

@Post('signin') 
async signIn(@Body() signInDto: SignInDto, @Res() res: Response)

@Get('profile')
@UseGuards(AuthGuard)
async getProfile(@Request() req: any)
```

### **2. AuthService**
**Purpose**: Business logic implementation
**Responsibilities**:
- User registration and authentication
- Token management and validation
- Profile management
- Password operations
- Integration with Supabase

**Key Methods**:
```typescript
async signUp(signUpDto: SignUpDto): Promise<any>
async signIn(signInDto: SignInDto, res: any): Promise<void>
async validateToken(token: string): Promise<any>
async updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<any>
```

### **3. AuthGuard**
**Purpose**: Request authentication
**Responsibilities**:
- Validate JWT tokens
- Extract user information from tokens
- Protect routes that require authentication
- Handle authentication errors

**Implementation**:
```typescript
async canActivate(context: ExecutionContext): Promise<boolean> {
  const request = context.switchToHttp().getRequest();
  const token = this.extractTokenFromRequest(request);
  const user = await this.authService.validateToken(token);
  request.user = user;
  return true;
}
```

### **4. Utility Classes**

#### **AuthResponseUtil**
**Purpose**: Standardized response formatting
```typescript
static createSuccessResponse(data: any, message: string): StandardAuthResponse
static createErrorResponse(message: string, code: number): StandardAuthResponse
static sendResponse(res: Response, response: StandardAuthResponse): void
```

#### **AuthValidationUtil**
**Purpose**: Input validation helpers
```typescript
static validateEmail(email: string): void
static validatePassword(password: string): void
static validateToken(token: string): string
```

#### **AUTH_CONSTANTS**
**Purpose**: Centralized configuration
```typescript
DEFAULTS: { LANGUAGE: 'en', TIME_ZONE: 'Asia/Kolkata' }
ERROR_MESSAGES: { SIGNUP_FAILED: 'Signup failed...' }
SUCCESS_MESSAGES: { SIGNUP_SUCCESS: 'User registered...' }
```

## 🔄 Data Flow

### **1. User Registration Flow**
```
Client Request → Controller → Service → Supabase → Database
                ↓
Client Response ← Controller ← Service ← Supabase ← Database
```

### **2. User Authentication Flow**
```
Client Request → Controller → Service → Supabase Auth
                ↓
Client Response ← Controller ← Service ← Supabase Auth
```

### **3. Protected Route Flow**
```
Client Request → Guard → Service → Controller → Response
                ↓
Token Validation → User Extraction → Route Access
```

## 🛡️ Security Features

### **1. Input Validation**
- DTO-based validation using class-validator
- Email format validation
- Password strength requirements
- Phone number format validation

### **2. Authentication**
- JWT token-based authentication
- Token expiration handling
- Refresh token mechanism
- Secure token extraction

### **3. Error Handling**
- Consistent error response format
- No sensitive information leakage
- Proper HTTP status codes
- Detailed logging for debugging

## 📊 Performance Considerations

### **1. Database Optimization**
- Efficient Supabase queries
- Minimal data fetching
- Connection pooling through Supabase

### **2. Response Optimization**
- Standardized response format
- Minimal payload size
- Efficient JSON serialization

### **3. Caching Strategy**
- Token validation caching (handled by Supabase)
- User profile caching (future enhancement)

## 🔧 Configuration

### **Environment Variables**
```typescript
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
JWT_SECRET=your_jwt_secret
```

### **Module Configuration**
```typescript
@Module({
  imports: [SupabaseModule],
  controllers: [AuthController],
  providers: [AuthService, AuthGuard],
  exports: [AuthService, AuthGuard],
})
```

## 🧪 Testing Strategy

### **1. Unit Tests**
- Service method testing
- Utility function testing
- DTO validation testing

### **2. Integration Tests**
- Controller endpoint testing
- Guard functionality testing
- End-to-end authentication flow

### **3. Mock Strategy**
- Supabase service mocking
- Request/response mocking
- Token validation mocking

## 🚀 Future Enhancements

### **1. Planned Features**
- Multi-factor authentication (MFA)
- OAuth provider integration
- Session management
- Audit logging

### **2. Performance Improvements**
- Redis caching
- Database query optimization
- Response compression

### **3. Security Enhancements**
- Rate limiting
- IP whitelisting
- Advanced token management
