# Template Module

A comprehensive WhatsApp template management system with Meta API integration, AI-powered generation, and voice-to-template conversion capabilities.

## Features

- ✅ **Template CRUD Operations**: Create, read, update, and delete templates
- ✅ **Meta API Integration**: Sync templates with WhatsApp Business API
- ✅ **Direct Meta Format API**: Create templates using Meta's exact format (NEW!)
- ✅ **AI Template Generation**: Generate templates using AI from text prompts
- ✅ **Voice-to-Template**: Convert audio files to templates using speech-to-text
- ✅ **Draft Templates**: Create templates without Meta submission
- ✅ **Workspace Management**: Multi-tenant template organization
- ✅ **Comprehensive Validation**: Input validation and error handling
- ✅ **Standardized Responses**: Consistent API response format

## Quick Start

### Installation

```bash
# Install dependencies
npm install

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration

# Run database migrations
npm run db:migrate

# Start development server
npm run start:dev
```

### Basic Usage

#### Create a Template

```typescript
import { TemplateService } from './template.service';

const templateService = new TemplateService();

const template = await templateService.createTemplate({
  name: 'appointment_confirmation',
  description: 'Confirm customer appointments',
  category: 'UTILITY',
  language: 'en',
  waba_id: '123456789',
  components: [
    {
      type: 'HEADER',
      format: 'TEXT',
      text: '{{1}}',
      example: {
        header_text: ['Appointment Confirmation']
      }
    },
    {
      type: 'BODY',
      text: 'Hi {{1}}, your appointment is confirmed for {{2}}.',
      example: {
        body_text: [['John', '2:00 PM']]
      }
    }
  ]
}, req);
```

#### Generate AI Template

```typescript
const aiTemplate = await templateService.generateAiTemplate({
  prompt: 'Create a welcome message for new customers',
  waba_id: '123456789',
  category: 'MARKETING'
}, req);
```

#### Sync with Meta

```typescript
const syncResult = await templateService.syncAllWithMeta('123456789', req);
```

## API Endpoints

### Template Management
- `POST /templates` - Create template
- `POST /templates/draft` - Create draft template
- `GET /templates` - Get user templates
- `GET /templates/workspace/:id` - Get workspace templates
- `GET /templates/:id` - Get template by ID
- `PUT /templates/:id` - Update template
- `DELETE /templates/:wabaId/template/:templateId` - Delete template

### Meta Integration
- `GET /templates/meta/sync/waba/:wabaId` - Sync all templates
- `POST /templates/:id/sync` - Sync single template
- `GET /templates/meta/templates` - Get Meta templates

### AI Generation
- `POST /templates/ai/generate` - Generate AI template
- `POST /templates/voice/generate` - Generate from voice

## Configuration

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...

# Meta API
META_APP_ID=...
META_APP_SECRET=...

# AI Service
OPENAI_API_KEY=...

# File Storage
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
AWS_S3_BUCKET=...
```

### Template Types

- `text` - Text-only templates
- `image` - Templates with images
- `video` - Templates with videos
- `audio` - Templates with audio
- `document` - Templates with documents
- `location` - Location-based templates
- `contact` - Contact sharing templates
- `interactive` - Interactive templates with buttons

### Template Categories

- `MARKETING` - Marketing and promotional content
- `UTILITY` - Utility and transactional content
- `AUTHENTICATION` - Authentication and verification

## API Endpoints

### Template Management

| Method | Endpoint | Description | Format |
|--------|----------|-------------|---------|
| `POST` | `/templates` | Create template using Meta format | Meta Format |
| `GET` | `/templates` | List templates | Query Parameters |
| `GET` | `/templates/:id` | Get template by ID | Path Parameter |
| `PUT` | `/templates/:id` | Update template | Update DTO |
| `DELETE` | `/templates/:id` | Delete template | Path Parameter |
| `POST` | `/templates/ai` | Generate AI template | AI Prompt |
| `POST` | `/templates/voice` | Convert voice to template | Audio Upload |
| `POST` | `/templates/draft` | Create draft template | Template DTO |

### Meta Template Format (Recommended)

```json
{
  "name": "appointment_confirmation",
  "category": "UTILITY",
  "language": "en",
  "waba_id": "123456789",
  "components": [
    {
      "type": "HEADER",
      "format": "TEXT",
      "text": "{{1}}",
      "example": {
        "header_text": ["Appointment Confirmation"]
      }
    },
    {
      "type": "BODY",
      "text": "Hi {{1}}, your appointment is confirmed for {{2}}.",
      "example": {
        "body_text": [["John", "2:00 PM"]]
      }
    }
  ]
}
```

## Examples

### Creating a Template (Meta Format)

```json
{
  "name": "order_confirmation_meta",
  "category": "UTILITY",
  "language": "en",
  "waba_id": "123456789",
  "components": [
    {
      "type": "BODY",
      "text": "Hi {{1}}, your order #{{2}} has been confirmed and will be delivered on {{3}}.",
      "example": {
        "body_text": [["John", "12345", "Tomorrow"]]
      }
    }
  ]
}
```

### Creating an Interactive Template

```json
{
  "name": "product_catalog",
  "type": "interactive",
  "content": "Check out our latest products!",
  "buttons": [
    {
      "text": "View Products",
      "type": "url",
      "url": "https://example.com/products"
    },
    {
      "text": "Contact Support",
      "type": "phone_number",
      "phone_number": "+1234567890"
    }
  ],
  "language": "en",
  "category": "MARKETING",
  "waba_id": "123456789"
}
```

### AI Template Generation

```json
{
  "prompt": "Create a welcome message for new e-commerce customers",
  "waba_id": "123456789",
  "category": "MARKETING",
  "language": "en"
}
```

## Error Handling

The module provides comprehensive error handling with standardized error responses:

```json
{
  "status": "error",
  "code": 400,
  "message": "Template name is required",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Common Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `404` - Not Found (template not found)
- `409` - Conflict (duplicate template)
- `500` - Internal Server Error

## Template API Features

### Key Benefits

| Feature | Description |
|---------|-------------|
| **Meta Compliance** | ✅ 100% Meta API compliant |
| **Direct Submission** | ✅ No backend conversion needed |
| **Error Reduction** | ✅ Minimal conversion errors |
| **Performance** | ✅ Fast template creation |
| **Maintenance** | ✅ Simple and clean |
| **Debugging** | ✅ Clear Meta format |
| **Future Proof** | ✅ Always up-to-date with Meta |

### Benefits

1. **Direct Submission**: Components submitted directly to Meta API
2. **Error Reduction**: Eliminates conversion-related errors
3. **Better Performance**: Faster template creation
4. **Easier Debugging**: Clear Meta API format
5. **Future Compatibility**: Always up-to-date with Meta changes
6. **Simplified Frontend**: Direct Meta format usage

## Testing

```bash
# Run all tests
npm test

# Run unit tests
npm run test:unit

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e

# Run with coverage
npm run test:coverage
```

## Documentation

- [Complete API Documentation](docs/API_ENDPOINTS.md)
- [Architecture Overview](docs/ARCHITECTURE.md)
- [Security Guidelines](docs/SECURITY.md)
- [Testing Documentation](docs/TESTING.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Contact the development team

## Changelog

### v1.0.0
- Initial release
- Template CRUD operations
- Meta API integration
- AI template generation
- Voice-to-template conversion
- Comprehensive documentation
- Full test coverage
