{"info": {"name": "Template API Collection", "description": "Complete collection for Template management API endpoints", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "your_jwt_token_here", "type": "string"}, {"key": "templateId", "value": "template_id_here", "type": "string"}, {"key": "wabaId", "value": "1159844736009453", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "Template Management", "item": [{"name": "Create Template - Text with Variables", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"appointment_confirmation\",\n  \"description\": \"Confirm customer appointments\",\n  \"category\": \"UTILITY\",\n  \"language\": \"en\",\n  \"waba_id\": \"{{wabaId}}\",\n  \"components\": [\n    {\n      \"type\": \"HEADER\",\n      \"format\": \"TEXT\",\n      \"text\": \"{{1}}\",\n      \"example\": {\n        \"header_text\": [\"Appointment Confirmation\"]\n      }\n    },\n    {\n      \"type\": \"BODY\",\n      \"text\": \"Hi {{1}}, your appointment is confirmed for {{2}}. Please contact us if you need to reschedule.\",\n      \"example\": {\n        \"body_text\": [[\"John\", \"2:00 PM\"]]\n      }\n    },\n    {\n      \"type\": \"FOOTER\",\n      \"text\": \"Thank you for choosing us!\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/templates", "host": ["{{baseUrl}}"], "path": ["templates"]}}}, {"name": "Create Template - Interactive with But<PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"product_catalog\",\n  \"description\": \"Show product catalog with interactive buttons\",\n  \"category\": \"MARKETING\",\n  \"language\": \"en\",\n  \"waba_id\": \"{{wabaId}}\",\n  \"components\": [\n    {\n      \"type\": \"HEADER\",\n      \"format\": \"TEXT\",\n      \"text\": \"{{1}}\",\n      \"example\": {\n        \"header_text\": [\"New Products Available\"]\n      }\n    },\n    {\n      \"type\": \"BODY\",\n      \"text\": \"Check out our latest products! {{1}}\",\n      \"example\": {\n        \"body_text\": [[\"Click the buttons below to explore\"]]\n      }\n    },\n    {\n      \"type\": \"BUTTONS\",\n      \"buttons\": [\n        {\n          \"type\": \"QUICK_REPLY\",\n          \"text\": \"View Products\"\n        },\n        {\n          \"type\": \"URL\",\n          \"text\": \"Visit Website\",\n          \"url\": \"https://example.com\"\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/templates", "host": ["{{baseUrl}}"], "path": ["templates"]}}}, {"name": "Create Template - <PERSON> Header", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"product_announcement\",\n  \"description\": \"Announce new product with image\",\n  \"category\": \"MARKETING\",\n  \"language\": \"en\",\n  \"waba_id\": \"{{wabaId}}\",\n  \"components\": [\n    {\n      \"type\": \"HEADER\",\n      \"format\": \"IMAGE\",\n      \"text\": \"{{1}}\",\n      \"example\": {\n        \"header_text\": [\"New Product Launch\"]\n      }\n    },\n    {\n      \"type\": \"BODY\",\n      \"text\": \"Introducing our latest product: {{1}}! Available now for just {{2}}.\",\n      \"example\": {\n        \"body_text\": [[\"Smartphone X\", \"$599\"]]\n      }\n    },\n    {\n      \"type\": \"FOOTER\",\n      \"text\": \"Limited time offer!\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/templates", "host": ["{{baseUrl}}"], "path": ["templates"]}}}, {"name": "Get Template by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/templates/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["templates", "{{templateId}}"]}}}, {"name": "Update Template", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"updated_appointment_confirmation\",\n  \"description\": \"Updated appointment confirmation template\",\n  \"components\": [\n    {\n      \"type\": \"HEADER\",\n      \"format\": \"TEXT\",\n      \"text\": \"{{1}}\",\n      \"example\": {\n        \"header_text\": [\"Updated Appointment Confirmation\"]\n      }\n    },\n    {\n      \"type\": \"BODY\",\n      \"text\": \"Hi {{1}}, your appointment is confirmed for {{2}}. Please contact us if you need to reschedule.\",\n      \"example\": {\n        \"body_text\": [[\"John\", \"3:00 PM\"]]\n      }\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/templates/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["templates", "{{templateId}}"]}}}, {"name": "Delete Template", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/templates/{{templateId}}?waba_id={{wabaId}}", "host": ["{{baseUrl}}"], "path": ["templates", "{{templateId}}"], "query": [{"key": "waba_id", "value": "{{wabaId}}"}]}}}, {"name": "List Templates - All", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/templates", "host": ["{{baseUrl}}"], "path": ["templates"]}}}, {"name": "List Templates - With Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/templates?page=1&limit=10&category=MARKETING&status=APPROVED&language=en", "host": ["{{baseUrl}}"], "path": ["templates"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category", "value": "MARKETING"}, {"key": "status", "value": "APPROVED"}, {"key": "language", "value": "en"}]}}}, {"name": "List Templates - Search", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/templates?search=appointment&ai_generated=true", "host": ["{{baseUrl}}"], "path": ["templates"], "query": [{"key": "search", "value": "appointment"}, {"key": "ai_generated", "value": "true"}]}}}]}, {"name": "AI & Voice Generation", "item": [{"name": "Generate AI Template - Welcome Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"prompt\": \"Create a welcome message for new customers joining our e-commerce platform. Include a header, body with customer name variable, and footer with call-to-action.\",\n  \"waba_id\": \"{{wabaId}}\",\n  \"category\": \"MARKETING\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/templates/ai", "host": ["{{baseUrl}}"], "path": ["templates", "ai"]}}}, {"name": "Generate AI Template - Order Confirmation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"prompt\": \"Create an order confirmation template for customers. Include order number, customer name, and delivery date variables.\",\n  \"waba_id\": \"{{wabaId}}\",\n  \"category\": \"UTILITY\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/templates/ai", "host": ["{{baseUrl}}"], "path": ["templates", "ai"]}}}, {"name": "Generate AI Template - Promotional", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"prompt\": \"Create a promotional template for a flash sale. Include discount percentage, product name variables, and interactive buttons for shopping.\",\n  \"waba_id\": \"{{wabaId}}\",\n  \"category\": \"MARKETING\",\n  \"language\": \"en\"\n}"}, "url": {"raw": "{{baseUrl}}/templates/ai", "host": ["{{baseUrl}}"], "path": ["templates", "ai"]}}}, {"name": "Generate Template from Voice", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/audio/file.mp3"}, {"key": "waba_id", "value": "{{wabaId}}", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/templates/voice", "host": ["{{baseUrl}}"], "path": ["templates", "voice"]}}}]}, {"name": "Meta Integration", "item": [{"name": "Sync Meta Templates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"waba_id\": \"{{wabaId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/templates/meta/sync", "host": ["{{baseUrl}}"], "path": ["templates", "meta", "sync"]}}}, {"name": "<PERSON>a Templates", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/templates/meta", "host": ["{{baseUrl}}"], "path": ["templates", "meta"]}}}, {"name": "Sync Single Template with Meta", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/templates/{{templateId}}/sync", "host": ["{{baseUrl}}"], "path": ["templates", "{{templateId}}", "sync"]}}}]}, {"name": "Error Testing", "item": [{"name": "Create Template - Validation Error", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"\",\n  \"category\": \"INVALID_CATEGORY\",\n  \"components\": []\n}"}, "url": {"raw": "{{baseUrl}}/templates", "host": ["{{baseUrl}}"], "path": ["templates"]}}}, {"name": "Create Template - Duplicate Name", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"duplicate_template\",\n  \"description\": \"This should fail if template already exists\",\n  \"category\": \"UTILITY\",\n  \"language\": \"en\",\n  \"waba_id\": \"{{wabaId}}\",\n  \"components\": [\n    {\n      \"type\": \"BODY\",\n      \"text\": \"This is a test template\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/templates", "host": ["{{baseUrl}}"], "path": ["templates"]}}}, {"name": "Get Template - Not Found", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/templates/non-existent-id", "host": ["{{baseUrl}}"], "path": ["templates", "non-existent-id"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-generate timestamp for unique template names", "const timestamp = new Date().getTime();", "pm.environment.set('timestamp', timestamp);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test script for all requests", "pm.test('Status code is successful', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response has required fields', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('timestamp');", "});", "", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Store template ID for subsequent requests", "if (pm.response.code === 201 && pm.response.json().data?.template?.id) {", "    pm.environment.set('templateId', pm.response.json().data.template.id);", "}"]}}]}