
import { Body, Controller, Post, Req, Res, UseGuards, Get, Put, Param, Delete } from '@nestjs/common';
import { AutomateCardService } from './automate-card.service';


@Controller('public')
export class PublicProfileController {
    constructor(private readonly automateCardService: AutomateCardService) {}

    @Get('/:id')
    async publicCompanyProfile(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.getPublicCompanyProfileById(id, req, res);
    }
    @Get('company/:id')
    async publicusercardProfile(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.getSpecificUserCard(id, req, res);
    }
}
