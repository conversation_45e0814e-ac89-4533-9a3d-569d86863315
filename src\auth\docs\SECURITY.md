# 🔒 Auth Module Security Guide

## 📋 Overview
This document outlines security measures, best practices, and security considerations for the auth module.

## 🛡️ Security Architecture

### **1. Authentication Flow**
```
Client → AuthGuard → AuthService → Supabase → Database
   ↓         ↓           ↓           ↓         ↓
Token    Validate    Business    Auth API   User Data
Extract  Token       Logic       Call       Storage
```

### **2. Security Layers**
- **Input Validation**: DTO-based validation
- **Authentication**: JWT token validation
- **Authorization**: Role-based access control
- **Data Protection**: Encrypted storage
- **Transport Security**: HTTPS/TLS

## 🔐 Authentication Security

### **1. JWT Token Security**

#### **Token Structure**
```typescript
interface JWTPayload {
  sub: string;        // User ID
  email: string;      // User email
  iat: number;        // Issued at
  exp: number;        // Expiration
  aud: string;        // Audience
  iss: string;        // Issuer
}
```

#### **Token Configuration**
```typescript
// JWT Configuration
const JWT_CONFIG = {
  secret: process.env.JWT_SECRET, // Strong secret key
  expiresIn: '1h',                // Short expiration
  algorithm: 'HS256',             // Secure algorithm
  issuer: 'your-app-name',        // Token issuer
  audience: 'your-app-users',     // Token audience
};
```

#### **Token Validation**
```typescript
// auth.guard.ts
async canActivate(context: ExecutionContext): Promise<boolean> {
  const request = context.switchToHttp().getRequest();
  const token = this.extractTokenFromRequest(request);
  
  try {
    // Validate token signature
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: JWT_CONFIG.issuer,
      audience: JWT_CONFIG.audience,
    });
    
    // Check token expiration
    if (decoded.exp < Date.now() / 1000) {
      throw new UnauthorizedException('Token expired');
    }
    
    // Extract user information
    const user = await this.authService.validateToken(token);
    request.user = user;
    
    return true;
  } catch (error) {
    throw new UnauthorizedException('Invalid token');
  }
}
```

### **2. Password Security**

#### **Password Requirements**
```typescript
// auth.dto.ts
export class SignUpDto {
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: 'Password must contain uppercase, lowercase, number, and special character'
  })
  password: string;
}
```

#### **Password Hashing**
```typescript
// Supabase handles password hashing automatically
// Using bcrypt with salt rounds: 12
const hashedPassword = await bcrypt.hash(password, 12);
```

#### **Password Reset Security**
```typescript
// Password reset with secure token
async resetPassword(email: string, redirectUrl: string): Promise<void> {
  // Generate secure reset token
  const resetToken = crypto.randomBytes(32).toString('hex');
  const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  
  // Store token with expiration
  await this.storeResetToken(email, hashedToken, Date.now() + 3600000); // 1 hour
  
  // Send secure email
  await this.sendPasswordResetEmail(email, resetToken, redirectUrl);
}
```

## 🔒 Authorization Security

### **1. Role-Based Access Control (RBAC)**

#### **User Roles**
```typescript
enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  MODERATOR = 'moderator',
  GUEST = 'guest'
}

interface UserPermissions {
  canRead: boolean;
  canWrite: boolean;
  canDelete: boolean;
  canManageUsers: boolean;
}
```

#### **Role Guard Implementation**
```typescript
// role.guard.ts
@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
```

#### **Usage in Controllers**
```typescript
@Controller('admin')
@UseGuards(AuthGuard, RoleGuard)
@Roles(UserRole.ADMIN)
export class AdminController {
  @Get('users')
  async getAllUsers() {
    // Only admins can access
  }
}
```

### **2. Resource-Based Authorization**

#### **Ownership Validation**
```typescript
// ownership.guard.ts
@Injectable()
export class OwnershipGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const resourceId = request.params.id;
    
    // Check if user owns the resource
    return this.checkResourceOwnership(user.id, resourceId);
  }
}
```

## 🛡️ Input Validation Security

### **1. DTO Validation**

#### **Email Validation**
```typescript
@IsEmail({}, { 
  message: 'Please provide a valid email address' 
})
@IsNotEmpty({ message: 'Email is required' })
email: string;
```

#### **Phone Number Validation**
```typescript
@IsNumberString({}, { 
  message: 'Phone number must contain only numbers' 
})
@Matches(/^[0-9]{10,15}$/, { 
  message: 'Phone number must be 10-15 digits' 
})
phoneNumber: string;
```

#### **Name Validation**
```typescript
@IsString({ message: 'Name must be a string' })
@MinLength(2, { message: 'Name must be at least 2 characters' })
@MaxLength(50, { message: 'Name must not exceed 50 characters' })
@Matches(/^[a-zA-Z\s]+$/, { 
  message: 'Name can only contain letters and spaces' 
})
first_name: string;
```

### **2. SQL Injection Prevention**

#### **Parameterized Queries**
```typescript
// Using Supabase client (automatically prevents SQL injection)
const { data, error } = await supabase
  .from('user_profile')
  .select('*')
  .eq('id', userId)  // Safe parameterized query
  .single();
```

#### **Input Sanitization**
```typescript
// Sanitize user input
const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '')  // Remove potential HTML tags
    .replace(/['"]/g, '')  // Remove quotes
    .substring(0, 1000);   // Limit length
};
```

## 🔐 Data Protection

### **1. Sensitive Data Handling**

#### **PII Protection**
```typescript
// Mask sensitive data in logs
const maskEmail = (email: string): string => {
  const [username, domain] = email.split('@');
  const maskedUsername = username.substring(0, 2) + '*'.repeat(username.length - 2);
  return `${maskedUsername}@${domain}`;
};

// Log with masked data
this.logger.log(`User ${maskUser.id} with email ${maskEmail(user.email)} signed in`);
```

#### **Data Encryption**
```typescript
// Encrypt sensitive data before storage
import * as crypto from 'crypto';

const encryptData = (data: string, key: string): string => {
  const cipher = crypto.createCipher('aes-256-cbc', key);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};

const decryptData = (encryptedData: string, key: string): string => {
  const decipher = crypto.createDecipher('aes-256-cbc', key);
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};
```

### **2. Session Management**

#### **Secure Session Configuration**
```typescript
// Session security settings
const sessionConfig = {
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    httpOnly: true,                                // Prevent XSS
    maxAge: 3600000,                              // 1 hour
    sameSite: 'strict',                           // CSRF protection
  },
};
```

## 🚨 Security Headers

### **1. HTTP Security Headers**
```typescript
// main.ts
app.use((req, res, next) => {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Strict transport security
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  
  // Content security policy
  res.setHeader('Content-Security-Policy', "default-src 'self'");
  
  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  next();
});
```

### **2. CORS Configuration**
```typescript
// CORS security settings
app.enableCors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['X-Total-Count'],
});
```

## 🔍 Rate Limiting

### **1. Request Rate Limiting**
```typescript
// rate-limit.middleware.ts
import { ThrottlerModule } from '@nestjs/throttler';

@Module({
  imports: [
    ThrottlerModule.forRoot({
      ttl: 60,        // Time window in seconds
      limit: 10,      // Max requests per window
    }),
  ],
})
export class AppModule {}
```

### **2. Auth-Specific Rate Limiting**
```typescript
// Custom rate limiting for auth endpoints
@Throttle(5, 60)  // 5 attempts per minute
@Post('signin')
async signIn(@Body() signInDto: SignInDto) {
  // Sign in logic
}

@Throttle(3, 300)  // 3 attempts per 5 minutes
@Post('reset-password')
async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
  // Password reset logic
}
```

## 🛡️ Security Monitoring

### **1. Security Event Logging**
```typescript
// security-logger.service.ts
@Injectable()
export class SecurityLoggerService {
  private readonly logger = new Logger(SecurityLoggerService.name);

  logAuthAttempt(email: string, success: boolean, ip: string) {
    this.logger.warn({
      event: 'auth_attempt',
      email: this.maskEmail(email),
      success,
      ip,
      timestamp: new Date().toISOString(),
    });
  }

  logSuspiciousActivity(userId: string, activity: string, metadata: any) {
    this.logger.error({
      event: 'suspicious_activity',
      userId,
      activity,
      metadata,
      timestamp: new Date().toISOString(),
    });
  }
}
```

### **2. Failed Login Protection**
```typescript
// Account lockout after failed attempts
const MAX_LOGIN_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes

async handleFailedLogin(email: string, ip: string) {
  const attempts = await this.getFailedAttempts(email, ip);
  
  if (attempts >= MAX_LOGIN_ATTEMPTS) {
    await this.lockAccount(email, LOCKOUT_DURATION);
    this.securityLogger.logSuspiciousActivity(
      email, 
      'account_locked', 
      { attempts, ip }
    );
  }
}
```

## 🔐 Environment Security

### **1. Environment Variables**
```bash
# .env.production
NODE_ENV=production
JWT_SECRET=your-super-secure-jwt-secret-key-here
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SESSION_SECRET=your-session-secret-key
ENCRYPTION_KEY=your-encryption-key-for-sensitive-data
```

### **2. Secrets Management**
```typescript
// Use AWS Secrets Manager or similar
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';

const getSecret = async (secretName: string): Promise<string> => {
  const client = new SecretsManagerClient({ region: 'us-east-1' });
  const command = new GetSecretValueCommand({ SecretId: secretName });
  const response = await client.send(command);
  return response.SecretString;
};
```

## 🚨 Security Incident Response

### **1. Incident Detection**
```typescript
// Monitor for security incidents
const detectSecurityIncidents = async () => {
  // Check for multiple failed logins
  const failedLogins = await this.getRecentFailedLogins();
  if (failedLogins.length > 10) {
    await this.triggerSecurityAlert('Multiple failed login attempts');
  }
  
  // Check for unusual access patterns
  const unusualAccess = await this.detectUnusualAccess();
  if (unusualAccess.length > 0) {
    await this.triggerSecurityAlert('Unusual access patterns detected');
  }
};
```

### **2. Automated Response**
```typescript
// Automated security responses
const handleSecurityIncident = async (incident: SecurityIncident) => {
  switch (incident.type) {
    case 'brute_force':
      await this.blockIP(incident.ip);
      await this.notifySecurityTeam(incident);
      break;
      
    case 'account_compromise':
      await this.disableAccount(incident.userId);
      await this.forcePasswordReset(incident.userId);
      break;
      
    case 'data_breach':
      await this.rotateSecrets();
      await this.notifyAffectedUsers(incident);
      break;
  }
};
```

## 📋 Security Checklist

### **✅ Pre-Deployment Security Checklist**
- [ ] Strong JWT secret configured
- [ ] HTTPS enabled in production
- [ ] Rate limiting configured
- [ ] Input validation implemented
- [ ] Security headers set
- [ ] CORS properly configured
- [ ] Error messages don't leak sensitive info
- [ ] Logging configured for security events
- [ ] Secrets stored securely
- [ ] Database connections encrypted
- [ ] Password requirements enforced
- [ ] Account lockout implemented
- [ ] Session management secure
- [ ] CSRF protection enabled
- [ ] XSS protection enabled

### **✅ Runtime Security Monitoring**
- [ ] Monitor failed login attempts
- [ ] Track unusual access patterns
- [ ] Monitor for SQL injection attempts
- [ ] Watch for XSS attempts
- [ ] Monitor rate limit violations
- [ ] Track security event logs
- [ ] Monitor system resource usage
- [ ] Check for suspicious API calls
