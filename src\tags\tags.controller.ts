import { 
  Body, 
  Controller, 
  Delete, 
  Get, 
  Param, 
  Post, 
  Put, 
  Req, 
  UseGuards, 
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import { TagsService } from './tags.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { CreateTagDto, UpdateTagDto } from './dto';

/**
 * Refactored TagsController with improved structure and consistent response handling
 */
@Controller('tags')
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  /**
   * Create tag endpoint
   */
  @Post()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async createTag(@Body() createDto: CreateTagDto, @Req() req: any) {
    return await this.tagsService.createTag(createDto, req);
  }

  /**
   * Get all tags for workspace endpoint
   */
  @Get()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getTags(@Req() req: any) {
    return await this.tagsService.getTagsForWorkspace(req);
  }

  /**
   * Get tag by ID endpoint
   */
  @Get(':id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getTag(@Param('id') id: string, @Req() req: any) {
    return await this.tagsService.getTagById(id, req);
  }

  /**
   * Update tag endpoint
   */
  @Put(':id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async updateTag(
    @Param('id') id: string,
    @Body() updateDto: UpdateTagDto,
    @Req() req: any,
  ) {
    return await this.tagsService.updateTag(id, updateDto, req);
  }

  /**
   * Delete tag endpoint
   */
  @Delete(':id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async deleteTag(@Param('id') id: string, @Req() req: any) {
    return await this.tagsService.deleteTag(id, req);
  }
}


