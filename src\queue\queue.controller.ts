import { Controller, Get, Post, Body, Param, HttpCode, HttpStatus, UseGuards } from '@nestjs/common';
import { QueueService } from './queue.service';
import { MessageStatusService } from './message-status.service';
import { AuthGuard } from '../auth/auth.guard';

@Controller('queue')
@UseGuards(AuthGuard)
export class QueueController {
  constructor(
    private readonly queueService: QueueService,
    private readonly messageStatusService: MessageStatusService,
  ) {}

  @Get('stats')
  @HttpCode(HttpStatus.OK)
  async getQueueStats() {
    try {
      const stats = await this.queueService.getQueueStats();
      return {
        status: 'success',
        code: 200,
        message: 'Queue statistics retrieved successfully',
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        code: 500,
        message: 'Failed to retrieve queue statistics',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post('pause/:queueName')
  @HttpCode(HttpStatus.OK)
  async pauseQueue(@Param('queueName') queueName: string) {
    try {
      await this.queueService.pauseQueue(queueName);
      return {
        status: 'success',
        code: 200,
        message: `Queue ${queueName} paused successfully`,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        code: 500,
        message: `Failed to pause queue ${queueName}`,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post('resume/:queueName')
  @HttpCode(HttpStatus.OK)
  async resumeQueue(@Param('queueName') queueName: string) {
    try {
      await this.queueService.resumeQueue(queueName);
      return {
        status: 'success',
        code: 200,
        message: `Queue ${queueName} resumed successfully`,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        code: 500,
        message: `Failed to resume queue ${queueName}`,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post('clear/:queueName')
  @HttpCode(HttpStatus.OK)
  async clearQueue(@Param('queueName') queueName: string) {
    try {
      await this.queueService.clearQueue(queueName);
      return {
        status: 'success',
        code: 200,
        message: `Queue ${queueName} cleared successfully`,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        code: 500,
        message: `Failed to clear queue ${queueName}`,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get('campaign/:campaignId/stats')
  @HttpCode(HttpStatus.OK)
  async getCampaignStats(@Param('campaignId') campaignId: string) {
    try {
      const stats = await this.messageStatusService.getCampaignStats(campaignId);
      return {
        status: 'success',
        code: 200,
        message: 'Campaign statistics retrieved successfully',
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        code: 500,
        message: 'Failed to retrieve campaign statistics',
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get('campaign/:campaignId/status/:contactId')
  @HttpCode(HttpStatus.OK)
  async getMessageStatus(
    @Param('campaignId') campaignId: string,
    @Param('contactId') contactId: string,
  ) {
    try {
      const status = await this.messageStatusService.getMessageStatus(campaignId, contactId);
      return {
        status: 'success',
        code: 200,
        message: 'Message status retrieved successfully',
        data: status,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        code: 500,
        message: 'Failed to retrieve message status',
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get('campaign/:campaignId/statuses')
  @HttpCode(HttpStatus.OK)
  async getCampaignMessageStatuses(@Param('campaignId') campaignId: string) {
    try {
      const statuses = await this.messageStatusService.getCampaignMessageStatuses(campaignId);
      return {
        status: 'success',
        code: 200,
        message: 'Campaign message statuses retrieved successfully',
        data: statuses,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'error',
        code: 500,
        message: 'Failed to retrieve campaign message statuses',
        timestamp: new Date().toISOString(),
      };
    }
  }
}
