# AWS EC2 Deployment Guide

This guide provides step-by-step instructions for deploying the WhatsApp Backend application on AWS EC2 with <PERSON>is and Bull MQ.

## Prerequisites

### AWS EC2 Instance Requirements
- **Instance Type**: t3.medium or larger (minimum 2GB RAM)
- **Operating System**: Ubuntu 20.04 LTS or newer
- **Storage**: Minimum 20GB SSD
- **Security Group**: Configure the following inbound rules:
  - SSH (22) - Your IP only
  - HTTP (8000) - 0.0.0.0/0 (or your specific IPs)
  - Redis Commander (8081) - Your IP only (optional, for debugging)

### Local Requirements
- Git installed
- Access to your environment variables and secrets

## Quick Deployment

### 1. Connect to Your EC2 Instance
```bash
ssh -i your-key.pem ubuntu@your-ec2-public-ip
```

### 2. Clone the Repository
```bash
git clone https://github.com/your-username/automate-whatsapp-backend.git
cd automate-whatsapp-backend
```

### 3. Configure Environment
```bash
# Copy production environment template
cp .env.production .env

# Edit with your production values
nano .env
```

**Required Environment Variables:**
- `REDIS_PASSWORD` - Set a strong password for Redis
- `MONGODB_URI` - Your MongoDB connection string
- `WHATSAPP_ACCESS_TOKEN` - Your WhatsApp Business API token
- `WHATSAPP_PHONE_NUMBER_ID` - Your WhatsApp phone number ID
- `SUPABASE_URL` and `SUPABASE_ANON_KEY` - Your Supabase configuration

### 4. Run Deployment Script
```bash
chmod +x deploy-aws-ec2.sh
./deploy-aws-ec2.sh
```

The script will:
- Install Docker and Docker Compose
- Configure firewall settings
- Build and start all services
- Perform health checks
- Display access URLs

## Manual Deployment

If you prefer manual deployment or need to troubleshoot:

### 1. Install Dependencies
```bash
# Update system
sudo apt-get update -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Log out and back in for group changes
```

### 2. Configure Firewall
```bash
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 8000/tcp  # Application
sudo ufw allow 8081/tcp  # Redis Commander (optional)
sudo ufw --force enable
```

### 3. Start Services
```bash
# For production (Redis Commander disabled)
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# For development (Redis Commander enabled)
docker-compose up -d
```

## Service Management

### Check Service Status
```bash
docker-compose ps
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f app
docker-compose logs -f redis
```

### Restart Services
```bash
docker-compose restart
```

### Stop Services
```bash
docker-compose down
```

### Update Application
```bash
git pull
docker-compose down
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
```

## Health Checks

### Application Health
```bash
curl http://localhost:8000/health
```

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "database": {
    "connected": true,
    "status": "connected"
  },
  "redis": {
    "connected": true,
    "status": "connected"
  }
}
```

### Redis Health
```bash
docker exec whatsapp-redis redis-cli ping
```

## Troubleshooting

### Common Issues

#### 1. Redis Connection Failed
```bash
# Check Redis container
docker logs whatsapp-redis

# Test Redis connection
docker exec whatsapp-redis redis-cli ping

# Check network connectivity
docker exec whatsapp-backend ping redis
```

#### 2. Application Won't Start
```bash
# Check application logs
docker logs whatsapp-backend

# Check environment variables
docker exec whatsapp-backend env | grep REDIS
```

#### 3. Port Already in Use
```bash
# Check what's using the port
sudo netstat -tulpn | grep :8000

# Kill the process if needed
sudo kill -9 <PID>
```

#### 4. Permission Denied
```bash
# Fix Docker permissions
sudo usermod -aG docker $USER
# Log out and back in
```

### Performance Tuning

#### Redis Configuration
For high-traffic applications, consider:
- Increasing Redis memory limit in `docker-compose.prod.yml`
- Adjusting Redis persistence settings
- Using Redis Cluster for horizontal scaling

#### Application Scaling
- Use Docker Swarm or Kubernetes for multi-instance deployment
- Implement load balancing with nginx
- Monitor resource usage with Docker stats

## Security Considerations

1. **Firewall**: Only open necessary ports
2. **Redis Password**: Always set a strong Redis password in production
3. **Environment Variables**: Never commit secrets to version control
4. **SSL/TLS**: Use a reverse proxy (nginx) with SSL certificates
5. **Updates**: Regularly update Docker images and system packages

## Monitoring

### Basic Monitoring
```bash
# Resource usage
docker stats

# Disk usage
df -h
docker system df
```

### Advanced Monitoring
Consider implementing:
- Prometheus + Grafana for metrics
- ELK Stack for log aggregation
- AWS CloudWatch for infrastructure monitoring

## Backup

### Database Backup
Ensure your MongoDB instance has proper backup configured.

### Redis Backup
Redis data is persisted in Docker volumes. To backup:
```bash
docker run --rm -v whatsapp-backend_redis-data:/data -v $(pwd):/backup alpine tar czf /backup/redis-backup.tar.gz /data
```

## Support

If you encounter issues:
1. Check the logs: `docker-compose logs -f`
2. Verify environment variables are set correctly
3. Ensure all required ports are open in security groups
4. Check AWS EC2 instance resources (CPU, memory, disk)

For additional help, refer to the main README.md or create an issue in the repository.
