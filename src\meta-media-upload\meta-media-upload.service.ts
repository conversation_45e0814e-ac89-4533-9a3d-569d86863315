import { Injectable, Logger, BadRequestException, UnauthorizedException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { StartUploadSessionDto, UploadFileDto, ResumeUploadDto } from './dto';
import { UploadResponseUtil } from './utils/upload-response.util';
import { UploadValidationUtil } from './utils/upload-validation.util';
import { UPLOAD_CONSTANTS } from './utils/upload-constants.util';

/**
 * Service for handling Meta Media Upload operations using the Resumable Upload API
 */
@Injectable()
export class MetaMediaUploadService {
  private readonly logger = new Logger(MetaMediaUploadService.name);
  private readonly axiosInstance: AxiosInstance;
  private readonly baseUrl = UPLOAD_CONSTANTS.META_API_BASE_URL;

  constructor(private readonly configService: ConfigService) {
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      timeout: UPLOAD_CONSTANTS.SESSION_CONFIG.TIMEOUT,
    });
  }

  /**
   * Step 1: Start an upload session with Meta's Resumable Upload API
   */
  async startUploadSession(
    appId: string,
    accessToken: string,
    startUploadSessionDto: StartUploadSessionDto
  ): Promise<any> {
    try {
      this.logger.log(`Starting upload session for file: ${startUploadSessionDto.fileName}`);

      // Validate input parameters
      this.validateStartSessionInput(startUploadSessionDto);

      // Build Meta API request URL
      const url = `/${appId}/uploads`;

      // Prepare request parameters
      const params = {
        file_name: startUploadSessionDto.fileName,
        file_length: startUploadSessionDto.fileLength,
        file_type: startUploadSessionDto.fileType,
        access_token: accessToken,
      };

      this.logger.debug(`Meta API request - URL: ${url}, Params:`, params);

      // Make request to Meta API
      const response = await this.axiosInstance.post(url, null, { params });

      if (response.data && response.data.id) {
        const uploadSessionId = response.data.id;
        this.logger.log(`Upload session created successfully: ${uploadSessionId}`);

        return UploadResponseUtil.createSuccessResponse(
          UploadResponseUtil.formatUploadSessionResponse(uploadSessionId),
          UPLOAD_CONSTANTS.SUCCESS_MESSAGES.SESSION_CREATED,
          UPLOAD_CONSTANTS.HTTP_STATUS.CREATED
        );
      } else {
        throw new BadRequestException('Invalid response from Meta API');
      }

    } catch (error) {
      this.logger.error('Failed to start upload session:', error.response?.data || error.message);
      this.handleMetaApiError(error);
    }
  }

  /**
   * Step 2: Upload file data to the upload session
   */
  async uploadFile(
    uploadSessionId: string,
    accessToken: string,
    fileBuffer: Buffer,
    fileOffset: number = 0
  ): Promise<any> {
    try {
      this.logger.log(`Uploading file data to session: ${uploadSessionId}, offset: ${fileOffset}`);

      // Validate input parameters
      this.validateUploadInput(uploadSessionId, fileOffset);

      // Clean upload session ID (remove 'upload:' prefix if present)
      const cleanSessionId = UploadValidationUtil.extractSessionId(uploadSessionId);
      const fullSessionId = UploadValidationUtil.buildSessionId(cleanSessionId);

      // Build Meta API request URL
      const url = `/${fullSessionId}`;

      // Prepare request headers
      const headers = {
        'Authorization': `OAuth ${accessToken}`,
        'file_offset': fileOffset.toString(),
        'Content-Type': 'application/octet-stream',
      };

      this.logger.debug(`Meta API upload request - URL: ${url}, Headers:`, headers);

      // Make request to Meta API with binary data
      const response = await this.axiosInstance.post(url, fileBuffer, { headers });

      if (response.data && response.data.h) {
        const handleId = response.data.h;
        this.logger.log(`File uploaded successfully, handle ID: ${handleId}`);

        return UploadResponseUtil.createSuccessResponse(
          UploadResponseUtil.formatFileHandleResponse(handleId, uploadSessionId),
          UPLOAD_CONSTANTS.SUCCESS_MESSAGES.FILE_UPLOADED,
          UPLOAD_CONSTANTS.HTTP_STATUS.OK
        );
      } else {
        throw new BadRequestException('Invalid response from Meta API - no handle ID received');
      }

    } catch (error) {
      this.logger.error('Failed to upload file:', error.response?.data || error.message);
      this.handleMetaApiError(error);
    }
  }

  /**
   * Resume an interrupted upload session
   */
  async resumeUpload(
    uploadSessionId: string,
    accessToken: string
  ): Promise<any> {
    try {
      this.logger.log(`Resuming upload session: ${uploadSessionId}`);

      // Validate input parameters
      this.validateResumeInput(uploadSessionId);

      // Clean upload session ID
      const cleanSessionId = UploadValidationUtil.extractSessionId(uploadSessionId);
      const fullSessionId = UploadValidationUtil.buildSessionId(cleanSessionId);

      // Build Meta API request URL
      const url = `/${fullSessionId}`;

      // Prepare request headers
      const headers = {
        'Authorization': `OAuth ${accessToken}`,
      };

      this.logger.debug(`Meta API resume request - URL: ${url}, Headers:`, headers);

      // Make GET request to Meta API to check session status
      const response = await this.axiosInstance.get(url, { headers });

      if (response.data && response.data.id && typeof response.data.file_offset !== 'undefined') {
        const fileOffset = response.data.file_offset;
        this.logger.log(`Upload session status retrieved, file offset: ${fileOffset}`);

        return UploadResponseUtil.createSuccessResponse(
          UploadResponseUtil.formatResumeUploadResponse(uploadSessionId, fileOffset),
          UPLOAD_CONSTANTS.SUCCESS_MESSAGES.UPLOAD_RESUMED,
          UPLOAD_CONSTANTS.HTTP_STATUS.OK
        );
      } else {
        throw new BadRequestException('Invalid response from Meta API - no offset information received');
      }

    } catch (error) {
      this.logger.error('Failed to resume upload session:', error.response?.data || error.message);
      this.handleMetaApiError(error);
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Validates start session input parameters
   */
  private validateStartSessionInput(dto: StartUploadSessionDto): void {
    if (!UploadValidationUtil.validateFileName(dto.fileName)) {
      throw new BadRequestException(UPLOAD_CONSTANTS.ERROR_MESSAGES.INVALID_FILE_NAME);
    }

    if (!UploadValidationUtil.validateFileSize(dto.fileLength)) {
      throw new BadRequestException(UPLOAD_CONSTANTS.ERROR_MESSAGES.FILE_TOO_LARGE);
    }

    if (!UploadValidationUtil.validateFileType(dto.fileType)) {
      throw new BadRequestException(UPLOAD_CONSTANTS.ERROR_MESSAGES.INVALID_FILE_TYPE);
    }
  }

  /**
   * Validates upload input parameters
   */
  private validateUploadInput(uploadSessionId: string, fileOffset: number): void {
    if (!UploadValidationUtil.validateUploadSessionId(uploadSessionId)) {
      throw new BadRequestException(UPLOAD_CONSTANTS.ERROR_MESSAGES.INVALID_SESSION_ID);
    }

    if (!UploadValidationUtil.validateFileOffset(fileOffset)) {
      throw new BadRequestException(UPLOAD_CONSTANTS.ERROR_MESSAGES.INVALID_OFFSET);
    }
  }

  /**
   * Validates resume input parameters
   */
  private validateResumeInput(uploadSessionId: string): void {
    if (!UploadValidationUtil.validateUploadSessionId(uploadSessionId)) {
      throw new BadRequestException(UPLOAD_CONSTANTS.ERROR_MESSAGES.INVALID_SESSION_ID);
    }
  }

  /**
   * Handles Meta API errors and converts them to appropriate HTTP exceptions
   */
  private handleMetaApiError(error: any): void {
    if (error.response?.data?.error) {
      const metaError = error.response.data.error;
      
      // Handle specific Meta API error codes
      switch (metaError.code) {
        case UPLOAD_CONSTANTS.META_ERROR_CODES.INVALID_TOKEN:
          throw new UnauthorizedException('Your Meta access token has expired or is invalid. Please reconnect your Meta Business Account.');
        
        case UPLOAD_CONSTANTS.META_ERROR_CODES.PERMISSION_DENIED:
          throw new UnauthorizedException('You do not have permission to upload files. Please check your Meta Business account permissions.');
        
        case UPLOAD_CONSTANTS.META_ERROR_CODES.INVALID_PARAMETER:
          throw new BadRequestException(`Invalid upload parameters: ${metaError.message}`);
        
        case UPLOAD_CONSTANTS.META_ERROR_CODES.TEMPORARY_ISSUE:
          throw new InternalServerErrorException('Meta API is temporarily unavailable. Please try again in a few minutes.');
        
        default:
          throw new BadRequestException(`Meta API error: ${metaError.message || `Code ${metaError.code}`}`);
      }
    } else if (error.response?.status === 401) {
      throw new UnauthorizedException('Your Meta access token is invalid or expired. Please reconnect your Meta Business Account.');
    } else if (error.response?.status === 403) {
      throw new UnauthorizedException('You do not have permission to upload files. Please check your Meta Business account permissions.');
    } else if (error.response?.status >= 500) {
      throw new InternalServerErrorException('Meta API is temporarily unavailable. Please try again later.');
    } else if (error instanceof BadRequestException || 
               error instanceof UnauthorizedException || 
               error instanceof InternalServerErrorException) {
      // Re-throw known exceptions
      throw error;
    } else {
      // Handle unknown errors
      this.logger.error('Unexpected error in Meta Media Upload Service:', error);
      throw new InternalServerErrorException('An unexpected error occurred during file upload. Please try again.');
    }
  }
}
