import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WhatsAppMessage, WhatsAppMessageDocument } from '../schema/whatsapp-message.schema';
import { WhatsAppStatus, WhatsAppStatusDocument } from '../schema/whatsapp-status.schema';
import { WhatsAppService } from '../whatsapp/whatsapp.service';
import { SupabaseService } from '../supabase/supabase.service';

export interface WhatsAppMessageData {
  id?: string;
  from: string;
  timestamp: string;
  type: string;
  text?: {
    body: string;
  };
  image?: {
    id: string;
    mime_type: string;
    sha256: string;
    filename?: string;
  };
  button?: {
    text: string;
    payload: string;
  };
  interactive?: {
    type: string;
    button_reply?: {
      id: string;
      title: string;
    };
    list_reply?: {
      id: string;
      title: string;
      description?: string;
    };
  };
}

export interface WebhookEntry {
  id: string;
  changes: Array<{
    value: {
      messaging_product: string;
      metadata: {
        display_phone_number: string;
        phone_number_id: string;
      };
      contacts?: Array<{
        profile: {
          name: string;
        };
        wa_id: string;
      }>;
      messages?: WhatsAppMessageData[];
      statuses?: Array<{
        id: string;
        status: string;
        timestamp: string;
        recipient_id: string;
      }>;
    };
    field: string;
  }>;
}

@Injectable()
export class WebhooksService {
  private readonly logger = new Logger(WebhooksService.name);

  constructor(
    private readonly configService: ConfigService,
    @InjectModel(WhatsAppMessage.name) private whatsappMessageModel: Model<WhatsAppMessageDocument>,
    @InjectModel(WhatsAppStatus.name) private whatsappStatusModel: Model<WhatsAppStatusDocument>,
    private readonly whatsappService: WhatsAppService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async verifyWebhook(mode: string, verifyToken: string): Promise<boolean> {
    const expectedToken = this.configService.get<string>('WHATSAPP_VERIFY_TOKEN');
    
    if (mode === 'subscribe' && verifyToken === expectedToken) {
      this.logger.log('Webhook verification successful');
      return true;
    }
    
    this.logger.warn('Webhook verification failed');
    return false;
  }

  async processWebhook(body: any): Promise<void> {
    try {
      this.logger.log('Received webhook body:', JSON.stringify(body, null, 2));
      
      if (body.object === 'whatsapp_business_account') {
        for (const entry of body.entry as WebhookEntry[]) {
          this.logger.log(`Processing entry ID: ${entry.id}`);
          
          for (const change of entry.changes) {
            this.logger.log(`Processing change field: ${change.field}`);
            
                         if (change.field === 'messages') {
               await this.processMessages(change.value);
             }
             else if (change.field === 'message_template_status_update') {
               this.logger.log('Processing template_status change:', JSON.stringify(change.value, null, 2));
               await this.processTemplateStatus(change.value);
             }
             else if (change.field === 'template_category_update') {
               this.logger.log('Processing template_category_update change:', JSON.stringify(change.value, null, 2));
               await this.processTemplateCategoryUpdate(change.value);
             } else {
               this.logger.warn(`Unknown change field: ${change.field}`);
             }
          }
        }
      } else {
        this.logger.warn(`Unknown webhook object: ${body.object}`);
      }
    } catch (error) {
      this.logger.error('Error processing webhook:', error);
      throw error;
    }
  }

  private async processMessages(value: any): Promise<void> {
    if (!value.messages || value.messages.length === 0) return;

    for (const message of value.messages) {
      try {
        await this.storeMessage(message, value.metadata);
        
        // Process different message types
        if (message.type === 'text') {
          await this.handleTextMessage(message, value.metadata);
        } else if (message.type === 'image') {
          await this.handleImageMessage(message, value.metadata);
        } else if (message.type === 'button') {
          await this.handleButtonMessage(message, value.metadata);
        } else if (message.type === 'interactive') {
          await this.handleInteractiveMessage(message, value.metadata);
        }
      } catch (error) {
        this.logger.error(`Error processing message ${message.id}:`, error);
      }
    }
  }

  private async processTemplateStatus(value: any): Promise<void> {
    this.logger.log('Processing template status with value:', JSON.stringify(value, null, 2));
    
    // Handle the new template status format
    if (value.event && value.message_template_id) {
      await this.handleTemplateStatusUpdate({
        id: value.message_template_id,
        status: value.event,
        name: value.message_template_name,
        language: value.message_template_language,
        reason: value.reason
      });
      this.logger.log(`Template status: ${value.event} for template ${value.message_template_id}`);
    } else {
      this.logger.warn('Invalid template status data format');
    }
  }

  private async processTemplateCategoryUpdate(value: any): Promise<void> {
    this.logger.log('Processing template category update with value:', JSON.stringify(value, null, 2));
    
    // Handle the template category update format
    if (value.message_template_id && value.new_category) {
      await this.handleTemplateCategoryUpdate({
        id: value.message_template_id,
        name: value.message_template_name,
        language: value.message_template_language,
        previousCategory: value.previous_category,
        newCategory: value.new_category,
        correctCategory: value.correct_category
      });
      this.logger.log(`Template category update: ${value.previous_category} -> ${value.new_category} for template ${value.message_template_id}`);
    } else {
      this.logger.warn('Invalid template category update data format');
    }
  }

  private async storeMessage(message: WhatsAppMessageData, metadata: any): Promise<void> {
    const messageData = {
      messageId: message.id || message.from, // Use message.id if available, fallback to from
      timestamp: new Date(parseInt(message.timestamp) * 1000),
      type: message.type,
      from: message.from,
      phoneNumberId: metadata.phone_number_id,
      displayPhoneNumber: metadata.display_phone_number,
      content: this.extractMessageContent(message),
      processed: false,
      metadata: metadata,
    };

    const newMessage = new this.whatsappMessageModel(messageData);
    await newMessage.save();
    this.logger.log('Stored message:', messageData);
  }

  private async storeStatus(status: any, metadata: any): Promise<void> {
    const statusData = {
      messageId: status.id,
      status: status.status,
      timestamp: new Date(parseInt(status.timestamp) * 1000),
      recipientId: status.recipient_id,
      phoneNumberId: metadata.phone_number_id,
      metadata: metadata,
    };

    const newStatus = new this.whatsappStatusModel(statusData);
    await newStatus.save();
    this.logger.log('Stored status:', statusData);
  }

  private extractMessageContent(message: WhatsAppMessageData): any {
    switch (message.type) {
      case 'text':
        return { body: message.text?.body };
      case 'image':
        return {
          id: message.image?.id,
          mimeType: message.image?.mime_type,
          sha256: message.image?.sha256,
          filename: message.image?.filename,
        };
      case 'button':
        return {
          text: message.button?.text,
          payload: message.button?.payload,
        };
      case 'interactive':
        if (message.interactive?.button_reply) {
          return {
            type: 'button_reply',
            id: message.interactive.button_reply.id,
            title: message.interactive.button_reply.title,
          };
        } else if (message.interactive?.list_reply) {
          return {
            type: 'list_reply',
            id: message.interactive.list_reply.id,
            title: message.interactive.list_reply.title,
            description: message.interactive.list_reply.description,
          };
        }
        break;
      default:
        return { type: message.type };
    }
  }

  private async handleTextMessage(message: WhatsAppMessageData, metadata: any): Promise<void> {
    const text = message.text?.body || '';
    this.logger.log(`Received text message from ${message.from}: ${text}`);
    
    // Here you can implement your business logic
    // For example, analyze sentiment, route to customer service, etc.
    
    // Example: Simple auto-response for review requests
    if (text.toLowerCase().includes('review')) {
      await this.sendAutoResponse(message.from, 'Thank you for your review! We appreciate your feedback.');
    }
  }

  private async handleImageMessage(message: WhatsAppMessageData, metadata: any): Promise<void> {
    this.logger.log(`Received image message from ${message.from}`);
    // Handle image processing logic here
  }

  private async handleButtonMessage(message: WhatsAppMessageData, metadata: any): Promise<void> {
    const buttonText = message.button?.text || '';
    this.logger.log(`Received button message from ${message.from}: ${buttonText}`);
    // Handle button interaction logic here
  }

  private async handleInteractiveMessage(message: WhatsAppMessageData, metadata: any): Promise<void> {
    if (message.interactive?.button_reply) {
      const buttonId = message.interactive.button_reply.id;
      this.logger.log(`Received button interaction from ${message.from}: ${buttonId}`);
    } else if (message.interactive?.list_reply) {
      const listId = message.interactive.list_reply.id;
      this.logger.log(`Received list interaction from ${message.from}: ${listId}`);
    }
    // Handle interactive message logic here
  }

  private async sendAutoResponse(to: string, message: string): Promise<void> {
    try {
      await this.whatsappService.sendTextMessage(to, message);
      this.logger.log(`Auto-response sent to ${to}: ${message}`);
    } catch (error) {
      this.logger.error(`Failed to send auto-response to ${to}:`, error);
    }
  }

  private async handleTemplateStatusUpdate(templateStatus: any): Promise<void> {
    try {
      const { id: metaTemplateId, status: newStatus, name, language, reason } = templateStatus;
      
      if (!metaTemplateId) {
        this.logger.error('Template status update missing meta_template_id');
        return;
      }
      
      if (!newStatus) {
        this.logger.error('Template status update missing status');
        return;
      }
      
      this.logger.log(`Processing template status update: ${metaTemplateId} -> ${newStatus}`);
      if (name) this.logger.log(`Template name: ${name}`);
      if (language) this.logger.log(`Template language: ${language}`);
      if (reason) this.logger.log(`Template reason: ${reason}`);

      // Find the most recent template that matches this Meta template ID and name
      let query = this.supabaseService.getClient()
        .from('automate_whatsapp_templates')
        .select('*')
        .eq('meta_template_id', metaTemplateId);

      // Add name filter if available
      if (name) {
        query = query.eq('name', name);
        this.logger.log(`Filtering by template name: ${name}`);
      }

      // Get only the most recent template and limit to 1 for optimization
      query = query.order('created_at', { ascending: false }).limit(1);

      const { data: templates, error } = await query;

      if (error) {
        this.logger.error(`Error fetching template with meta_template_id ${metaTemplateId}:`, error);
        return;
      }

      if (!templates || templates.length === 0) {
        this.logger.warn(`No local template found for Meta template ID: ${metaTemplateId}${name ? ` and name: ${name}` : ''}`);
        return;
      }

      // Get the single most recent template
      const template = templates[0];
      this.logger.log(`Found template ${template.id} for Meta template ID: ${metaTemplateId}${name ? ` and name: ${name}` : ''}`);

      // Check if status update is needed
      if (template.meta_template_status === newStatus) {
        this.logger.log(`Template ${template.id} already has status ${newStatus}, skipping update`);
        return;
      }

      // Update only the most recent template
      try {
        const updateData: any = {
          meta_template_status: newStatus,
          updated_at: new Date().toISOString()
        };

      
        if (language) updateData.language = language;

        const { data: updatedTemplate, error: updateError } = await this.supabaseService.getClient()
          .from('automate_whatsapp_templates')
          .update(updateData)
          .eq('id', template.id)
          .select()
          .single();

        if (updateError) {
          this.logger.error(`Error updating template ${template.id}:`, updateError);
        } else {
          this.logger.log(`Successfully updated template ${template.id} status from ${template.meta_template_status} to ${newStatus}`);
        }
      } catch (updateError) {
        this.logger.error(`Error updating template ${template.id}:`, updateError);
      }
         } catch (error) {
       this.logger.error('Error handling template status update:', error);
     }
   }

   private async handleTemplateCategoryUpdate(templateCategoryUpdate: any): Promise<void> {
     try {
       const { id: metaTemplateId, name, language, previousCategory, newCategory, correctCategory } = templateCategoryUpdate;
       
       if (!metaTemplateId) {
         this.logger.error('Template category update missing meta_template_id');
         return;
       }
       
       if (!newCategory) {
         this.logger.error('Template category update missing new_category');
         return;
       }
       
       this.logger.log(`Processing template category update: ${metaTemplateId} -> ${previousCategory} -> ${newCategory}`);
       if (name) this.logger.log(`Template name: ${name}`);
       if (language) this.logger.log(`Template language: ${language}`);
       if (correctCategory) this.logger.log(`Correct category: ${correctCategory}`);

       // Find the most recent template that matches this Meta template ID and name
       let query = this.supabaseService.getClient()
         .from('automate_whatsapp_templates')
         .select('*')
         .eq('meta_template_id', metaTemplateId);

       // Add name filter if available
       if (name) {
         query = query.eq('name', name);
         this.logger.log(`Filtering by template name: ${name}`);
       }

       // Get only the most recent template and limit to 1 for optimization
       query = query.order('created_at', { ascending: false }).limit(1);

       const { data: templates, error } = await query;

       if (error) {
         this.logger.error(`Error fetching template with meta_template_id ${metaTemplateId}:`, error);
         return;
       }

       if (!templates || templates.length === 0) {
         this.logger.warn(`No local template found for Meta template ID: ${metaTemplateId}${name ? ` and name: ${name}` : ''}`);
         return;
       }

       // Get the single most recent template
       const template = templates[0];
       this.logger.log(`Found template ${template.id} for Meta template ID: ${metaTemplateId}${name ? ` and name: ${name}` : ''}`);

       // Check if category update is needed
       if (template.meta_template_category === newCategory) {
         this.logger.log(`Template ${template.id} already has category ${newCategory}, skipping update`);
         return;
       }

       // Update only the most recent template
       try {
         const updateData: any = {
           meta_template_category: newCategory,
           updated_at: new Date().toISOString()
         };

         const { data: updatedTemplate, error: updateError } = await this.supabaseService.getClient()
           .from('automate_whatsapp_templates')
           .update(updateData)
           .eq('id', template.id)
           .select()
           .single();

         if (updateError) {
           this.logger.error(`Error updating template ${template.id}:`, updateError);
         } else {
           this.logger.log(`Successfully updated template ${template.id} category from ${template.meta_template_category || 'unknown'} to ${newCategory}`);
         }
       } catch (updateError) {
         this.logger.error(`Error updating template ${template.id}:`, updateError);
       }
     } catch (error) {
       this.logger.error('Error handling template category update:', error);
     }
   }
} 