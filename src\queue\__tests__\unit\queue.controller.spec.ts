import { Test, TestingModule } from '@nestjs/testing';
import { QueueController } from '../../queue.controller';
import { QueueService } from '../../queue.service';

describe('QueueController', () => {
  let controller: QueueController;
  let queueService: QueueService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [QueueController],
      providers: [
        {
          provide: QueueService,
          useValue: {
            getQueueStats: jest.fn(),
            pauseQueue: jest.fn(),
            resumeQueue: jest.fn(),
            clearQueue: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<QueueController>(QueueController);
    queueService = module.get<QueueService>(QueueService);
  });

  describe('getQueueStats', () => {
    it('should return queue statistics', async () => {
      const mockStats = {
        campaignMessages: { waiting: 10, active: 2, completed: 100, failed: 5 },
        scheduledMessages: { waiting: 5, active: 1, completed: 50, failed: 2 },
        retryMessages: { waiting: 3, active: 0, completed: 20, failed: 1 },
        total: { waiting: 18, active: 3, completed: 170, failed: 8 },
      };

      jest.spyOn(queueService, 'getQueueStats').mockResolvedValue(mockStats);

      const result = await controller.getQueueStats();

      expect(result).toEqual(mockStats);
      expect(queueService.getQueueStats).toHaveBeenCalled();
    });

    it('should handle service errors', async () => {
      const error = new Error('Service error');
      jest.spyOn(queueService, 'getQueueStats').mockRejectedValue(error);

      await expect(controller.getQueueStats()).rejects.toThrow('Service error');
    });
  });

  describe('pauseQueue', () => {
    it('should pause queue successfully', async () => {
      const queueName = 'campaign-messages';
      jest.spyOn(queueService, 'pauseQueue').mockResolvedValue(undefined);

      const result = await controller.pauseQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} paused successfully` });
      expect(queueService.pauseQueue).toHaveBeenCalledWith(queueName);
    });

    it('should pause scheduled messages queue', async () => {
      const queueName = 'scheduled-campaign-messages';
      jest.spyOn(queueService, 'pauseQueue').mockResolvedValue(undefined);

      const result = await controller.pauseQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} paused successfully` });
      expect(queueService.pauseQueue).toHaveBeenCalledWith(queueName);
    });

    it('should pause retry messages queue', async () => {
      const queueName = 'campaign-retry-messages';
      jest.spyOn(queueService, 'pauseQueue').mockResolvedValue(undefined);

      const result = await controller.pauseQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} paused successfully` });
      expect(queueService.pauseQueue).toHaveBeenCalledWith(queueName);
    });

    it('should handle service errors', async () => {
      const queueName = 'campaign-messages';
      const error = new Error('Service error');
      jest.spyOn(queueService, 'pauseQueue').mockRejectedValue(error);

      await expect(controller.pauseQueue(queueName)).rejects.toThrow('Service error');
    });
  });

  describe('resumeQueue', () => {
    it('should resume queue successfully', async () => {
      const queueName = 'campaign-messages';
      jest.spyOn(queueService, 'resumeQueue').mockResolvedValue(undefined);

      const result = await controller.resumeQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} resumed successfully` });
      expect(queueService.resumeQueue).toHaveBeenCalledWith(queueName);
    });

    it('should resume scheduled messages queue', async () => {
      const queueName = 'scheduled-campaign-messages';
      jest.spyOn(queueService, 'resumeQueue').mockResolvedValue(undefined);

      const result = await controller.resumeQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} resumed successfully` });
      expect(queueService.resumeQueue).toHaveBeenCalledWith(queueName);
    });

    it('should resume retry messages queue', async () => {
      const queueName = 'campaign-retry-messages';
      jest.spyOn(queueService, 'resumeQueue').mockResolvedValue(undefined);

      const result = await controller.resumeQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} resumed successfully` });
      expect(queueService.resumeQueue).toHaveBeenCalledWith(queueName);
    });

    it('should handle service errors', async () => {
      const queueName = 'campaign-messages';
      const error = new Error('Service error');
      jest.spyOn(queueService, 'resumeQueue').mockRejectedValue(error);

      await expect(controller.resumeQueue(queueName)).rejects.toThrow('Service error');
    });
  });

  describe('clearQueue', () => {
    it('should clear queue successfully', async () => {
      const queueName = 'campaign-messages';
      jest.spyOn(queueService, 'clearQueue').mockResolvedValue(undefined);

      const result = await controller.clearQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} cleared successfully` });
      expect(queueService.clearQueue).toHaveBeenCalledWith(queueName);
    });

    it('should clear scheduled messages queue', async () => {
      const queueName = 'scheduled-campaign-messages';
      jest.spyOn(queueService, 'clearQueue').mockResolvedValue(undefined);

      const result = await controller.clearQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} cleared successfully` });
      expect(queueService.clearQueue).toHaveBeenCalledWith(queueName);
    });

    it('should clear retry messages queue', async () => {
      const queueName = 'campaign-retry-messages';
      jest.spyOn(queueService, 'clearQueue').mockResolvedValue(undefined);

      const result = await controller.clearQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} cleared successfully` });
      expect(queueService.clearQueue).toHaveBeenCalledWith(queueName);
    });

    it('should handle service errors', async () => {
      const queueName = 'campaign-messages';
      const error = new Error('Service error');
      jest.spyOn(queueService, 'clearQueue').mockRejectedValue(error);

      await expect(controller.clearQueue(queueName)).rejects.toThrow('Service error');
    });
  });
});
