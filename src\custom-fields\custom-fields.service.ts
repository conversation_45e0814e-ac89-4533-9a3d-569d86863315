import { Injectable, BadRequestException, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CustomField } from '../schema/custom-field.schema';
import { CreateCustomFieldDto, UpdateCustomFieldDto } from './dto';
import { SupabaseService } from '../supabase/supabase.service';
import { CustomFieldsResponseUtil, CustomFieldsResponseData } from './utils/custom-fields-response.util';
import { CustomFieldsValidationUtil } from './utils/custom-fields-validation.util';
import { CUSTOM_FIELDS_CONSTANTS } from './utils/custom-fields-constants.util';

/**
 * Refactored CustomFieldsService with improved structure and consistent response handling
 */
@Injectable()
export class CustomFieldsService {
  private readonly logger = new Logger(CustomFieldsService.name);

  constructor(
    @InjectModel(CustomField.name) private readonly customFieldModel: Model<CustomField>,
    private readonly supabaseService: SupabaseService,
  ) {}

  // ==================== PUBLIC METHODS ====================

  /**
   * Create custom field
   */
  async createCustomField(createDto: CreateCustomFieldDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting custom field creation process');

      const user = CustomFieldsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = CustomFieldsValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Validate and prepare data
      const payload = CustomFieldsValidationUtil.validateCustomFieldCreationData(createDto, user.id, workspaceId);

      // Check for duplicate label in workspace
      const existing = await this.customFieldModel.findOne({ workspaceId, label: payload.label });
      if (existing) {
        return CustomFieldsResponseUtil.createDuplicateErrorResponse(
          CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_CUSTOM_FIELD_LABEL
        );
      }

      const customField = new this.customFieldModel(payload);
      
      let savedCustomField;
      try {
        savedCustomField = await customField.save();
      } catch (error: any) {
        // Handle duplicate key error from Mongo unique index
        if (error?.code === 11000) {
          return CustomFieldsResponseUtil.createDuplicateErrorResponse(
            CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_CUSTOM_FIELD_LABEL
          );
        }
        this.logger.error('Failed to create custom field', error);
        throw new BadRequestException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.CUSTOM_FIELD_CREATION_FAILED);
      }

      this.logger.log(`Custom field created successfully: ${savedCustomField._id}`);

      const responseData = CustomFieldsResponseUtil.createCustomFieldCreationData(savedCustomField);
      return CustomFieldsResponseUtil.createSuccessResponse(
        responseData,
        CUSTOM_FIELDS_CONSTANTS.SUCCESS_MESSAGES.CUSTOM_FIELD_CREATED,
        CUSTOM_FIELDS_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Create custom field failed:', error);
      this.handleCustomFieldsError(error);
    }
  }

  /**
   * Get all custom fields for workspace
   */
  async getCustomFieldsForWorkspace(req: any): Promise<any> {
    try {
      this.logger.log('Starting get custom fields for workspace process');

      const user = CustomFieldsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = CustomFieldsValidationUtil.validateUserProfile(userProfile, userProfileError);

      const customFields = await this.customFieldModel
        .find({ workspaceId })
        .sort({ createdAt: -1 })
        .lean();

      this.logger.log(`Custom fields fetched successfully: ${customFields.length} total fields`);

      const responseData = CustomFieldsResponseUtil.createCustomFieldsListData(customFields);
      return CustomFieldsResponseUtil.createSuccessResponse(
        responseData,
        CUSTOM_FIELDS_CONSTANTS.SUCCESS_MESSAGES.CUSTOM_FIELDS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get custom fields for workspace failed:', error);
      this.handleCustomFieldsError(error);
    }
  }

  /**
   * Get custom field by ID
   */
  async getCustomFieldById(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get custom field process for ID: ${id}`);

      const user = CustomFieldsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = CustomFieldsValidationUtil.validateUserProfile(userProfile, userProfileError);

      const customField = await this.customFieldModel
        .findOne({ _id: id, workspaceId })
        .lean();

      CustomFieldsValidationUtil.validateCustomFieldExists(customField, id);

      this.logger.log(`Custom field fetched successfully: ${id}`);

      const responseData = CustomFieldsResponseUtil.createCustomFieldCreationData(customField);
      return CustomFieldsResponseUtil.createSuccessResponse(
        responseData,
        CUSTOM_FIELDS_CONSTANTS.SUCCESS_MESSAGES.CUSTOM_FIELD_FETCHED
      );

    } catch (error) {
      this.logger.error('Get custom field failed:', error);
      this.handleCustomFieldsError(error);
    }
  }

  /**
   * Update custom field
   */
  async updateCustomField(id: string, updateDto: UpdateCustomFieldDto, req: any): Promise<any> {
    try {
      this.logger.log(`Starting update custom field process for ID: ${id}`);

      const user = CustomFieldsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = CustomFieldsValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Get current custom field to validate type changes
      const currentCustomField = await this.customFieldModel.findOne({ _id: id, workspaceId });
      CustomFieldsValidationUtil.validateCustomFieldExists(currentCustomField, id);

      // Validate update data
      const updateData = CustomFieldsValidationUtil.validateCustomFieldUpdateData(updateDto);

      // If type is being changed, re-validate options
      if (updateData.type && updateData.options !== undefined) {
        updateData.options = CustomFieldsValidationUtil.validateOptions(updateData.options, updateData.type);
      } else if (updateData.options !== undefined && !updateData.type && currentCustomField) {
        // If only options are being updated, validate against current type
        updateData.options = CustomFieldsValidationUtil.validateOptions(updateData.options, currentCustomField.type);
      }

      // Check for duplicate label if label is being updated
      if (updateData.label) {
        const existing = await this.customFieldModel.findOne({
          _id: { $ne: id },
          workspaceId,
          label: updateData.label
        });
        if (existing) {
          return CustomFieldsResponseUtil.createDuplicateErrorResponse(
            CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_CUSTOM_FIELD_LABEL
          );
        }
      }

      try {
        const updated = await this.customFieldModel.findOneAndUpdate(
          { _id: id, workspaceId },
          { $set: updateData },
          { new: true }
        );

        this.logger.log(`Custom field updated successfully: ${id}`);

        const responseData = CustomFieldsResponseUtil.createCustomFieldCreationData(updated);
        return CustomFieldsResponseUtil.createSuccessResponse(
          responseData,
          CUSTOM_FIELDS_CONSTANTS.SUCCESS_MESSAGES.CUSTOM_FIELD_UPDATED
        );

      } catch (error: any) {
        if (error?.code === 11000) {
          return CustomFieldsResponseUtil.createDuplicateErrorResponse(
            CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_CUSTOM_FIELD_LABEL
          );
        }
        this.logger.error('Failed to update custom field', error);
        throw new BadRequestException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.CUSTOM_FIELD_UPDATE_FAILED);
      }

    } catch (error) {
      this.logger.error('Update custom field failed:', error);
      this.handleCustomFieldsError(error);
    }
  }

  /**
   * Delete custom field
   */
  async deleteCustomField(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting delete custom field process for ID: ${id}`);

      const user = CustomFieldsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = CustomFieldsValidationUtil.validateUserProfile(userProfile, userProfileError);

      const deletedCustomField = await this.customFieldModel.findOneAndDelete({ _id: id, workspaceId });
      CustomFieldsValidationUtil.validateCustomFieldExists(deletedCustomField, id);

      this.logger.log(`Custom field deleted successfully: ${id}`);

      return CustomFieldsResponseUtil.createSuccessResponse(
        {},
        CUSTOM_FIELDS_CONSTANTS.SUCCESS_MESSAGES.CUSTOM_FIELD_DELETED
      );

    } catch (error) {
      this.logger.error('Delete custom field failed:', error);
      this.handleCustomFieldsError(error);
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Handles custom fields-related errors
   */
  private handleCustomFieldsError(error: any): void {
    if (error instanceof BadRequestException || 
        error instanceof NotFoundException || 
        error instanceof ConflictException) {
      throw error;
    }
    
    this.logger.error('Unexpected custom fields error:', error);
    throw new BadRequestException('Internal server error');
  }
}


