# Custom Fields Module Documentation

## Overview

The Custom Fields module provides functionality for managing custom fields within workspaces. Custom fields allow users to extend contact data with additional information specific to their business needs.

## Table of Contents

- [Architecture](ARCHITECTURE.md) - Module architecture and design patterns
- [API Endpoints](API_ENDPOINTS.md) - Complete API documentation
- [Security](SECURITY.md) - Security considerations and best practices
- [Testing](TESTING.md) - Testing strategies and test cases
- [Deployment](DEPLOYMENT.md) - Deployment guidelines

## Features

- ✅ Create custom fields with various types (text, number, date, datetime, dropdown, boolean)
- ✅ Update existing custom fields
- ✅ Delete custom fields
- ✅ List all custom fields for a workspace
- ✅ Get specific custom field by ID
- ✅ Workspace-scoped access control
- ✅ Comprehensive validation
- ✅ Consistent error handling
- ✅ Detailed logging

## Quick Start

### Prerequisites

- User must be authenticated
- User must belong to a workspace
- Proper permissions for custom field management

### Basic Usage

```typescript
// Create a text custom field
POST /custom-fields
{
  "label": "Company Size",
  "type": "text",
  "showOnContact": true,
  "showOnChat": false
}

// Create a dropdown custom field
POST /custom-fields
{
  "label": "Industry",
  "type": "dropdown",
  "options": ["Technology", "Healthcare", "Finance", "Education"],
  "showOnContact": true,
  "showOnChat": true
}
```

## Module Structure

```
custom-fields/
├── custom-fields.controller.ts    # API endpoints
├── custom-fields.service.ts       # Business logic
├── custom-fields.module.ts        # Module configuration
├── dto/                           # Data transfer objects
│   ├── create-custom-field.dto.ts
│   ├── update-custom-field.dto.ts
│   └── index.ts
├── utils/                         # Utility classes
│   ├── custom-fields-constants.util.ts
│   ├── custom-fields-validation.util.ts
│   └── custom-fields-response.util.ts
└── docs/                          # Documentation
    ├── INDEX.md
    ├── ARCHITECTURE.md
    ├── API_ENDPOINTS.md
    ├── SECURITY.md
    ├── TESTING.md
    └── DEPLOYMENT.md
```

## Custom Field Types

| Type | Description | Options Required | Example |
|------|-------------|------------------|---------|
| `text` | Free text input | No | "Company Name" |
| `number` | Numeric input | No | "Employee Count" |
| `date` | Date picker | No | "Birth Date" |
| `datetime` | Date and time picker | No | "Last Contact" |
| `dropdown` | Select from options | Yes | "Industry" |
| `bool` | Boolean checkbox | No | "Is VIP Customer" |

## Response Format

All API responses follow a consistent format:

```json
{
  "status": "success|error",
  "code": 200,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Error Handling

The module provides comprehensive error handling with specific error codes and messages:

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `404` - Not Found (custom field not found)
- `409` - Conflict (duplicate label)
- `500` - Internal Server Error

## Security

- All endpoints require authentication
- Workspace-scoped access control
- Input validation and sanitization
- SQL injection prevention through Mongoose
- Rate limiting (handled at application level)

## Performance Considerations

- Database indexes on workspaceId and label
- Lean queries for read operations
- Efficient validation using utility classes
- Proper error handling to prevent resource leaks

## Monitoring and Logging

- Comprehensive logging at service level
- Error tracking and monitoring
- Performance metrics collection
- Audit trail for custom field operations

## Dependencies

- `@nestjs/common` - NestJS core functionality
- `@nestjs/mongoose` - MongoDB integration
- `class-validator` - DTO validation
- `mongoose` - MongoDB ODM
- `../auth/auth.guard` - Authentication
- `../supabase/supabase.service` - User profile management

## Version History

- **v1.0.0** - Initial implementation with basic CRUD operations
- **v1.1.0** - Added comprehensive validation and error handling
- **v1.2.0** - Refactored following auth module patterns
- **v1.3.0** - Added complete documentation and test coverage
