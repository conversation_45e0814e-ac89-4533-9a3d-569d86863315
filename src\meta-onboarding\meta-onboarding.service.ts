import { Injectable, UnauthorizedException, BadRequestException, NotFoundException, Logger, ConflictException } from '@nestjs/common';
import { Response } from 'express';
import { SupabaseService } from 'src/supabase/supabase.service';
import { CreateMetaCredentialsDto, UpdateMetaCredentialsDto, MetaCredentialsQueryDto } from './dto';
import { MetaOnboardingResponseUtil, MetaOnboardingResponseData } from './utils/meta-onboarding-response.util';
import { MetaOnboardingValidationUtil } from './utils/meta-onboarding-validation.util';
import { META_ONBOARDING_CONSTANTS } from './utils/meta-onboarding-constants.util';

/**
 * Refactored MetaOnboardingService with improved structure and consistent response handling
 */
@Injectable()
export class MetaOnboardingService {
  private readonly logger = new Logger(MetaOnboardingService.name);

  constructor(private readonly supabaseService: SupabaseService) {}

  // ==================== PUBLIC METHODS ====================

  /**
   * Create meta credentials
   */
  async createMetaCredentials(credentialsDto: CreateMetaCredentialsDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting meta credentials creation process',credentialsDto);

      const user = MetaOnboardingValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = MetaOnboardingValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Validate and prepare data
      const payload = MetaOnboardingValidationUtil.validateMetaCredentialsCreationData(credentialsDto, user.id, workspaceId);

      // Check for duplicate credentials in workspace
      const { data: existing, error: existingError } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('id')
        .eq('workspace_id', workspaceId)
        .eq('whatsapp_business_id', payload.whatsapp_business_id)
        .single();

      if (existing && !existingError) {
        return MetaOnboardingResponseUtil.createDuplicateErrorResponse(
          META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.DUPLICATE_CREDENTIALS
        );
      }

      const { data: credentials, error } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .insert(payload)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to create meta credentials', error);
        
        // Handle specific database constraint violations
        if (error.code === '23505') { // Unique constraint violation
          if (error.details && error.details.includes('phone_number_id')) {
            throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.PHONE_NUMBER_ALREADY_EXISTS);
          } else if (error.details && error.details.includes('whatsapp_business_id')) {
            throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.WHATSAPP_BUSINESS_ACCOUNT_ALREADY_EXISTS);
          } else {
            throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.CREDENTIALS_ALREADY_IN_USE);
          }
        }
        
        // Handle other database errors
        if (error.code === '23503') { // Foreign key constraint violation
          throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.INVALID_WORKSPACE_REFERENCE);
        }
        
        // Generic error for other cases
        throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.CREDENTIALS_CREATION_FAILED);
      }

      this.logger.log(`Meta credentials created successfully: ${credentials.id}`);

      const responseData = MetaOnboardingResponseUtil.createCredentialsCreationData(credentials);
      return MetaOnboardingResponseUtil.createSuccessResponse(
        responseData,
        META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_CREATED,
        META_ONBOARDING_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Create meta credentials failed:', error);
      this.handleMetaOnboardingError(error);
    }
  }

  /**
   * Get meta credentials by workspace
   */
  async getMetaCredentialsByWorkspace(workspaceId: string, req: any, queryDto: MetaCredentialsQueryDto): Promise<any> {
    try {
      this.logger.log(`Starting get meta credentials by workspace process for workspace: ${workspaceId}`);

      const user = MetaOnboardingValidationUtil.validateUserContext(req);

      // Check if user has access to this workspace
      const workspaceMember = await this.supabaseService.getWorkspaceMember(parseInt(workspaceId), user.id);
      MetaOnboardingValidationUtil.validateWorkspaceAccess(workspaceMember, workspaceMember.error, workspaceId);

      const { page, limit } = MetaOnboardingValidationUtil.validatePaginationParams(queryDto.page, queryDto.limit);
      const skip = (page - 1) * limit;

      const { data: credentials, error, count } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*', { count: 'exact' })
        .eq('workspace_id', parseInt(workspaceId))
        .order('created_at', { ascending: false })
        .range(skip, skip + limit - 1);

      if (error) {
        this.logger.error('Failed to fetch workspace credentials', error);
        throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.CREDENTIALS_FETCH_FAILED);
      }

      this.logger.log(`Workspace credentials fetched successfully: ${credentials?.length || 0} total credentials`);

      const pagination = MetaOnboardingResponseUtil.createPaginationMetadata(page, limit, count || 0);
      const responseData = MetaOnboardingResponseUtil.createCredentialsListData(credentials || [], pagination);
      return MetaOnboardingResponseUtil.createSuccessResponse(
        responseData,
        META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.WORKSPACE_CREDENTIALS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get meta credentials by workspace failed:', error);
      this.handleMetaOnboardingError(error);
    }
  }

  /**
   * Get meta credentials by user
   */
  async getMetaCredentialsByUser(req: any, queryDto: MetaCredentialsQueryDto): Promise<any> {
    try {
      this.logger.log('Starting get meta credentials by user process');

      const user = MetaOnboardingValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = MetaOnboardingValidationUtil.validateUserProfile(userProfile, userProfileError);

      const { page, limit } = MetaOnboardingValidationUtil.validatePaginationParams(queryDto.page, queryDto.limit);
      const skip = (page - 1) * limit;

      const { data: credentials, error, count } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*', { count: 'exact' })
        .eq('created_by', user.id)
        .order('created_at', { ascending: false })
        .range(skip, skip + limit - 1);

      if (error) {
        this.logger.error('Failed to fetch user credentials', error);
        throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.CREDENTIALS_FETCH_FAILED);
      }

      this.logger.log(`User credentials fetched successfully: ${credentials?.length || 0} total credentials`);

      const pagination = MetaOnboardingResponseUtil.createPaginationMetadata(page, limit, count || 0);
      const responseData = MetaOnboardingResponseUtil.createCredentialsListData(credentials || [], pagination);
      return MetaOnboardingResponseUtil.createSuccessResponse(
        responseData,
        META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.USER_CREDENTIALS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get meta credentials by user failed:', error);
      this.handleMetaOnboardingError(error);
    }
  }

  /**
   * Get meta credentials by ID
   */
  async getMetaCredentialsById(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get meta credentials process for ID: ${id}`);

      const user = MetaOnboardingValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = MetaOnboardingValidationUtil.validateUserProfile(userProfile, userProfileError);
      const { data: credentials, error } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*')
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .single();

      if (error || !credentials) {
        throw new NotFoundException(`Meta credentials with ID ${id} not found`);
      }

      this.logger.log(`Meta credentials fetched successfully: ${id}`);

      const responseData = MetaOnboardingResponseUtil.createCredentialsCreationData(credentials);
      return MetaOnboardingResponseUtil.createSuccessResponse(
        responseData,
        META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get meta credentials failed:', error);
      this.handleMetaOnboardingError(error);
    }
  }

  /**
   * Get meta credentials by phone id
   */
  async getMetaCredentialsByPhoneId(phoneNumberId: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get meta credentials by phone id process for phone number id: ${phoneNumberId}`);

      const user = MetaOnboardingValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = MetaOnboardingValidationUtil.validateUserProfile(userProfile, userProfileError);

      const { data: credentials, error } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*')
        .eq('phone_number_id', phoneNumberId)
        .eq('workspace_id', workspaceId)
        .single();

      if (error || !credentials) {
        throw new NotFoundException(`Meta credentials with phone number id ${phoneNumberId} not found`);
      }

      this.logger.log(`Meta credentials fetched successfully: ${phoneNumberId}`);

      const responseData = MetaOnboardingResponseUtil.createCredentialsCreationData(credentials);
      return MetaOnboardingResponseUtil.createSuccessResponse(
        responseData,
        META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get meta credentials by phone id failed:', error);
      this.handleMetaOnboardingError(error);
    }
  }

  /**
   * Update meta credentials
   */
  async updateMetaCredentials(id: string, updateDto: UpdateMetaCredentialsDto, req: any): Promise<any> {
    try {
      this.logger.log(`Starting update meta credentials process for ID: ${id}`);

      const user = MetaOnboardingValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = MetaOnboardingValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Get current credentials to validate existence
      const { data: currentCredentials, error: currentError } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*')
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .single();

      if (currentError || !currentCredentials) {
        throw new NotFoundException(`Meta credentials with ID ${id} not found`);
      }

      // Validate update data
      const updateData = MetaOnboardingValidationUtil.validateMetaCredentialsUpdateData(updateDto);

      // Check for duplicate whatsapp_business_id if it's being updated
      if (updateData.whatsapp_business_id) {
        const { data: existing, error: existingError } = await this.supabaseService.getClient()
          .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
          .select('id')
          .eq('workspace_id', workspaceId)
          .eq('whatsapp_business_id', updateData.whatsapp_business_id)
          .neq('id', id)
          .single();

        if (existing && !existingError) {
          return MetaOnboardingResponseUtil.createDuplicateErrorResponse(
            META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.DUPLICATE_CREDENTIALS
          );
        }
      }

      const { data: updated, error } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .update(updateData)
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to update meta credentials', error);
        
        // Handle specific database constraint violations
        if (error.code === '23505') { // Unique constraint violation
          if (error.details && error.details.includes('phone_number_id')) {
            throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.PHONE_NUMBER_ALREADY_EXISTS);
          } else if (error.details && error.details.includes('whatsapp_business_id')) {
            throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.WHATSAPP_BUSINESS_ACCOUNT_ALREADY_EXISTS);
          } else {
            throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.CREDENTIALS_ALREADY_IN_USE);
          }
        }
        
        // Handle other database errors
        if (error.code === '23503') { // Foreign key constraint violation
          throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.INVALID_WORKSPACE_REFERENCE);
        }
        
        // Generic error for other cases
        throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.CREDENTIALS_UPDATE_FAILED);
      }

      this.logger.log(`Meta credentials updated successfully: ${id}`);

      const responseData = MetaOnboardingResponseUtil.createCredentialsCreationData(updated);
      return MetaOnboardingResponseUtil.createSuccessResponse(
        responseData,
        META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_UPDATED
      );

    } catch (error) {
      this.logger.error('Update meta credentials failed:', error);
      this.handleMetaOnboardingError(error);
    }
  }

  /**
   * Delete meta credentials
   */
  async deleteMetaCredentials(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting delete meta credentials process for ID: ${id}`);

      const user = MetaOnboardingValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = MetaOnboardingValidationUtil.validateUserProfile(userProfile, userProfileError);

      const { data: deleted, error } = await this.supabaseService.getClient()
        .from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS)
        .delete()
        .eq('id', id)
        .eq('workspace_id', workspaceId)
        .select()
        .single();

      if (error || !deleted) {
        throw new NotFoundException(`Meta credentials with ID ${id} not found`);
      }

      this.logger.log(`Meta credentials deleted successfully: ${id}`);

      return MetaOnboardingResponseUtil.createSuccessResponse(
        {},
        META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_DELETED
      );

    } catch (error) {
      this.logger.error('Delete meta credentials failed:', error);
      this.handleMetaOnboardingError(error);
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Handles meta-onboarding-related errors
   */
  private handleMetaOnboardingError(error: any): void {
    if (error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException) {
      throw error;
    }

    this.logger.error('Unexpected meta-onboarding error:', error);
    throw new BadRequestException('Internal server error');
  }
}