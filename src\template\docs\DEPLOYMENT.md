# Template Module Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Template Module in various environments, including development, staging, and production.

## Prerequisites

### System Requirements
- Node.js 18+ 
- PostgreSQL 13+
- Redis 6+
- Docker (optional)
- Kubernetes (optional)

### External Services
- Supabase account
- Meta Developer account
- OpenAI API access
- File storage (S3, GCS, etc.)

## Environment Configuration

### Environment Variables

#### Required Variables
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Meta API
META_APP_ID=your-meta-app-id
META_APP_SECRET=your-meta-app-secret
META_WEBHOOK_VERIFY_TOKEN=your-webhook-token

# AI Service
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORGANIZATION=your-org-id

# File Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket
AWS_REGION=us-east-1

# Application
NODE_ENV=production
PORT=3000
JWT_SECRET=your-jwt-secret
CORS_ORIGIN=https://your-frontend.com

# Rate Limiting
REDIS_URL=redis://localhost:6379
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
```

#### Optional Variables
```bash
# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Monitoring
SENTRY_DSN=your-sentry-dsn
NEW_RELIC_LICENSE_KEY=your-newrelic-key

# Security
ENCRYPTION_KEY=your-encryption-key
SESSION_SECRET=your-session-secret
```

## Local Development

### Setup
```bash
# Clone repository
git clone <repository-url>
cd automate-whatsapp-backend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Setup database
npm run db:migrate
npm run db:seed

# Start development server
npm run start:dev
```

### Docker Development
```bash
# Build development image
docker build -f Dockerfile.dev -t template-module:dev .

# Run with docker-compose
docker-compose -f docker-compose.dev.yml up
```

## Staging Deployment

### Docker Deployment
```bash
# Build staging image
docker build -f Dockerfile.staging -t template-module:staging .

# Deploy with docker-compose
docker-compose -f docker-compose.staging.yml up -d
```

### Kubernetes Deployment
```yaml
# k8s/staging-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: template-module-staging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: template-module-staging
  template:
    metadata:
      labels:
        app: template-module-staging
    spec:
      containers:
      - name: template-module
        image: template-module:staging
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "staging"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: staging-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

## Production Deployment

### Docker Production
```dockerfile
# Dockerfile.prod
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM node:18-alpine AS runtime
WORKDIR /app
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./
USER nestjs
EXPOSE 3000
CMD ["node", "dist/main"]
```

### Kubernetes Production
```yaml
# k8s/production-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: template-module-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: template-module-prod
  template:
    metadata:
      labels:
        app: template-module-prod
    spec:
      containers:
      - name: template-module
        image: template-module:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: prod-secrets
              key: database-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Service Configuration
```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: template-module-service
spec:
  selector:
    app: template-module-prod
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

## Database Setup

### Migration
```bash
# Run migrations
npm run db:migrate

# Rollback if needed
npm run db:rollback

# Check migration status
npm run db:status
```

### Seeding
```bash
# Seed development data
npm run db:seed

# Seed production data
npm run db:seed:prod
```

## Monitoring & Logging

### Health Checks
```typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  @Get()
  check() {
    return { status: 'ok', timestamp: new Date().toISOString() };
  }
  
  @Get('ready')
  ready() {
    // Check database connectivity
    // Check external services
    return { status: 'ready' };
  }
}
```

### Logging Configuration
```typescript
// logger.config.ts
export const loggerConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: process.env.LOG_FORMAT || 'json',
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'app.log' })
  ]
};
```

### Metrics Collection
```typescript
// metrics.service.ts
@Injectable()
export class MetricsService {
  private readonly prometheus = require('prom-client');
  
  private readonly httpRequestDuration = new this.prometheus.Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code']
  });
}
```

## Security Configuration

### SSL/TLS
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Firewall Rules
```bash
# UFW configuration
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 3000/tcp   # Block direct access to app
ufw enable
```

## Backup & Recovery

### Database Backup
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### Automated Backups
```yaml
# k8s/backup-cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: database-backup
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: postgres:13
            command:
            - /bin/bash
            - -c
            - |
              pg_dump $DATABASE_URL > backup.sql
              aws s3 cp backup.sql s3://backup-bucket/
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: prod-secrets
                  key: database-url
```

## Scaling

### Horizontal Scaling
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: template-module-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: template-module-prod
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Load Balancing
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: template-module-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: template-module-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: template-module-service
            port:
              number: 80
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check database connectivity
psql $DATABASE_URL -c "SELECT 1;"

# Check connection pool
npm run db:status
```

#### Memory Issues
```bash
# Check memory usage
docker stats

# Increase memory limits
# In docker-compose.yml or k8s deployment
```

#### Performance Issues
```bash
# Check logs
docker logs <container-id>

# Monitor metrics
kubectl top pods
```

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=debug
export NODE_ENV=development

# Start with debug
npm run start:debug
```

## Rollback Procedures

### Application Rollback
```bash
# Docker rollback
docker-compose down
docker-compose -f docker-compose.previous.yml up -d

# Kubernetes rollback
kubectl rollout undo deployment/template-module-prod
```

### Database Rollback
```bash
# Rollback migration
npm run db:rollback

# Restore from backup
psql $DATABASE_URL < backup_20240101_020000.sql
```

## Maintenance

### Regular Maintenance Tasks
- Update dependencies monthly
- Review and rotate secrets quarterly
- Monitor disk space and logs
- Update SSL certificates
- Review security patches

### Monitoring Checklist
- [ ] Application health checks
- [ ] Database connectivity
- [ ] External service availability
- [ ] Resource utilization
- [ ] Error rates and logs
- [ ] Security events
