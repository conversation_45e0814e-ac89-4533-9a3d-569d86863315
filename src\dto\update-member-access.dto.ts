import { IsBoolean, <PERSON><PERSON>otEmpty, IsString, <PERSON><PERSON>UID } from 'class-validator';

export class UpdateMemberAccessDto {
  @IsString({ message: 'User ID must be a string' })
  @IsNotEmpty({ message: 'User ID is required' })
  user_id: string;

  @IsBoolean({ message: 'Cards access must be a boolean value' })
  @IsNotEmpty({ message: 'Cards access is required' })
  cards_access: boolean;
}
