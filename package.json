{"name": "automate-whatsapp-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "redis:up": "docker-compose -f docker-compose.redis.yml up -d", "redis:down": "docker-compose -f docker-compose.redis.yml down", "test:queue": "node scripts/test-queue.js", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:prod": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d", "docker:logs": "docker-compose logs -f", "health:check": "curl -f http://localhost:8000/health || echo 'Health check failed'", "monitor": "bash scripts/monitor.sh", "monitor:watch": "bash scripts/monitor.sh --watch", "validate": "bash scripts/validate-setup.sh", "validate:docker": "bash scripts/validate-setup.sh --docker", "validate:compose": "bash scripts/validate-setup.sh --compose"}, "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@google/generative-ai": "^0.24.1", "@nestjs/bull": "^11.0.3", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^11.1.6", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.1.6", "@nestjs/websockets": "^11.1.6", "@supabase/supabase-js": "^2.51.0", "@types/cookie-parser": "^1.4.9", "axios": "^1.10.0", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.7", "form-data": "^4.0.4", "kafkajs": "^2.2.4", "mongoose": "^8.17.1", "redis": "^5.8.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/socket.io": "^3.0.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}