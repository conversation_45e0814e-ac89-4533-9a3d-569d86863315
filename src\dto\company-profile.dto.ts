import { PartialType } from '@nestjs/mapped-types';
import { IsNotEmpty, IsOptional, IsString, IsUUID, IsArray, IsUrl, IsEmail, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class SocialLinkDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsUrl()
  @IsNotEmpty()
  url: string;

  @IsOptional()
  @IsString()
  logo_url?: string;
}

export class CreateCompanyProfileDto {
  @IsString()
  @IsNotEmpty({ message: 'Company name is required' })
  company_name: string;

  @IsString()
  company_logo?: string;

  @IsOptional()
  @IsString()
  address?: string;

  @IsString()
  phone_number?: string;

 
  @IsString()
  country_code?: string;

  @IsEmail()
  email?: string;

  
  @IsUrl()
  website?: string;


  @IsString()
  industry_type?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SocialLinkDto)
  social_links?: SocialLinkDto[];

  @IsOptional()
  @IsString()
  card_type:string

  @IsOptional()
  @IsString()
  cover_image:string

}

export class UpdateCompanyProfileDto extends PartialType(CreateCompanyProfileDto){
  
}