import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { CreateAiTemplateDto } from '../template/dto/create-ai-template.dto';
import axios from 'axios';

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);
  private readonly genAI: GoogleGenerativeAI;
  private readonly model: any;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (!apiKey) {
      this.logger.error('GEMINI_API_KEY is not configured');
      throw new Error('GEMINI_API_KEY is required for AI template generation');
    }

    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
  }

  async generateTemplate(aiTemplateDto: CreateAiTemplateDto): Promise<any> {
    try {
      const prompt = this.buildPrompt(aiTemplateDto.prompt);
      
      this.logger.log('Generating template with AI...');
      
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Parse the AI response
      const template = this.parseAiResponse(text, aiTemplateDto.prompt);
      
      this.logger.log('Template generated successfully');
      return template;
    } catch (error) {
      this.logger.error('Failed to generate template with AI:', error);
      throw new BadRequestException(`AI template generation failed: ${error.message}`);
    }
  }

  private buildPrompt(userPrompt: string): string {
    return `You are an expert WhatsApp Business template creator. Create a professional WhatsApp template based on the user's request.

USER REQUEST: ${userPrompt}

WHATSAPP TEMPLATE GUIDELINES:
1. Template name should be descriptive and follow WhatsApp naming conventions (no spaces, use underscores)
2. Content should be engaging, clear, and appropriate for the business context
3. Include variables using {{1}}, {{2}}, etc. for dynamic content where appropriate
4. For interactive templates, include relevant buttons (max 3)
5. Header text should be compelling (max 60 characters)
6. Body text should be informative and actionable (max 1000 characters)
7. Footer should include call-to-action or contact info (max 60 characters)
8. Follow WhatsApp Business Policy guidelines
9. Choose appropriate template type: text, interactive, image, video, document
10. Use appropriate category: MARKETING, UTILITY, AUTHENTICATION

META TEMPLATE STRUCTURE REQUIREMENTS:
- Name: Must be unique, descriptive, and follow naming conventions
- Category: Choose from: MARKETING, UTILITY, AUTHENTICATION
- Components: HEADER (optional), BODY (required), FOOTER (optional), BUTTONS (for interactive)
- Language: Use standard language codes (en, es, fr, etc.)

Please respond with a JSON object in the following Meta format:
{
  "name": "template_name_here",
  "description": "Brief description of the template",
  "category": "MARKETING|UTILITY|AUTHENTICATION",
  "language": "en",
  "components": [
    {
      "type": "HEADER",
      "format": "TEXT",
      "text": "Header text with {{1}} if needed",
      "example": {
        "header_text": ["Example Header Value"]
      }
    },
    {
      "type": "BODY", 
      "text": "Main message content with {{1}}, {{2}} variables",
      "example": {
        "body_text": [["Example Value 1", "Example Value 2"]]
      }
    },
    {
      "type": "FOOTER",
      "text": "Footer text (optional)"
    },
    {
      "type": "BUTTONS",
      "buttons": [
        {
          "type": "QUICK_REPLY",
          "text": "Button Text"
        }
      ]
    }
  ]
}

Ensure the template is:
- Professional and brand-appropriate
- Compliant with WhatsApp Business Policy
- Engaging and action-oriented
- Suitable for the specified business context
- Optimized for the chosen template type`;
  }

  private parseAiResponse(aiResponse: string, userPrompt: string): any {
    try {
      // Extract JSON from the AI response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in AI response');
      }

      const templateData = JSON.parse(jsonMatch[0]);
      
      // Validate and enhance the template data (Meta format)
      const template = {
        name: templateData.name,
        description: templateData.description,
        language: templateData.language || 'en',
        components: templateData.components || [],
        meta_template_category: templateData.category || 'MARKETING'
      };

      // Validate components
      if (!Array.isArray(template.components) || template.components.length === 0) {
        throw new Error('Template must have at least one component');
      }

      // Validate each component
      template.components.forEach((component: any, index: number) => {
        if (!component.type) {
          throw new Error(`Component ${index} must have a type`);
        }
        
        // Validate text length based on component type
        if (component.text && component.type === 'BODY' && component.text.length > 1024) {
          component.text = component.text.substring(0, 1021) + '...';
        }
        
        if (component.text && (component.type === 'HEADER' || component.type === 'FOOTER') && component.text.length > 60) {
          component.text = component.text.substring(0, 57) + '...';
        }
      });

      return template;
    } catch (error) {
      this.logger.error('Failed to parse AI response:', error);
      throw new BadRequestException('Failed to parse AI-generated template');
    }
  }
  public async transcribeAudio(file: any, audioFormat?: string): Promise<string> {
    try {
      // Option 1: Use Google Speech-to-Text (requires Google Cloud credentials)
      // return await this.transcribeWithGoogleSpeechToText(file.buffer, audioFormat);
      
      // Option 2: Use OpenAI Whisper (requires OpenAI API key)
      return await this.transcribeWithOpenAIWhisper(file);
      
    } catch (error) {
      this.logger.error('Transcription failed:', error);
      throw new BadRequestException(`Audio transcription failed: ${error.message}`);
    }
  }

  private async transcribeWithOpenAIWhisper(file: any): Promise<string> {
    try {
      const openaiApiKey = this.configService.get<string>('OPENAI_API_KEY');
      if (!openaiApiKey) {
        throw new Error('OPENAI_API_KEY is required for Whisper transcription');
      }
      
      // Create form data for OpenAI Whisper API
      const formData = new FormData();
      
      // Convert the file buffer to a Blob
      const blob = new Blob([file.buffer], { type: file.mimetype });
      formData.append('file', blob, file.originalname || 'audio.mp3');
      formData.append('model', 'whisper-1');
      formData.append('response_format', 'text');

      const response = await axios.post('https://api.openai.com/v1/audio/transcriptions', formData, {
        headers: {
          'Authorization': `Bearer ${openaiApiKey}`,
          // Don't set Content-Type manually, let axios set it with boundary
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('OpenAI Whisper API error:', error.response?.data || error.message);
      throw new Error(`OpenAI Whisper transcription failed: ${error.response?.data?.error || error.message}`);
    }
  }
}
