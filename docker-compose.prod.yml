version: '3.8'

# Production Docker Compose configuration
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

services:
  redis:
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    command: >
      redis-server
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --bind 0.0.0.0
      --tcp-keepalive 60
      --timeout 300
      --tcp-backlog 511
      --databases 16
      --requirepass ${REDIS_PASSWORD}
    # Remove port mapping for security (only accessible within Docker network)
    ports: []
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  app:
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    # Production logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Disable Redis Commander in production for security
  redis-commander:
    profiles:
      - debug
    environment:
      - REDIS_HOSTS=local:redis:6379:0:${REDIS_PASSWORD}
      - HTTP_USER=${REDIS_COMMANDER_USER:-admin}
      - HTTP_PASSWORD=${REDIS_COMMANDER_PASSWORD}
