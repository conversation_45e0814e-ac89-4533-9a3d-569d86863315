# 🏗️ Meta Media Upload Architecture

## 📋 Overview
This document describes the architecture, design patterns, and technical implementation of the Meta Media Upload module.

## 🎯 Design Principles

### 1. **Separation of Concerns**
- **Controller**: Handles HTTP requests/responses and validation
- **Service**: Contains business logic and Meta API integration
- **DTOs**: Data validation and transfer objects
- **Utils**: Reusable utility functions and constants

### 2. **Single Responsibility Principle**
Each class has one clear responsibility:
- `MetaMediaUploadService`: Meta API integration and upload logic
- `MetaMediaUploadController`: HTTP endpoint handling
- `UploadResponseUtil`: Response formatting
- `UploadValidationUtil`: Input validation
- `UploadConstantsUtil`: Configuration and constants

### 3. **Dependency Injection**
All dependencies are injected through NestJS DI container:
```typescript
constructor(
  private readonly configService: ConfigService,
) {}
```

## 🏛️ Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                Meta Media Upload Module                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Controller  │  │   Service   │  │    Utils    │        │
│  │             │  │             │  │             │        │
│  │ • start()   │  │ • start()   │  │ • Response  │        │
│  │ • upload()  │  │ • upload()  │  │ • Validation│        │
│  │ • resume()  │  │ • resume()  │  │ • Constants │        │
│  │ • health()  │  │             │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│         │                 │                 │             │
│         │                 │                 │             │
│         ▼                 ▼                 ▼             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   AuthGuard │  │ Meta API    │  │   DTOs      │        │
│  │             │  │ Integration │  │             │        │
│  │ • JWT Valid │  │ • HTTP Calls│  │ • Validation│        │
│  │ • User Ext  │  │ • Error Hand│  │ • Type Safe │        │
│  │             │  │ • Retry Log │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Component Details

### **1. MetaMediaUploadController**
**Purpose**: HTTP request/response handling
**Responsibilities**:
- Route HTTP requests to appropriate service methods
- Handle request validation through DTOs
- Format and send HTTP responses
- Extract tokens from request headers
- Validate authentication and authorization

**Key Methods**:
```typescript
@Post('start-session')
async startUploadSession(@Body() dto, @Headers() headers, @Request() req)

@Post('upload') 
@UseInterceptors(FileInterceptor('file'))
async uploadFile(@UploadedFile() file, @Body() dto, @Headers() headers, @Request() req)

@Get('resume/:uploadSessionId')
async resumeUpload(@Body() dto, @Headers() headers, @Request() req)
```

### **2. MetaMediaUploadService**
**Purpose**: Business logic implementation
**Responsibilities**:
- Integrate with Meta's Resumable Upload API
- Handle file upload session management
- Process file data uploads
- Manage upload session resumption
- Error handling and validation

**Key Methods**:
```typescript
async startUploadSession(appId: string, accessToken: string, dto: StartUploadSessionDto): Promise<any>
async uploadFile(uploadSessionId: string, accessToken: string, fileBuffer: Buffer, fileOffset: number): Promise<any>
async resumeUpload(uploadSessionId: string, accessToken: string): Promise<any>
```

### **3. Utility Classes**

#### **UploadResponseUtil**
**Purpose**: Standardized response formatting
**Responsibilities**:
- Create consistent API responses
- Format success and error responses
- Add timestamps and status codes
- Structure data according to API standards

#### **UploadValidationUtil**
**Purpose**: Input validation and file handling
**Responsibilities**:
- Validate file types and sizes
- Check upload session IDs
- Validate file offsets
- Extract MIME types from extensions
- Sanitize input parameters

#### **UploadConstantsUtil**
**Purpose**: Configuration and constants management
**Responsibilities**:
- Define Meta API endpoints and versions
- Set file size and type limits
- Provide error messages and codes
- Configure retry and timeout settings

## 🔄 Data Flow

### **Upload Session Creation Flow**
```
Client Request → AuthGuard → Controller → Service → Meta API
     ↓              ↓           ↓          ↓         ↓
JWT Token → Validate Token → Extract User → Start Session → Create Upload Session
     ↓              ↓           ↓          ↓         ↓
Response ← Format Response ← Process Data ← Handle Response ← Meta Response
```

### **File Upload Flow**
```
Client Request → AuthGuard → Controller → Service → Meta API
     ↓              ↓           ↓          ↓         ↓
File Data → Validate Token → Validate File → Upload Data → Process Upload
     ↓              ↓           ↓          ↓         ↓
Response ← Format Response ← Handle Result ← Handle Response ← Meta Response
```

### **Resume Upload Flow**
```
Client Request → AuthGuard → Controller → Service → Meta API
     ↓              ↓           ↓          ↓         ↓
Session ID → Validate Token → Validate ID → Check Status → Get Upload Status
     ↓              ↓           ↓          ↓         ↓
Response ← Format Response ← Process Data ← Handle Response ← Meta Response
```

## 🛡️ Security Architecture

### **Authentication Flow**
```
Client → AuthGuard → AuthService → Supabase → Database
   ↓         ↓           ↓           ↓         ↓
Token → Validate → Business → Auth API → User Data
Extract   Token      Logic      Call      Storage
```

### **Security Layers**
- **Input Validation**: DTO-based validation with class-validator
- **Authentication**: JWT token validation through AuthGuard
- **Authorization**: User context extraction and validation
- **File Validation**: Type, size, and content validation
- **Transport Security**: HTTPS/TLS for all communications
- **API Security**: Meta API token validation and error handling

## 🔧 Meta API Integration

### **API Configuration**
```typescript
// Meta API Base Configuration
const META_API_CONFIG = {
  baseUrl: 'https://graph.facebook.com/v23.0',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
};
```

### **Supported File Types**
```typescript
const SUPPORTED_FILE_TYPES = {
  PDF: 'application/pdf',
  JPEG: 'image/jpeg',
  JPG: 'image/jpg',
  PNG: 'image/png',
  MP4: 'video/mp4',
};
```

### **Error Handling Strategy**
```typescript
// Meta API Error Mapping
const META_ERROR_CODES = {
  INVALID_TOKEN: 190,
  PERMISSION_DENIED: 200,
  INVALID_PARAMETER: 100,
  TEMPORARY_ISSUE: 368,
};
```

## 📊 Performance Considerations

### **File Upload Optimization**
- **Chunked Uploads**: Support for resuming interrupted uploads
- **Timeout Handling**: Configurable timeouts for large files
- **Retry Logic**: Automatic retry for transient failures
- **Memory Management**: Efficient buffer handling for file data

### **Caching Strategy**
- **Session Management**: Track upload sessions and progress
- **Error Caching**: Cache common error responses
- **Validation Caching**: Cache file type and size validations

## 🔍 Monitoring and Logging

### **Logging Strategy**
```typescript
// Structured logging with context
this.logger.log(`User ${userId} starting upload session for file: ${fileName}`);
this.logger.error('Failed to start upload session:', error.response?.data || error.message);
this.logger.debug(`Meta API request - URL: ${url}, Params:`, params);
```

### **Metrics Collection**
- Upload session creation rate
- File upload success/failure rates
- Upload completion times
- Error frequency by type
- Meta API response times

## 🧪 Testing Architecture

### **Unit Testing**
- Service method testing with mocked Meta API
- Controller endpoint testing with mocked requests
- Utility function testing with various inputs
- Error handling scenario testing

### **Integration Testing**
- End-to-end upload flow testing
- Meta API integration testing
- Authentication flow testing
- Error scenario testing

### **Test Structure**
```
__tests__/
├── unit/
│   ├── meta-media-upload.service.spec.ts
│   ├── meta-media-upload.controller.spec.ts
│   └── utils/
│       ├── upload-response.util.spec.ts
│       ├── upload-validation.util.spec.ts
│       └── upload-constants.util.spec.ts
├── integration/
│   ├── upload-flow.e2e.spec.ts
│   └── meta-api-integration.e2e.spec.ts
└── fixtures/
    ├── test-files/
    └── mock-responses/
```

## 🚀 Deployment Considerations

### **Environment Configuration**
```typescript
// Environment-specific settings
const DEPLOYMENT_CONFIG = {
  production: {
    metaApiTimeout: 60000,
    maxRetryAttempts: 5,
    logLevel: 'warn',
  },
  development: {
    metaApiTimeout: 30000,
    maxRetryAttempts: 3,
    logLevel: 'debug',
  },
};
```

### **Scalability Features**
- **Horizontal Scaling**: Stateless service design
- **Load Balancing**: Support for multiple instances
- **Resource Management**: Efficient memory and CPU usage
- **Queue Integration**: Support for background processing

## 🔄 Future Enhancements

### **Planned Features**
- **Batch Upload**: Support for multiple file uploads
- **Progress Tracking**: Real-time upload progress updates
- **Compression**: Automatic file compression before upload
- **Encryption**: Client-side file encryption support
- **CDN Integration**: Direct upload to content delivery networks

### **Architecture Evolution**
- **Microservice Migration**: Extract to separate microservice
- **Event-Driven Architecture**: Implement event sourcing
- **GraphQL Support**: Add GraphQL endpoints
- **WebSocket Integration**: Real-time upload progress
