import { Injectable, Logger, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import { ConfigService } from '@nestjs/config';

export interface CampaignMessage {
  campaignId: string;
  contactId: string;
  phoneNumber: string;
  countryCode: string;
  templateId: string;
  phoneNumberId: string;
  variableMapping: Record<string, any>;
  workspaceId: number;
  userId: string;
  retryCount: number;
  priority: 'HIGH' | 'NORMAL' | 'LOW';
  scheduledAt?: Date;
}

export interface CampaignBatchMessage {
  campaignId: string;
  messages: CampaignMessage[];
  workspaceId: number;
  userId: string;
}

@Injectable()
export class QueueService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    private configService: ConfigService,
    @InjectQueue('campaign-messages') private campaignQueue: Queue,
    @InjectQueue('scheduled-campaign-messages') private scheduledQueue: Queue,
    @InjectQueue('campaign-retry-messages') private retryQueue: Queue,
  ) {}

  async onModuleInit() {
    this.logger.log('Queue Service initialized');
    await this.setupQueueProcessors();
  }

  async onModuleDestroy() {
    this.logger.log('Queue Service shutting down');
  }

  private async setupQueueProcessors() {
    // Setup queue event listeners
    this.campaignQueue.on('completed', (job: Job) => {
      this.logger.debug(`Campaign message job ${job.id} completed`);
    });

    this.campaignQueue.on('failed', (job: Job, err: Error) => {
      this.logger.error(`Campaign message job ${job.id} failed:`, err.message);
    });

    this.scheduledQueue.on('completed', (job: Job) => {
      this.logger.debug(`Scheduled campaign job ${job.id} completed`);
    });

    this.scheduledQueue.on('failed', (job: Job, err: Error) => {
      this.logger.error(`Scheduled campaign job ${job.id} failed:`, err.message);
    });

    this.retryQueue.on('completed', (job: Job) => {
      this.logger.debug(`Retry campaign job ${job.id} completed`);
    });

    this.retryQueue.on('failed', (job: Job, err: Error) => {
      this.logger.error(`Retry campaign job ${job.id} failed:`, err.message);
    });
  }

  async sendCampaignMessage(message: CampaignMessage): Promise<Job> {
    try {
      const jobOptions = {
        priority: this.getPriorityValue(message.priority),
        attempts: 3,
        backoff: {
          type: 'exponential' as const,
          delay: 2000,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      };

      const job = await this.campaignQueue.add('process-campaign-message', message, jobOptions);

      this.logger.debug(`Campaign message queued for campaign ${message.campaignId} to ${message.phoneNumber}`);
      return job;
    } catch (error) {
      this.logger.error(`Failed to queue campaign message: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendCampaignBatch(batchMessage: CampaignBatchMessage): Promise<Job[]> {
    try {
      const jobs: Job[] = [];
      const jobOptions = {
        priority: 5, // Normal priority for batch
        attempts: 3,
        backoff: {
          type: 'exponential' as const,
          delay: 2000,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      };

      // Add each message as a separate job for better parallel processing
      for (const message of batchMessage.messages) {
        const job = await this.campaignQueue.add('process-campaign-message', message, jobOptions);
        jobs.push(job);
      }

      this.logger.debug(`Campaign batch queued: ${batchMessage.messages.length} messages for campaign ${batchMessage.campaignId}`);
      return jobs;
    } catch (error) {
      this.logger.error(`Failed to queue campaign batch: ${error.message}`, error.stack);
      throw error;
    }
  }

  async scheduleCampaignMessage(message: CampaignMessage, delay: number): Promise<Job> {
    try {
      const jobOptions = {
        priority: this.getPriorityValue(message.priority),
        delay: delay,
        attempts: 3,
        backoff: {
          type: 'exponential' as const,
          delay: 2000,
        },
        removeOnComplete: 100,
        removeOnFail: 50,
      };

      const job = await this.scheduledQueue.add('process-scheduled-campaign-message', message, jobOptions);

      this.logger.debug(`Scheduled campaign message queued for campaign ${message.campaignId} with delay ${delay}ms`);
      return job;
    } catch (error) {
      this.logger.error(`Failed to schedule campaign message: ${error.message}`, error.stack);
      throw error;
    }
  }

  async retryCampaignMessage(message: CampaignMessage, delay: number = 5000): Promise<Job> {
    try {
      const jobOptions = {
        priority: this.getPriorityValue(message.priority),
        delay: delay,
        attempts: 5, // More attempts for retry
        backoff: {
          type: 'exponential' as const,
          delay: 5000,
        },
        removeOnComplete: 50,
        removeOnFail: 25,
      };

      const job = await this.retryQueue.add('process-retry-campaign-message', message, jobOptions);

      this.logger.debug(`Retry campaign message queued for campaign ${message.campaignId} with delay ${delay}ms`);
      return job;
    } catch (error) {
      this.logger.error(`Failed to queue retry campaign message: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getQueueStats(): Promise<any> {
    try {
      const [campaignStats, scheduledStats, retryStats] = await Promise.all([
        this.campaignQueue.getJobCounts(),
        this.scheduledQueue.getJobCounts(),
        this.retryQueue.getJobCounts(),
      ]);

      return {
        campaignMessages: campaignStats,
        scheduledMessages: scheduledStats,
        retryMessages: retryStats,
        total: {
          waiting: campaignStats.waiting + scheduledStats.waiting + retryStats.waiting,
          active: campaignStats.active + scheduledStats.active + retryStats.active,
          completed: campaignStats.completed + scheduledStats.completed + retryStats.completed,
          failed: campaignStats.failed + scheduledStats.failed + retryStats.failed,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get queue stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  async pauseQueue(queueName: string): Promise<void> {
    try {
      let queue: Queue;
      switch (queueName) {
        case 'campaign-messages':
          queue = this.campaignQueue;
          break;
        case 'scheduled-campaign-messages':
          queue = this.scheduledQueue;
          break;
        case 'campaign-retry-messages':
          queue = this.retryQueue;
          break;
        default:
          throw new Error(`Unknown queue: ${queueName}`);
      }

      await queue.pause();
      this.logger.log(`Queue ${queueName} paused`);
    } catch (error) {
      this.logger.error(`Failed to pause queue ${queueName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async resumeQueue(queueName: string): Promise<void> {
    try {
      let queue: Queue;
      switch (queueName) {
        case 'campaign-messages':
          queue = this.campaignQueue;
          break;
        case 'scheduled-campaign-messages':
          queue = this.scheduledQueue;
          break;
        case 'campaign-retry-messages':
          queue = this.retryQueue;
          break;
        default:
          throw new Error(`Unknown queue: ${queueName}`);
      }

      await queue.resume();
      this.logger.log(`Queue ${queueName} resumed`);
    } catch (error) {
      this.logger.error(`Failed to resume queue ${queueName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async clearQueue(queueName: string): Promise<void> {
    try {
      let queue: Queue;
      switch (queueName) {
        case 'campaign-messages':
          queue = this.campaignQueue;
          break;
        case 'scheduled-campaign-messages':
          queue = this.scheduledQueue;
          break;
        case 'campaign-retry-messages':
          queue = this.retryQueue;
          break;
        default:
          throw new Error(`Unknown queue: ${queueName}`);
      }

      await queue.empty();
      this.logger.log(`Queue ${queueName} cleared`);
    } catch (error) {
      this.logger.error(`Failed to clear queue ${queueName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private getPriorityValue(priority: 'HIGH' | 'NORMAL' | 'LOW'): number {
    switch (priority) {
      case 'HIGH':
        return 10;
      case 'NORMAL':
        return 5;
      case 'LOW':
        return 1;
      default:
        return 5;
    }
  }
}
