# Meta-Onboarding Module Documentation

## Overview

The Meta-Onboarding module manages WhatsApp Business API credentials and integration setup for the automation platform. It handles the creation, management, and validation of Meta (Facebook) credentials required for WhatsApp Business API access within workspace contexts.

## Table of Contents

1. [Architecture](#architecture)
2. [API Endpoints](#api-endpoints)
3. [Data Models](#data-models)
4. [Security](#security)
5. [Usage Examples](#usage-examples)
6. [Testing](#testing)
7. [Deployment](#deployment)

## Architecture

### Module Structure

```
meta-onboarding/
├── dto/                          # Data Transfer Objects
│   ├── create-meta-credentials.dto.ts
│   ├── update-meta-credentials.dto.ts
│   ├── meta-credentials-query.dto.ts
│   └── index.ts
├── utils/                        # Utility Classes
│   ├── meta-onboarding-constants.util.ts
│   ├── meta-onboarding-validation.util.ts
│   └── meta-onboarding-response.util.ts
├── docs/                         # Documentation
│   └── INDEX.md
├── meta-onboarding.controller.ts  # Controller
├── meta-onboarding.service.ts     # Service
├── meta-onboarding.module.ts      # Module
└── README.md                      # Module Summary
```

### Key Components

1. **Controller**: Handles HTTP requests and responses
2. **Service**: Contains business logic and data operations
3. **DTOs**: Validate and structure incoming data
4. **Utilities**: Provide reusable validation, constants, and response formatting
5. **Constants**: Centralized configuration and messages

### Design Patterns

- **Repository Pattern**: Data access abstraction through Supabase
- **DTO Pattern**: Data validation and transformation
- **Utility Pattern**: Reusable business logic
- **Response Standardization**: Consistent API responses
- **Error Handling**: Comprehensive error management

## API Endpoints

### Credentials Management

#### Create Meta Credentials
```http
POST /meta-onboarding/connect
Authorization: Bearer <token>
Content-Type: application/json

{
  "whatsapp_business_id": "123456789",
  "phone_number_id": "987654321",
  "access_token": "your-meta-access-token",
  "status": "Active"
}
```

#### Get User Credentials
```http
GET /meta-onboarding/credentials?page=1&limit=10
Authorization: Bearer <token>
```

#### Get Workspace Credentials
```http
GET /meta-onboarding/workspace/{workspaceId}/credentials?page=1&limit=10
Authorization: Bearer <token>
```

#### Get Credentials by ID
```http
GET /meta-onboarding/credentials/{id}
Authorization: Bearer <token>
```

#### Update Credentials
```http
PUT /meta-onboarding/credentials/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "whatsapp_business_id": "updated-business-id",
  "status": "Inactive"
}
```

#### Delete Credentials
```http
DELETE /meta-onboarding/credentials/{id}
Authorization: Bearer <token>
```

## Data Models

### Meta Credentials
```typescript
interface MetaCredentials {
  id: string;
  whatsapp_business_id: string;
  phone_number_id: string;
  access_token: string;
  status: 'Active' | 'Inactive';
  workspace_id: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}
```

### Credentials Query Parameters
```typescript
interface MetaCredentialsQuery {
  page?: number;    // Default: 1
  limit?: number;   // Default: 10, Max: 100
}
```

## Security

### Authentication & Authorization
- All endpoints require valid JWT authentication
- Workspace-scoped access control
- User context validation for all operations

### Input Validation
- Comprehensive DTO validation using class-validator
- Business logic validation in utility classes
- SQL injection prevention through parameterized queries

### Data Protection
- Access token encryption in transit and at rest
- Workspace isolation for credential access
- Audit logging for all credential operations

### Security Best Practices
- Principle of least privilege
- Credential rotation support
- Secure token storage
- Input sanitization and validation

## Usage Examples

### Setting Up WhatsApp Business Integration

```typescript
// 1. Create meta credentials
const credentials = await metaOnboardingService.createMetaCredentials({
  whatsapp_business_id: "123456789",
  phone_number_id: "987654321",
  access_token: "your-meta-access-token",
  status: "Active"
}, req);

// 2. Get workspace credentials
const workspaceCredentials = await metaOnboardingService.getMetaCredentialsByWorkspace(
  "workspace-id",
  req,
  { page: 1, limit: 10 }
);

// 3. Update credentials if needed
const updatedCredentials = await metaOnboardingService.updateMetaCredentials(
  credentials.data.credentials.id,
  {
    status: "Inactive"
  },
  req
);
```

### Managing Multiple Credentials

```typescript
// Get all user's credentials with pagination
const userCredentials = await metaOnboardingService.getMetaCredentialsByUser(req, {
  page: 1,
  limit: 20
});

// Get specific credentials by ID
const specificCredentials = await metaOnboardingService.getMetaCredentialsById(
  "credentials-id",
  req
);

// Delete credentials when no longer needed
await metaOnboardingService.deleteMetaCredentials("credentials-id", req);
```

## Testing

### Unit Tests
- Service method testing with mocked dependencies
- Validation utility testing
- Response utility testing
- Error handling testing

### Integration Tests
- End-to-end API testing
- Database interaction testing
- Authentication flow testing
- Credential validation testing

### Test Coverage
- Minimum 80% code coverage
- All critical paths tested
- Error scenarios covered
- Security testing included

## Deployment

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...

# Authentication
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Logging
LOG_LEVEL=info
```

### Database Setup
1. Ensure Supabase tables exist:
   - `automate_whatsapp_meta_credentials`

2. Set up proper indexes for performance:
   - `workspace_id` index
   - `created_by` index
   - `whatsapp_business_id` unique index per workspace

3. Configure row-level security policies

### Meta API Setup
1. Create Meta Business App
2. Configure WhatsApp Business API
3. Generate access tokens
4. Set up webhook endpoints

### Monitoring
- Application performance monitoring
- Error tracking and alerting
- Database performance monitoring
- Security event logging
- Credential usage monitoring

### Scaling Considerations
- Database connection pooling
- Caching for frequently accessed credentials
- Rate limiting for API endpoints
- Load balancing for high availability
- Credential rotation automation

## Integration with WhatsApp Business API

### Required Meta Credentials
- **WhatsApp Business ID**: Unique identifier for your WhatsApp Business account
- **Phone Number ID**: Identifier for the phone number associated with your business
- **Access Token**: Long-lived access token for API authentication

### API Endpoints Used
- Message sending
- Template management
- Webhook verification
- Business profile management

### Error Handling
- Invalid credentials detection
- Token expiration handling
- Rate limit management
- Network error recovery
