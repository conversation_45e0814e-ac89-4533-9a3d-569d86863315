# Workspace Module Architecture

## Overview

The Workspace Module follows a clean, modular architecture that promotes maintainability, testability, and scalability. It implements the same patterns as the Auth module for consistency across the application.

## Architecture Principles

### 1. Separation of Concerns
- **Controller**: Handles HTTP requests and responses
- **Service**: Contains business logic and data operations
- **DTOs**: Define data transfer objects with validation
- **Utilities**: Reusable helper functions and constants

### 2. Dependency Injection
- Uses NestJS dependency injection for loose coupling
- Services are injected into controllers
- External dependencies (SupabaseService) are injected into services

### 3. Consistent Response Patterns
- All endpoints return standardized response format
- Error handling is centralized and consistent
- HTTP status codes are properly mapped to business logic

## Module Structure

```
src/workspace/
├── dto/                           # Data Transfer Objects
│   ├── workspace.dto.ts          # Request/response DTOs
│   └── index.ts                  # DTO exports
├── utils/                        # Utility Classes
│   ├── workspace-constants.util.ts    # Constants and configuration
│   ├── workspace-response.util.ts     # Response formatting utilities
│   └── workspace-validation.util.ts   # Validation logic
├── docs/                         # Documentation
├── workspace.controller.ts       # REST API controller
├── workspace.service.ts          # Business logic service
├── workspace.module.ts           # NestJS module definition
├── workspace.service.spec.ts     # Service unit tests
└── workspace.controller.spec.ts  # Controller unit tests
```

## Component Responsibilities

### WorkspaceController
- **Purpose**: Handle HTTP requests and responses
- **Responsibilities**:
  - Route HTTP requests to appropriate service methods
  - Validate request data using DTOs
  - Apply authentication guards
  - Return standardized responses
- **Dependencies**: WorkspaceService, AuthGuard

### WorkspaceService
- **Purpose**: Implement business logic and data operations
- **Responsibilities**:
  - Validate business rules
  - Coordinate with external services (Supabase)
  - Handle complex operations (workspace creation with rollback)
  - Manage error scenarios
- **Dependencies**: SupabaseService

### DTOs (Data Transfer Objects)
- **Purpose**: Define and validate data structures
- **Responsibilities**:
  - Input validation using class-validator
  - Type safety for request/response data
  - Documentation of API contracts
- **Types**:
  - `CreateWorkspaceDto`: Workspace creation data
  - `CreateMemberDto`: Member addition data
  - `UpdateWorkspaceDto`: Workspace update data
  - `WorkspaceMembersQueryDto`: Query parameters

### Utility Classes

#### WorkspaceConstantsUtil
- **Purpose**: Centralize constants and configuration
- **Contains**:
  - Default values (trial days, status, roles)
  - Error messages
  - Success messages
  - HTTP status codes
  - Pagination defaults

#### WorkspaceResponseUtil
- **Purpose**: Standardize response formatting
- **Methods**:
  - `createSuccessResponse()`: Format success responses
  - `createErrorResponse()`: Format error responses
  - `createPaginationMetadata()`: Generate pagination info
  - `createWorkspaceCreationData()`: Format workspace creation response
  - `createMemberAdditionData()`: Format member addition response

#### WorkspaceValidationUtil
- **Purpose**: Centralize validation logic
- **Methods**:
  - `validateUserContext()`: Validate user from request
  - `validateUserProfile()`: Validate user profile data
  - `validateWorkspaceExists()`: Validate workspace existence
  - `validatePaginationParams()`: Validate pagination parameters
  - `validateSortParams()`: Validate sorting parameters

## Data Flow

### 1. Workspace Creation Flow
```
Request → Controller → Service → SupabaseService → Database
                ↓
Response ← Controller ← Service ← SupabaseService ← Database
```

**Detailed Steps**:
1. Controller receives POST request with `CreateWorkspaceDto`
2. Controller calls `service.create()`
3. Service validates user context and profile
4. Service checks for existing workspace
5. Service creates workspace in database
6. Service updates user profile with workspace ID
7. Service adds user as workspace member
8. Service creates admin role and permissions
9. Service adds user to automate WhatsApp members
10. Service returns formatted success response

### 2. Member Addition Flow
```
Request → Controller → Service → SupabaseService → Database
                ↓
Response ← Controller ← Service ← SupabaseService ← Database
```

**Detailed Steps**:
1. Controller receives POST request with `CreateMemberDto`
2. Controller calls `service.addMemberToWorkspace()`
3. Service validates user context and workspace
4. Service checks for existing user by email
5. Service creates new user in Supabase Auth
6. Service creates user profile
7. Service adds user to workspace members
8. Service adds user to automate WhatsApp members (if applicable)
9. Service returns formatted success response

### 3. Get Members Flow
```
Request → Controller → Service → SupabaseService → Database
                ↓
Response ← Controller ← Service ← SupabaseService ← Database
```

**Detailed Steps**:
1. Controller receives GET request with query parameters
2. Controller calls `service.getWorkspaceMembers()`
3. Service validates user context and workspace
4. Service validates and processes query parameters
5. Service queries database with filters and pagination
6. Service formats response with pagination metadata
7. Service returns formatted success response

## Error Handling Strategy

### 1. Validation Errors
- **Level**: DTO validation (class-validator)
- **Response**: 400 Bad Request
- **Handling**: Automatic by NestJS validation pipe

### 2. Business Logic Errors
- **Level**: Service layer
- **Response**: Appropriate HTTP status codes
- **Handling**: Custom exceptions with specific error messages

### 3. External Service Errors
- **Level**: SupabaseService integration
- **Response**: 500 Internal Server Error
- **Handling**: Logged and converted to user-friendly messages

### 4. Rollback Strategy
- **Scenario**: Workspace creation failure
- **Implementation**: `handleWorkspaceCreationRollback()`
- **Actions**: Delete workspace, revert user profile

## Security Considerations

### 1. Authentication
- All endpoints protected by `AuthGuard`
- JWT token validation required
- User context extracted from token

### 2. Authorization
- Workspace operations scoped to workspace members
- User can only access their own workspace
- Admin role required for member management

### 3. Input Validation
- DTO validation prevents malicious input
- Phone number format validation
- Email format validation
- Password strength requirements

### 4. Data Isolation
- Workspace data isolated by workspace ID
- User data isolated by user ID
- No cross-workspace data access

## Testing Strategy

### 1. Unit Tests
- **Service Tests**: Test business logic in isolation
- **Controller Tests**: Test HTTP handling and response formatting
- **Utility Tests**: Test helper functions and validation

### 2. Integration Tests
- **Database Integration**: Test Supabase operations
- **End-to-End**: Test complete request/response cycles

### 3. Test Coverage
- **Target**: 90%+ code coverage
- **Focus Areas**: Business logic, error handling, validation

## Performance Considerations

### 1. Database Queries
- Efficient pagination for large member lists
- Indexed queries on workspace_id and user_id
- Minimal data transfer with selective fields

### 2. Caching Strategy
- User profile caching (if implemented)
- Workspace metadata caching (if implemented)

### 3. Rate Limiting
- API rate limiting at controller level
- Workspace creation rate limiting
- Member addition rate limiting

## Scalability Considerations

### 1. Horizontal Scaling
- Stateless service design
- Database connection pooling
- Load balancer compatibility

### 2. Data Growth
- Pagination for large datasets
- Efficient query patterns
- Database indexing strategy

### 3. Feature Extensions
- Modular utility classes
- Extensible DTO structure
- Plugin architecture for new features

## Monitoring and Logging

### 1. Logging Strategy
- Structured logging with context
- Error logging with stack traces
- Performance metrics logging

### 2. Monitoring Points
- API response times
- Error rates by endpoint
- Database query performance
- External service integration health

### 3. Alerting
- High error rates
- Slow response times
- Database connection issues
- External service failures


