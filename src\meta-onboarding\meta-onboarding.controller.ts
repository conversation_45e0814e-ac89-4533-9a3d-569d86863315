import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import { MetaOnboardingService } from './meta-onboarding.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { CreateMetaCredentialsDto, UpdateMetaCredentialsDto, MetaCredentialsQueryDto } from './dto';

/**
 * Refactored MetaOnboardingController with improved structure and consistent response handling
 */
@Controller('meta-onboarding')
export class MetaOnboardingController {
  constructor(private readonly metaOnboardingService: MetaOnboardingService) {}

  /**
   * Create meta credentials endpoint
   */
  @Post('connect')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async connectPhone(@Body() createMetaCredentialsDto: CreateMetaCredentialsDto, @Req() req: any) {
    return await this.metaOnboardingService.createMetaCredentials(createMetaCredentialsDto, req);
  }

  /**
   * Get user credentials endpoint
   */
  @Get('credentials')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getCredentials(@Query() query: MetaCredentialsQueryDto, @Req() req: any) {
    return await this.metaOnboardingService.getMetaCredentialsByUser(req, query);
  }

  /**
   * Get workspace credentials endpoint
   */
  @Get('workspace/:workspaceId/credentials')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getCredentialsByWorkspace(
    @Param('workspaceId') workspaceId: string,
    @Query() query: MetaCredentialsQueryDto,
    @Req() req: any
  ) {
    return await this.metaOnboardingService.getMetaCredentialsByWorkspace(workspaceId, req, query);
  }

  /**
   * Get credentials by ID endpoint
   */
  @Get('credentials/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getCredentialsById(@Param('id') id: string, @Req() req: any) {
    return await this.metaOnboardingService.getMetaCredentialsById(id, req);
  }

  /**
   * Update credentials endpoint
   */
  @Put('credentials/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async updateCredentials(
    @Param('id') id: string,
    @Body() updateDto: UpdateMetaCredentialsDto,
    @Req() req: any,
  ) {
    return await this.metaOnboardingService.updateMetaCredentials(id, updateDto, req);
  }

  /**
   * Delete credentials endpoint
   */
  @Delete('credentials/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async deleteCredentials(@Param('id') id: string, @Req() req: any) {
    return await this.metaOnboardingService.deleteMetaCredentials(id, req);
  }
}
