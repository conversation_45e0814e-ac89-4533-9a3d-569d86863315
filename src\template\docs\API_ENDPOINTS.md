# Template Module API Endpoints

## Base URL

All endpoints are prefixed with `/templates`

## Authentication

All endpoints require authentication via JW<PERSON> token in the Authorization header:

```http
Authorization: Bearer <jwt_token>
```

## Response Format

All responses follow a standardized format:

### Success Response
```json
{
  "status": "success",
  "code": 200,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response
```json
{
  "status": "error",
  "code": 400,
  "message": "Error description",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Template CRUD Operations

### Create Template

Creates a new template using Meta's exact format requirements.

**Endpoint**: `POST /templates`

**Request Body**:
```json
{
  "name": "appointment_confirmation",
  "description": "Confirm customer appointments",
  "category": "UTILITY",
  "language": "en",
  "waba_id": "*********",
  "components": [
    {
      "type": "HEADER",
      "format": "TEXT",
      "text": "{{1}}",
      "example": {
        "header_text": ["Appointment Confirmation"]
      }
    },
    {
      "type": "BODY",
      "text": "Hi {{1}}, your appointment is confirmed for {{2}}. Please contact us if you need to reschedule.",
      "example": {
        "body_text": [["John", "2:00 PM"]]
      }
    },
    {
      "type": "FOOTER",
      "text": "Thank you for choosing us!"
    }
  ]
}
```

**Response** (201 Created):
```json
{
  "status": "success",
  "code": 201,
  "message": "Template created successfully",
  "data": {
    "template": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "appointment_confirmation",
      "meta_template_id": "meta_template_123",
      "meta_template_status": "PENDING",
      "category": "UTILITY",
      "language": "en",
      "components": [...],
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```


### Create Draft Template

Creates a template without submitting it to Meta for approval.

**Endpoint**: `POST /templates/draft`

**Request Body**: Same as Create Template

**Response** (201 Created):
```json
{
  "status": "success",
  "code": 201,
  "message": "Template created successfully in local DB only",
  "data": {
    "template": {
      "id": "550e8400-e29b-41d4-a716-************",
      "meta_template_id": null,
      "meta_template_status": "DRAFT",
      "is_active": false,
      ...
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Get User Templates

Retrieves templates created by the authenticated user.

**Endpoint**: `GET /templates`

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search term for name/description
- `category` (optional): Filter by category (MARKETING, UTILITY, AUTHENTICATION)
- `type` (optional): Filter by type (text, image, video, etc.)
- `language` (optional): Filter by language
- `status` (optional): Filter by Meta status (DRAFT, PENDING, APPROVED, etc.)
- `waba_id` (optional): Filter by WABA ID

**Example**: `GET /templates?page=1&limit=10&search=welcome&category=UTILITY`

**Response** (200 OK):
```json
{
  "status": "success",
  "code": 200,
  "message": "Templates retrieved successfully",
  "data": {
    "templates": [
      {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "welcome_message",
        "description": "Welcome new users",
        "language": "en",
        "components": [
          {
            "type": "BODY",
            "text": "Hello {{1}}, welcome!"
          }
        ],
        "meta_template_status": "APPROVED",
        "is_active": true,
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Get Workspace Templates

Retrieves templates for a specific workspace.

**Endpoint**: `GET /templates/workspace/{workspaceId}`

**Path Parameters**:
- `workspaceId`: Workspace ID

**Query Parameters**: Same as Get User Templates

**Response** (200 OK): Same format as Get User Templates

### Get Template by ID

Retrieves a specific template by its ID.

**Endpoint**: `GET /templates/{id}`

**Path Parameters**:
- `id`: Template ID

**Response** (200 OK):
```json
{
  "status": "success",
  "code": 200,
  "message": "Template retrieved successfully",
  "data": {
    "template": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "welcome_message",
      "description": "Welcome new users",
      "language": "en",
      "components": [
        {
          "type": "BODY",
          "text": "Hello {{1}}, welcome!"
        }
      ],
      "meta_template_id": "meta_template_123",
      "meta_template_status": "APPROVED",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Update Template

Updates an existing template.

**Endpoint**: `PUT /templates/{id}`

**Path Parameters**:
- `id`: Template ID

**Request Body** (all fields optional):
```json
{
  "name": "updated_welcome_message",
  "description": "Updated description",
  "language": "en",
  "components": [
    {
      "type": "HEADER",
      "format": "TEXT",
      "text": "Updated header"
    },
    {
      "type": "BODY",
      "text": "Updated content {{1}}"
    },
    {
      "type": "FOOTER",
      "text": "Updated footer"
    }
  ],
  "category": "MARKETING"
}
```

**Response** (200 OK):
```json
{
  "status": "success",
  "code": 200,
  "message": "Template updated successfully in both local DB and Meta",
  "data": {
    "template": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "updated_welcome_message",
      "updated_at": "2024-01-01T00:00:00.000Z",
      ...
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Delete Template

Deletes a template from both local database and Meta (if applicable).

**Endpoint**: `DELETE /templates/{wabaId}/template/{templateId}`

**Path Parameters**:
- `wabaId`: WhatsApp Business Account ID
- `templateId`: Template ID

**Response** (200 OK):
```json
{
  "status": "success",
  "code": 200,
  "message": "Template deleted successfully from both Meta and local database",
  "data": {
    "templateId": "550e8400-e29b-41d4-a716-************",
    "template_name": "welcome_message",
    "meta_template_status": "APPROVED",
    "deleted_from_meta": true
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Meta Integration

### Sync All Templates with Meta

Synchronizes all templates from Meta API to local database.

**Endpoint**: `GET /templates/meta/sync/waba/{wabaId}`

**Path Parameters**:
- `wabaId`: WhatsApp Business Account ID

**Response** (200 OK):
```json
{
  "status": "success",
  "code": 200,
  "message": "Bulk sync from Meta completed",
  "data": {
    "sync_results": [
      {
        "template_id": "550e8400-e29b-41d4-a716-************",
        "template_name": "meta_template_1",
        "meta_template_id": "meta_123",
        "status": "success"
      }
    ],
    "errors": [],
    "total_meta_templates": 10,
    "new_templates_found": 3,
    "successful_syncs": 3,
    "failed_syncs": 0
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Sync Single Template with Meta

Synchronizes a specific template with Meta API.

**Endpoint**: `POST /templates/{id}/sync`

**Path Parameters**:
- `id`: Template ID

**Response** (200 OK):
```json
{
  "status": "success",
  "code": 200,
  "message": "Template synced with Meta successfully",
  "data": {
    "local_template": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "welcome_message",
      "meta_template_id": "meta_template_123",
      "meta_template_status": "PENDING",
      "updated_at": "2024-01-01T00:00:00.000Z"
    },
    "meta_template": {
      "id": "meta_template_123",
      "name": "welcome_message",
      "status": "PENDING",
      "category": "UTILITY"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Get Meta Templates

Retrieves templates directly from Meta API.

**Endpoint**: `GET /templates/meta/templates`

**Response** (200 OK):
```json
{
  "status": "success",
  "code": 200,
  "message": "Meta templates retrieved successfully",
  "data": {
    "templates": [
      {
        "id": "meta_template_123",
        "name": "welcome_message",
        "status": "APPROVED",
        "category": "UTILITY",
        "language": "en",
        "components": [...]
      }
    ],
    "total": 10,
    "paging": {
      "cursors": {...}
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## AI Generation

### Generate AI Template

Generates a template using AI based on a text prompt.

**Endpoint**: `POST /templates/ai/generate`

**Request Body**:
```json
{
  "prompt": "Create a welcome message for new customers joining our e-commerce platform",
  "waba_id": "*********",
  "category": "MARKETING",
  "language": "en"
}
```

**Response** (201 Created):
```json
{
  "status": "success",
  "code": 201,
  "message": "AI template generated and saved as draft successfully",
  "data": {
    "template": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "ai_generated_welcome",
      "language": "en",
      "components": [
        {
          "type": "BODY",
          "text": "Welcome to our e-commerce platform! We're thrilled to have you join our community of satisfied customers."
        }
      ],
      "meta_template_status": "DRAFT",
      "ai_generated": true,
      "is_active": false,
      ...
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Generate Template from Voice

Generates a template from an audio file using speech-to-text and AI.

**Endpoint**: `POST /templates/voice/generate`

**Content-Type**: `multipart/form-data`

**Form Data**:
- `audioFile`: Audio file (required, max 10MB, audio formats only)
- `waba_id`: WhatsApp Business Account ID (required)

**Response** (201 Created): Same format as AI template generation

## Error Responses

### Validation Error (400)
```json
{
  "status": "error",
  "code": 400,
  "message": "Template name is required",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Unauthorized (401)
```json
{
  "status": "error",
  "code": 401,
  "message": "User not found",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Not Found (404)
```json
{
  "status": "error",
  "code": 404,
  "message": "Template not found",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Conflict (409)
```json
{
  "status": "error",
  "code": 409,
  "message": "Template with this name and language already exists in your workspace",
  "data": {
    "existing_template": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "welcome_message",
      "language": "en",
      "waba_id": "*********"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Rate Limiting

- **General endpoints**: 100 requests per minute per user
- **Meta API endpoints**: 10 requests per minute per user
- **AI generation**: 5 requests per minute per user
- **File upload**: 10 requests per minute per user

## Pagination

All list endpoints support pagination with the following parameters:

- `page`: Page number (1-based, default: 1)
- `limit`: Items per page (default: 10, max: 100)

Pagination metadata is included in the response:

```json
{
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

## Filtering and Sorting

### Available Filters

- `search`: Search in name and description
- `category`: Filter by template category
- `type`: Filter by template type
- `language`: Filter by language
- `status`: Filter by Meta template status
- `waba_id`: Filter by WhatsApp Business Account ID

### Sorting

- `sort_by`: Field to sort by (default: created_at)
- `sort_order`: Sort order (asc/desc, default: desc)

## File Upload

### Supported Audio Formats

- MP3 (.mp3)
- WAV (.wav)
- OGG (.ogg)
- M4A (.m4a)

### File Size Limits

- Maximum file size: 10MB
- Minimum file size: 1KB

### Upload Process

1. Send multipart/form-data request
2. File is validated for format and size
3. Audio is transcribed to text
4. Text is used as prompt for AI generation
5. Generated template is saved as draft
