import { Module } from '@nestjs/common';
import { MetaOnboardingController } from './meta-onboarding.controller';
import { MetaOnboardingService } from './meta-onboarding.service';
import { AuthModule } from 'src/auth/auth.module';
import { SupabaseModule } from 'src/supabase/supabase.module';

@Module({
  imports: [
    AuthModule,
    SupabaseModule
  ],
  controllers: [MetaOnboardingController],
  providers: [MetaOnboardingService],
  exports: [MetaOnboardingService]
})
export class MetaOnboardingModule {}
