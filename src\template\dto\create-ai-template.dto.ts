import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { TEMPLATE_CONSTANTS } from '../utils/template-constants.util';

export class CreateAiTemplateDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  prompt: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(TEMPLATE_CONSTANTS.VALIDATION.WABA_ID_MAX_LENGTH)
  waba_id: string;

  @IsOptional()
  @IsEnum(Object.values(TEMPLATE_CONSTANTS.TEMPLATE_CATEGORIES))
  category?: string;

  @IsOptional()
  @IsString()
  @MaxLength(TEMPLATE_CONSTANTS.VALIDATION.LANGUAGE_MAX_LENGTH)
  language?: string;
}
