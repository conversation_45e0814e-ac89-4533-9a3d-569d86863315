# Meta-Onboarding Module

## 🎯 **Overview**

The Meta-Onboarding module manages WhatsApp Business API credentials and integration setup for the automation platform. It handles the creation, management, and validation of Meta (Facebook) credentials required for WhatsApp Business API access within workspace contexts.

## 🚀 **Features**

- **Credential Management**: Create, read, update, and delete Meta credentials
- **Workspace Scoping**: All credentials are scoped to user workspaces
- **User Credentials**: Manage credentials created by specific users
- **Workspace Credentials**: Access all credentials within a workspace
- **Comprehensive Validation**: Input validation and business logic validation
- **Standardized Responses**: Consistent API response format
- **Error Handling**: Comprehensive error management with specific error types
- **Security**: Authentication required for all endpoints

## 📁 **Module Structure**

```
meta-onboarding/
├── dto/                          # Data Transfer Objects
│   ├── create-meta-credentials.dto.ts
│   ├── update-meta-credentials.dto.ts
│   ├── meta-credentials-query.dto.ts
│   └── index.ts
├── utils/                        # Utility Classes
│   ├── meta-onboarding-constants.util.ts
│   ├── meta-onboarding-validation.util.ts
│   └── meta-onboarding-response.util.ts
├── docs/                         # Documentation
│   └── INDEX.md
├── meta-onboarding.controller.ts  # Controller
├── meta-onboarding.service.ts     # Service
├── meta-onboarding.module.ts      # Module
└── README.md                      # This file
```

## 🔧 **API Endpoints**

### Credentials Management
- `POST /meta-onboarding/connect` - Create meta credentials
- `GET /meta-onboarding/credentials` - Get user credentials
- `GET /meta-onboarding/workspace/:workspaceId/credentials` - Get workspace credentials
- `GET /meta-onboarding/credentials/:id` - Get credentials by ID
- `PUT /meta-onboarding/credentials/:id` - Update credentials
- `DELETE /meta-onboarding/credentials/:id` - Delete credentials

## 🛡️ **Security Features**

- ✅ Authentication required for all endpoints
- ✅ Workspace-scoped access control
- ✅ Input validation and sanitization
- ✅ SQL injection prevention
- ✅ Access token security
- ✅ Comprehensive error handling
- ✅ Audit logging

## 📊 **Data Models**

### Meta Credentials
```typescript
interface MetaCredentials {
  id: string;
  whatsapp_business_id: string;
  phone_number_id: string;
  access_token: string;
  status: 'Active' | 'Inactive';
  workspace_id: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}
```

### Query Parameters
```typescript
interface MetaCredentialsQuery {
  page?: number;    // Default: 1
  limit?: number;   // Default: 10, Max: 100
}
```

## 🎯 **Usage Examples**

### Setting Up WhatsApp Business Integration
```typescript
// 1. Create meta credentials
const credentials = await metaOnboardingService.createMetaCredentials({
  whatsapp_business_id: "123456789",
  phone_number_id: "987654321",
  access_token: "your-meta-access-token",
  status: "Active"
}, req);

// 2. Get workspace credentials
const workspaceCredentials = await metaOnboardingService.getMetaCredentialsByWorkspace(
  "workspace-id",
  req,
  { page: 1, limit: 10 }
);

// 3. Update credentials if needed
const updatedCredentials = await metaOnboardingService.updateMetaCredentials(
  credentials.data.credentials.id,
  { status: "Inactive" },
  req
);
```

### Managing Multiple Credentials
```typescript
// Get all user's credentials with pagination
const userCredentials = await metaOnboardingService.getMetaCredentialsByUser(req, {
  page: 1,
  limit: 20
});

// Get specific credentials by ID
const specificCredentials = await metaOnboardingService.getMetaCredentialsById(
  "credentials-id",
  req
);

// Delete credentials when no longer needed
await metaOnboardingService.deleteMetaCredentials("credentials-id", req);
```

## 🔍 **Validation Rules**

### Credentials Validation
- WhatsApp Business ID: 1-50 characters, required
- Phone Number ID: 1-50 characters, required
- Access Token: 10-1000 characters, required
- Status: 'Active' or 'Inactive', defaults to 'Active'

### Query Parameters
- Page: Minimum 1, defaults to 1
- Limit: 1-100, defaults to 10

## 📈 **Performance**

- Optimized database queries with proper indexing
- Pagination support for large datasets
- Efficient workspace scoping
- Minimal data transfer with lean queries

## 🧪 **Testing**

- Unit tests for all service methods
- Integration tests for API endpoints
- Validation testing for DTOs
- Error handling testing
- Security testing

## 🚀 **Deployment**

### Prerequisites
- Supabase database with required tables
- Proper environment variables configured
- Authentication system set up
- Meta Business App configured

### Environment Variables
```bash
DATABASE_URL=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...
JWT_SECRET=your-jwt-secret
```

### Meta API Setup
1. Create Meta Business App
2. Configure WhatsApp Business API
3. Generate access tokens
4. Set up webhook endpoints

## 📚 **Documentation**

For detailed documentation, see:
- [Complete API Documentation](./docs/INDEX.md)
- [Architecture Overview](./docs/INDEX.md#architecture)
- [Security Guidelines](./docs/INDEX.md#security)
- [Deployment Guide](./docs/INDEX.md#deployment)

## 🔄 **Refactoring Summary**

This module has been completely refactored following the Auth module patterns:

- ✅ **Utility Classes**: Constants, validation, and response utilities
- ✅ **Enhanced DTOs**: Comprehensive validation with class-validator
- ✅ **Service Architecture**: Clean separation of concerns with proper error handling
- ✅ **Controller Structure**: Consistent endpoint patterns with proper decorators
- ✅ **Response Standardization**: Unified response format across all endpoints
- ✅ **Error Handling**: Comprehensive error management with specific exception types
- ✅ **Logging**: Structured logging for debugging and monitoring
- ✅ **Type Safety**: Strong TypeScript usage throughout

The module is now production-ready with comprehensive documentation and follows all established patterns from the Auth module.

## 🔗 **Integration with WhatsApp Business API**

### Required Meta Credentials
- **WhatsApp Business ID**: Unique identifier for your WhatsApp Business account
- **Phone Number ID**: Identifier for the phone number associated with your business
- **Access Token**: Long-lived access token for API authentication

### API Endpoints Used
- Message sending
- Template management
- Webhook verification
- Business profile management

### Error Handling
- Invalid credentials detection
- Token expiration handling
- Rate limit management
- Network error recovery
