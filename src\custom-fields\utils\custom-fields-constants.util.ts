/**
 * Constants for custom fields module
 */
export const CUSTOM_FIELDS_CONSTANTS = {
  // Default values
  DEFAULTS: {
    SHOW_ON_CONTACT: false,
    SHOW_ON_CHAT: false,
    OPTIONS: [],
  },

  // Error messages
  ERROR_MESSAGES: {
    CUSTOM_FIELD_CREATION_FAILED: 'Failed to create custom field',
    CUSTOM_FIELD_NOT_FOUND: 'Custom field not found',
    CUSTOM_FIELD_UPDATE_FAILED: 'Failed to update custom field',
    CUSTOM_FIELD_DELETE_FAILED: 'Failed to delete custom field',
    CUSTOM_FIELDS_FETCH_FAILED: 'Failed to fetch custom fields',
    USER_WORKSPACE_NOT_FOUND: 'User workspace not found',
    DUPLICATE_CUSTOM_FIELD_LABEL: 'A custom field with this label already exists in this workspace',
    INVALID_CUSTOM_FIELD_TYPE: 'Invalid custom field type',
    INVALID_OPTIONS_FOR_TYPE: 'Options can only be provided for dropdown type fields',
    MISSING_OPTIONS_FOR_DROPDOWN: 'Options are required for dropdown type fields',
    INVALID_OPTIONS_ARRAY: 'Options must be a non-empty array of strings',
  },

  // Success messages
  SUCCESS_MESSAGES: {
    CUSTOM_FIELD_CREATED: 'Custom field created successfully',
    CUSTOM_FIELD_UPDATED: 'Custom field updated successfully',
    CUSTOM_FIELD_DELETED: 'Custom field deleted successfully',
    CUSTOM_FIELDS_FETCHED: 'Custom fields fetched successfully',
    CUSTOM_FIELD_FETCHED: 'Custom field fetched successfully',
  },

  // HTTP status codes
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    NOT_FOUND: 404,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
  },

  // Custom field types
  FIELD_TYPES: {
    TEXT: 'text',
    NUMBER: 'number',
    DATE: 'date',
    DATETIME: 'datetime',
    DROPDOWN: 'dropdown',
    BOOL: 'bool',
  },

  // Validation rules
  VALIDATION: {
    LABEL_MIN_LENGTH: 1,
    LABEL_MAX_LENGTH: 100,
    OPTIONS_MIN_LENGTH: 1,
    OPTIONS_MAX_LENGTH: 50,
    MAX_OPTIONS_PER_FIELD: 20,
  },
} as const;

/**
 * Type for custom fields constants
 */
export type CustomFieldsConstants = typeof CUSTOM_FIELDS_CONSTANTS;
