# Queue Module Testing Documentation

## Overview

This document provides comprehensive testing guidelines, strategies, and examples for the Queue Module to ensure reliability, performance, and security.

## Testing Strategy

### Testing Pyramid

```
        ┌─────────────────┐
        │   E2E Tests     │  ← Few, High-level
        │   (Integration) │
        ├─────────────────┤
        │   Unit Tests    │  ← Many, Fast
        │   (Components)  │
        └─────────────────┘
```

### Test Types

1. **Unit Tests** - Test individual components in isolation
2. **Integration Tests** - Test component interactions
3. **E2E Tests** - Test complete user workflows
4. **Performance Tests** - Test system performance under load
5. **Security Tests** - Test security vulnerabilities

## Unit Testing

### Queue Service Tests

```typescript
// src/queue/__tests__/unit/queue.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { QueueService } from '../queue.service';
import { getQueueToken } from '@nestjs/bull';

describe('QueueService', () => {
  let service: QueueService;
  let mockCampaignQueue: any;
  let mockScheduledQueue: any;
  let mockRetryQueue: any;

  beforeEach(async () => {
    const mockQueue = {
      add: jest.fn(),
      addBulk: jest.fn(),
      pause: jest.fn(),
      resume: jest.fn(),
      empty: jest.fn(),
      getJobCounts: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QueueService,
        {
          provide: getQueueToken('campaign-messages'),
          useValue: mockQueue,
        },
        {
          provide: getQueueToken('scheduled-campaign-messages'),
          useValue: mockQueue,
        },
        {
          provide: getQueueToken('campaign-retry-messages'),
          useValue: mockQueue,
        },
      ],
    }).compile();

    service = module.get<QueueService>(QueueService);
    mockCampaignQueue = module.get(getQueueToken('campaign-messages'));
    mockScheduledQueue = module.get(getQueueToken('scheduled-campaign-messages'));
    mockRetryQueue = module.get(getQueueToken('campaign-retry-messages'));
  });

  describe('sendCampaignMessage', () => {
    it('should add message to campaign queue', async () => {
      const message = {
        campaignId: 'campaign-123',
        contactId: 'contact-456',
        phoneNumber: '+**********',
        countryCode: '+1',
        templateId: 'template-789',
        phoneNumberId: 'phone-id-123',
        variableMapping: { body: { '1': 'Hello' } },
        workspaceId: 1,
        userId: 'user-123',
        retryCount: 0,
        priority: 'NORMAL' as const,
      };

      mockCampaignQueue.add.mockResolvedValue({ id: 'job-123' });

      await service.sendCampaignMessage(message);

      expect(mockCampaignQueue.add).toHaveBeenCalledWith(
        'process-campaign-message',
        message,
        expect.objectContaining({
          priority: 2,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        })
      );
    });

    it('should handle queue errors', async () => {
      const message = { /* ... */ };
      mockCampaignQueue.add.mockRejectedValue(new Error('Queue error'));

      await expect(service.sendCampaignMessage(message)).rejects.toThrow('Queue error');
    });
  });

  describe('getQueueStats', () => {
    it('should return queue statistics', async () => {
      const mockStats = {
        waiting: 10,
        active: 2,
        completed: 100,
        failed: 5,
      };

      mockCampaignQueue.getJobCounts.mockResolvedValue(mockStats);
      mockScheduledQueue.getJobCounts.mockResolvedValue(mockStats);
      mockRetryQueue.getJobCounts.mockResolvedValue(mockStats);

      const result = await service.getQueueStats();

      expect(result).toEqual({
        campaignMessages: mockStats,
        scheduledMessages: mockStats,
        retryMessages: mockStats,
        total: {
          waiting: 30,
          active: 6,
          completed: 300,
          failed: 15,
        },
      });
    });
  });
});
```

### Queue Controller Tests

```typescript
// src/queue/__tests__/unit/queue.controller.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { QueueController } from '../queue.controller';
import { QueueService } from '../queue.service';

describe('QueueController', () => {
  let controller: QueueController;
  let queueService: QueueService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [QueueController],
      providers: [
        {
          provide: QueueService,
          useValue: {
            getQueueStats: jest.fn(),
            pauseQueue: jest.fn(),
            resumeQueue: jest.fn(),
            clearQueue: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<QueueController>(QueueController);
    queueService = module.get<QueueService>(QueueService);
  });

  describe('getQueueStats', () => {
    it('should return queue statistics', async () => {
      const mockStats = {
        campaignMessages: { waiting: 10, active: 2, completed: 100, failed: 5 },
        scheduledMessages: { waiting: 5, active: 1, completed: 50, failed: 2 },
        retryMessages: { waiting: 3, active: 0, completed: 20, failed: 1 },
      };

      jest.spyOn(queueService, 'getQueueStats').mockResolvedValue(mockStats);

      const result = await controller.getQueueStats();

      expect(result).toEqual(mockStats);
      expect(queueService.getQueueStats).toHaveBeenCalled();
    });
  });

  describe('pauseQueue', () => {
    it('should pause queue successfully', async () => {
      const queueName = 'campaign-messages';
      jest.spyOn(queueService, 'pauseQueue').mockResolvedValue(undefined);

      const result = await controller.pauseQueue(queueName);

      expect(result).toEqual({ message: `Queue ${queueName} paused successfully` });
      expect(queueService.pauseQueue).toHaveBeenCalledWith(queueName);
    });

    it('should handle invalid queue name', async () => {
      const invalidQueueName = 'invalid-queue';
      
      await expect(controller.pauseQueue(invalidQueueName)).rejects.toThrow();
    });
  });
});
```

## Integration Testing

### Queue Module Integration Tests

```typescript
// src/queue/__tests__/integration/queue.integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { QueueModule } from '../queue.module';
import { QueueService } from '../queue.service';
import { BullModule } from '@nestjs/bull';

describe('Queue Integration', () => {
  let module: TestingModule;
  let queueService: QueueService;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        BullModule.forRoot({
          redis: {
            host: 'localhost',
            port: 6379,
          },
        }),
        BullModule.registerQueue(
          { name: 'campaign-messages' },
          { name: 'scheduled-campaign-messages' },
          { name: 'campaign-retry-messages' },
        ),
      ],
      providers: [QueueService],
    }).compile();

    queueService = module.get<QueueService>(QueueService);
  });

  afterAll(async () => {
    await module.close();
  });

  it('should connect to Redis and create queues', async () => {
    expect(queueService).toBeDefined();
    
    // Test queue connection
    const stats = await queueService.getQueueStats();
    expect(stats).toBeDefined();
  });

  it('should process messages end-to-end', async () => {
    const message = {
      campaignId: 'test-campaign',
      contactId: 'test-contact',
      phoneNumber: '+**********',
      countryCode: '+1',
      templateId: 'test-template',
      phoneNumberId: 'test-phone-id',
      variableMapping: { body: { '1': 'Test Message' } },
      workspaceId: 1,
      userId: 'test-user',
      retryCount: 0,
      priority: 'NORMAL' as const,
    };

    // Add message to queue
    await queueService.sendCampaignMessage(message);

    // Wait for processing
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Verify message was processed
    const stats = await queueService.getQueueStats();
    expect(stats.campaignMessages.completed).toBeGreaterThan(0);
  });
});
```

## E2E Testing

### Queue E2E Tests

```typescript
// src/queue/__tests__/e2e/queue.e2e-spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../app.module';
import { QueueService } from '../queue.service';

describe('Queue E2E', () => {
  let app: INestApplication;
  let queueService: QueueService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    queueService = moduleFixture.get<QueueService>(QueueService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/queue/stats (GET)', () => {
    it('should return queue statistics', () => {
      return request(app.getHttpServer())
        .get('/queue/stats')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'success');
          expect(res.body).toHaveProperty('data');
          expect(res.body.data).toHaveProperty('campaignMessages');
          expect(res.body.data).toHaveProperty('scheduledMessages');
          expect(res.body.data).toHaveProperty('retryMessages');
        });
    });
  });

  describe('/queue/pause/:queueName (POST)', () => {
    it('should pause queue successfully', () => {
      return request(app.getHttpServer())
        .post('/queue/pause/campaign-messages')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body.message).toContain('paused successfully');
        });
    });

    it('should return 400 for invalid queue name', () => {
      return request(app.getHttpServer())
        .post('/queue/pause/invalid-queue')
        .expect(400);
    });
  });

  describe('/queue/resume/:queueName (POST)', () => {
    it('should resume queue successfully', () => {
      return request(app.getHttpServer())
        .post('/queue/resume/campaign-messages')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body.message).toContain('resumed successfully');
        });
    });
  });

  describe('/queue/clear/:queueName (POST)', () => {
    it('should clear queue successfully', () => {
      return request(app.getHttpServer())
        .post('/queue/clear/campaign-messages')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body.message).toContain('cleared successfully');
        });
    });
  });
});
```

## Performance Testing

### Load Testing

```typescript
// src/queue/__tests__/performance/queue.load.spec.ts
import { QueueService } from '../queue.service';

describe('Queue Performance', () => {
  let queueService: QueueService;

  beforeEach(async () => {
    // Setup queue service
  });

  it('should handle high message volume', async () => {
    const messageCount = 1000;
    const messages = Array.from({ length: messageCount }, (_, i) => ({
      campaignId: `campaign-${i}`,
      contactId: `contact-${i}`,
      phoneNumber: `+123456789${i}`,
      countryCode: '+1',
      templateId: 'test-template',
      phoneNumberId: 'test-phone-id',
      variableMapping: { body: { '1': `Message ${i}` } },
      workspaceId: 1,
      userId: 'test-user',
      retryCount: 0,
      priority: 'NORMAL' as const,
    }));

    const startTime = Date.now();

    // Send messages in parallel
    await Promise.all(
      messages.map(message => queueService.sendCampaignMessage(message))
    );

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`Sent ${messageCount} messages in ${duration}ms`);
    expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
  });

  it('should maintain performance under concurrent load', async () => {
    const concurrentUsers = 10;
    const messagesPerUser = 100;

    const promises = Array.from({ length: concurrentUsers }, (_, userIndex) => {
      return Promise.all(
        Array.from({ length: messagesPerUser }, (_, messageIndex) => {
          const message = {
            campaignId: `campaign-${userIndex}-${messageIndex}`,
            contactId: `contact-${userIndex}-${messageIndex}`,
            phoneNumber: `+123456789${messageIndex}`,
            countryCode: '+1',
            templateId: 'test-template',
            phoneNumberId: 'test-phone-id',
            variableMapping: { body: { '1': `Message ${messageIndex}` } },
            workspaceId: 1,
            userId: `user-${userIndex}`,
            retryCount: 0,
            priority: 'NORMAL' as const,
          };
          return queueService.sendCampaignMessage(message);
        })
      );
    });

    const startTime = Date.now();
    await Promise.all(promises);
    const endTime = Date.now();

    const totalMessages = concurrentUsers * messagesPerUser;
    const duration = endTime - startTime;
    const messagesPerSecond = totalMessages / (duration / 1000);

    console.log(`Processed ${totalMessages} messages in ${duration}ms (${messagesPerSecond.toFixed(2)} msg/s)`);
    expect(messagesPerSecond).toBeGreaterThan(100); // Should process at least 100 msg/s
  });
});
```

## Security Testing

### Security Test Suite

```typescript
// src/queue/__tests__/security/queue.security.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../app.module';

describe('Queue Security', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Authentication', () => {
    it('should require authentication for all endpoints', () => {
      return request(app.getHttpServer())
        .get('/queue/stats')
        .expect(401);
    });

    it('should reject invalid tokens', () => {
      return request(app.getHttpServer())
        .get('/queue/stats')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('Authorization', () => {
    it('should enforce role-based access control', () => {
      // Test with different user roles
      const userToken = 'valid-user-token';
      const adminToken = 'valid-admin-token';

      return request(app.getHttpServer())
        .post('/queue/clear/campaign-messages')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });
  });

  describe('Input Validation', () => {
    it('should validate queue names', () => {
      return request(app.getHttpServer())
        .post('/queue/pause/../../../etc/passwd')
        .set('Authorization', 'Bearer valid-token')
        .expect(400);
    });

    it('should prevent SQL injection', () => {
      return request(app.getHttpServer())
        .post('/queue/pause/; DROP TABLE users; --')
        .set('Authorization', 'Bearer valid-token')
        .expect(400);
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits', async () => {
      const token = 'valid-token';
      
      // Make multiple requests quickly
      const promises = Array.from({ length: 150 }, () => {
        return request(app.getHttpServer())
          .get('/queue/stats')
          .set('Authorization', `Bearer ${token}`);
      });

      const responses = await Promise.all(promises);
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });
});
```

## Test Fixtures

### Test Data Fixtures

```typescript
// src/queue/__tests__/fixtures/queue.fixtures.ts
export const mockCampaignMessage = {
  campaignId: 'campaign-123',
  contactId: 'contact-456',
  phoneNumber: '+**********',
  countryCode: '+1',
  templateId: 'template-789',
  phoneNumberId: 'phone-id-123',
  variableMapping: {
    body: { '1': 'Hello World!' },
    header: { '1': 'Welcome' },
  },
  workspaceId: 1,
  userId: 'user-123',
  retryCount: 0,
  priority: 'NORMAL' as const,
};

export const mockScheduledMessage = {
  ...mockCampaignMessage,
  scheduledAt: new Date(Date.now() + 60000), // 1 minute from now
};

export const mockRetryMessage = {
  ...mockCampaignMessage,
  retryCount: 1,
  originalError: 'Network timeout',
};

export const mockQueueStats = {
  campaignMessages: {
    waiting: 10,
    active: 2,
    completed: 100,
    failed: 5,
  },
  scheduledMessages: {
    waiting: 5,
    active: 1,
    completed: 50,
    failed: 2,
  },
  retryMessages: {
    waiting: 3,
    active: 0,
    completed: 20,
    failed: 1,
  },
};

export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  role: 'admin',
  permissions: ['queue:read', 'queue:write', 'queue:admin'],
  workspaceId: 1,
};
```

## Test Configuration

### Jest Configuration

```json
// jest.config.js
module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: 'src',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: [
    '**/*.(t|j)s',
    '!**/*.spec.ts',
    '!**/*.e2e-spec.ts',
  ],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  moduleNameMapping: {
    '^src/(.*)$': '<rootDir>/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/../test/setup.ts'],
  testTimeout: 30000,
};
```

### Test Setup

```typescript
// test/setup.ts
import { Test } from '@nestjs/testing';
import { AppModule } from '../src/app.module';

beforeAll(async () => {
  // Global test setup
  process.env.NODE_ENV = 'test';
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = '6379';
});

afterAll(async () => {
  // Global test cleanup
});
```

## Running Tests

### Test Commands

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e

# Run performance tests
npm run test:performance

# Run security tests
npm run test:security

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- queue.service.spec.ts

# Run tests matching pattern
npm test -- --testNamePattern="Queue Service"
```

### Test Scripts

```json
// package.json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest --testPathPattern=unit",
    "test:integration": "jest --testPathPattern=integration",
    "test:e2e": "jest --testPathPattern=e2e",
    "test:performance": "jest --testPathPattern=performance",
    "test:security": "jest --testPathPattern=security",
    "test:coverage": "jest --coverage",
    "test:watch": "jest --watch",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

## Test Best Practices

### 1. Test Organization

- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Keep tests focused and independent

### 2. Mocking

- Mock external dependencies
- Use realistic test data
- Avoid over-mocking
- Test error scenarios

### 3. Assertions

- Use specific assertions
- Test both success and failure cases
- Verify side effects
- Check error messages

### 4. Performance

- Keep tests fast
- Use parallel execution
- Clean up resources
- Monitor test duration

### 5. Maintenance

- Update tests with code changes
- Remove obsolete tests
- Refactor test code
- Document test scenarios

## Continuous Integration

### GitHub Actions

```yaml
# .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run unit tests
      run: npm run test:unit
    
    - name: Run integration tests
      run: npm run test:integration
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Generate coverage report
      run: npm run test:coverage
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

## Test Monitoring

### Test Metrics

- Test coverage percentage
- Test execution time
- Test failure rate
- Flaky test detection

### Test Reporting

```bash
# Generate HTML coverage report
npm run test:coverage -- --coverageReporters=html

# Generate JUnit report
npm run test -- --reporters=default --reporters=jest-junit

# Generate JSON report
npm run test -- --json --outputFile=test-results.json
```

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**
   - Ensure Redis is running
   - Check connection configuration
   - Verify network connectivity

2. **Test Timeouts**
   - Increase timeout values
   - Check for infinite loops
   - Verify async operations

3. **Mock Issues**
   - Ensure mocks are properly configured
   - Check mock return values
   - Verify mock call expectations

4. **Environment Issues**
   - Set correct environment variables
   - Use test-specific configuration
   - Clean up test data

### Debug Tips

```bash
# Run tests with debug output
DEBUG=* npm test

# Run specific test with verbose output
npm test -- --verbose queue.service.spec.ts

# Run tests with coverage and debug
npm run test:coverage -- --verbose
```
