import { createClient } from '@supabase/supabase-js';
import { Process, Processor, OnQueueActive, OnQueueCompleted, OnQueueFailed } from '@nestjs/bull';
import { Job } from 'bull';
import { Injectable, Logger } from '@nestjs/common';
import { CampaignMessage } from './queue.service';
import { MetaApiService } from '../meta-api/meta-api.service';
import { TemplateService } from '../template/template.service';
import { MessageStatusService } from './message-status.service';
import { MetaOnboardingService } from '../meta-onboarding/meta-onboarding.service';

@Processor('campaign-messages')
@Injectable()
export class CampaignMessageProcessor {
  private readonly logger = new Logger(CampaignMessageProcessor.name);

  constructor(
    private metaApiService: MetaApiService,
    private templateService: TemplateService,
    private messageStatusService: MessageStatusService,
    private metaOnboardingService: MetaOnboardingService,
  ) {}

  @Process('process-campaign-message')
  async processCampaignMessage(job: Job<CampaignMessage>) {
    const message = job.data;
    this.logger.debug(`Processing campaign message for campaign ${message.campaignId}`);

    try {
      // Get template details
      const template = await this.templateService.getTemplateById(message.templateId, {
        user: { id: message.userId }
      });
this.logger.debug(`Template: ${template}`);
      if (!template || template.status !== 'success') {
        throw new Error(`Template not found or not approved: ${message.templateId}`);
      }

      // Get Meta credentials
     const credential=await this.metaOnboardingService.getMetaCredentialsByPhoneId(message.phoneNumberId, { user: { id: message.userId } }); // Use first credential

      // Send message via Meta API
      const result = await this.metaApiService.sendTemplateMessage(
        message.phoneNumberId,
        template.data.template.name,
        message.phoneNumber,
        message.countryCode,
        message.variableMapping,
        template.data.template.language,
        credential.data.credentials.access_token,
        template.data.template
      );

      // Update message status
      await this.messageStatusService.updateMessageStatus(
        message.campaignId,
        message.contactId,
        'SENT',
        result.messageId
      );

      this.logger.debug(`Campaign message sent successfully for campaign ${message.campaignId}`);
      return { success: true, messageId: result.messageId };

    } catch (error) {
      this.logger.error(`Failed to process campaign message: ${error.message}`, error.stack);
      
      // Update message status to failed
      await this.messageStatusService.updateMessageStatus(
        message.campaignId,
        message.contactId,
        'FAILED',
        undefined,
        error.message
      );

      throw error;
    }
  }

  @OnQueueActive()
  onActive(job: Job<CampaignMessage>) {
    this.logger.debug(`Processing campaign message job ${job.id} for campaign ${job.data.campaignId}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job<CampaignMessage>, result: any) {
    this.logger.debug(`Campaign message job ${job.id} completed for campaign ${job.data.campaignId}`);
  }

  @OnQueueFailed()
  onFailed(job: Job<CampaignMessage>, err: Error) {
    this.logger.error(`Campaign message job ${job.id} failed for campaign ${job.data.campaignId}: ${err.message}`);
  }

  private buildMessageComponents(variableMapping: Record<string, any>): any[] {
    const components: any[] = [];

    if (variableMapping.body) {
      components.push({
        type: 'body',
        parameters: Object.entries(variableMapping.body).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    if (variableMapping.header) {
      components.push({
        type: 'header',
        parameters: Object.entries(variableMapping.header).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    if (variableMapping.footer) {
      components.push({
        type: 'footer',
        parameters: Object.entries(variableMapping.footer).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    return components;
  }
}

@Processor('scheduled-campaign-messages')
@Injectable()
export class ScheduledCampaignMessageProcessor {
  private readonly logger = new Logger(ScheduledCampaignMessageProcessor.name);

  constructor(
    private metaApiService: MetaApiService,
    private templateService: TemplateService,
    private messageStatusService: MessageStatusService,
    private metaOnboardingService: MetaOnboardingService,
  ) {}

  @Process('process-scheduled-campaign-message')
  async processScheduledCampaignMessage(job: Job<CampaignMessage>) {
    const message = job.data;
    this.logger.debug(`Processing scheduled campaign message for campaign ${message.campaignId}`);

    try {
      // Check if it's time to send the message
      if (message.scheduledAt && new Date() < new Date(message.scheduledAt)) {
        // Reschedule the job
        const delay = new Date(message.scheduledAt).getTime() - Date.now();
        throw new Error(`Message scheduled for future, rescheduling in ${delay}ms`);
      }

      // Process the message (same logic as regular campaign message)
      const template = await this.templateService.getTemplateById(message.templateId, {
        user: { id: message.userId }
      });

      if (!template || template.status !== 'success') {
        throw new Error(`Template not found or not approved: ${message.templateId}`);
      }

      const credentials = await this.metaOnboardingService.getMetaCredentialsByWorkspace(message.workspaceId.toString(), { user: { id: message.userId } }, {});
      if (!credentials || credentials.status !== 'success' || !credentials.data || credentials.data.length === 0) {
        throw new Error('Meta credentials not found');
      }

      const credential = credentials.data[0]; // Use first credential

      const result = await this.metaApiService.sendTemplateMessage(
        message.phoneNumberId,
        template.data.template.name,
        message.phoneNumber,
        message.countryCode,
        message.variableMapping,
        template.data.template.language,
        credential.access_token
      );

      await this.messageStatusService.updateMessageStatus(
        message.campaignId,
        message.contactId,
        'SENT',
        result.messageId
      );

      this.logger.debug(`Scheduled campaign message sent successfully for campaign ${message.campaignId}`);
      return { success: true, messageId: result.messageId };

    } catch (error) {
      this.logger.error(`Failed to process scheduled campaign message: ${error.message}`, error.stack);
      
      await this.messageStatusService.updateMessageStatus(
        message.campaignId,
        message.contactId,
        'FAILED',
        undefined,
        error.message
      );

      throw error;
    }
  }

  @OnQueueActive()
  onActive(job: Job<CampaignMessage>) {
    this.logger.debug(`Processing scheduled campaign message job ${job.id} for campaign ${job.data.campaignId}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job<CampaignMessage>, result: any) {
    this.logger.debug(`Scheduled campaign message job ${job.id} completed for campaign ${job.data.campaignId}`);
  }

  @OnQueueFailed()
  onFailed(job: Job<CampaignMessage>, err: Error) {
    this.logger.error(`Scheduled campaign message job ${job.id} failed for campaign ${job.data.campaignId}: ${err.message}`);
  }

  private buildMessageComponents(variableMapping: Record<string, any>): any[] {
    const components: any[] = [];

    if (variableMapping.body) {
      components.push({
        type: 'body',
        parameters: Object.entries(variableMapping.body).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    if (variableMapping.header) {
      components.push({
        type: 'header',
        parameters: Object.entries(variableMapping.header).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    if (variableMapping.footer) {
      components.push({
        type: 'footer',
        parameters: Object.entries(variableMapping.footer).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    return components;
  }
}

@Processor('campaign-retry-messages')
@Injectable()
export class RetryCampaignMessageProcessor {
  private readonly logger = new Logger(RetryCampaignMessageProcessor.name);

  constructor(
    private metaApiService: MetaApiService,
    private templateService: TemplateService,
    private messageStatusService: MessageStatusService,
    private metaOnboardingService: MetaOnboardingService,
  ) {}

  @Process('process-retry-campaign-message')
  async processRetryCampaignMessage(job: Job<CampaignMessage>) {
    const message = job.data;
    this.logger.debug(`Processing retry campaign message for campaign ${message.campaignId} (attempt ${job.attemptsMade + 1})`);

    try {
      // Get template details
      const template = await this.templateService.getTemplateById(message.templateId, {
        user: { id: message.userId }
      });

      if (!template || template.status !== 'success') {
        throw new Error(`Template not found or not approved: ${message.templateId}`);
      }

      // Get Meta credentials
      const credentials = await this.metaOnboardingService.getMetaCredentialsByWorkspace(message.workspaceId.toString(), { user: { id: message.userId } }, {});
      if (!credentials || credentials.status !== 'success' || !credentials.data || credentials.data.length === 0) {
        throw new Error('Meta credentials not found');
      }

      const credential = credentials.data[0]; // Use first credential

      // Send message via Meta API
      const result = await this.metaApiService.sendTemplateMessage(
        message.phoneNumberId,
        template.data.template.name,
        message.phoneNumber,
        message.countryCode,
        message.variableMapping,
        template.data.template.language,
        credential.access_token
      );

      // Update message status
      await this.messageStatusService.updateMessageStatus(
        message.campaignId,
        message.contactId,
        'SENT',
        result.messageId
      );

      this.logger.debug(`Retry campaign message sent successfully for campaign ${message.campaignId}`);
      return { success: true, messageId: result.messageId, retryAttempt: job.attemptsMade + 1 };

    } catch (error) {
      this.logger.error(`Failed to process retry campaign message: ${error.message}`, error.stack);
      
      // Update message status to failed
      await this.messageStatusService.updateMessageStatus(
        message.campaignId,
        message.contactId,
        'FAILED',
        undefined,
        error.message
      );

      throw error;
    }
  }

  @OnQueueActive()
  onActive(job: Job<CampaignMessage>) {
    this.logger.debug(`Processing retry campaign message job ${job.id} for campaign ${job.data.campaignId}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job<CampaignMessage>, result: any) {
    this.logger.debug(`Retry campaign message job ${job.id} completed for campaign ${job.data.campaignId}`);
  }

  @OnQueueFailed()
  onFailed(job: Job<CampaignMessage>, err: Error) {
    this.logger.error(`Retry campaign message job ${job.id} failed for campaign ${job.data.campaignId}: ${err.message}`);
  }

  private buildMessageComponents(variableMapping: Record<string, any>): any[] {
    const components: any[] = [];

    if (variableMapping.body) {
      components.push({
        type: 'body',
        parameters: Object.entries(variableMapping.body).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    if (variableMapping.header) {
      components.push({
        type: 'header',
        parameters: Object.entries(variableMapping.header).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    if (variableMapping.footer) {
      components.push({
        type: 'footer',
        parameters: Object.entries(variableMapping.footer).map(([key, value]) => ({
          type: 'text',
          text: value,
        })),
      });
    }

    return components;
  }
}
