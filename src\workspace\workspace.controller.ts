import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  HttpCode, 
  HttpStatus, 
  UseGuards, 
  Request, 
  Query 
} from '@nestjs/common';
import { WorkspaceService } from './workspace.service';
import { CreateWorkspaceDto, CreateMemberDto, WorkspaceMembersQueryDto } from './dto';
import { AuthGuard } from '../auth/auth.guard';

/**
 * Refactored WorkspaceController with improved structure and consistent response handling
 */
@Controller('workspace')
@UseGuards(AuthGuard)
export class WorkspaceController {
  constructor(private readonly workspaceService: WorkspaceService) {}

  /**
   * Create workspace endpoint
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Body() createWorkspaceDto: CreateWorkspaceDto, @Request() req: any) {
    return await this.workspaceService.create(createWorkspaceDto, req);
  }

  /**
   * Add member to workspace endpoint
   */
  @Post('add-member')
  @HttpCode(HttpStatus.CREATED)
  async addMember(@Body() memberData: CreateMemberDto, @Request() req: any) {
    return await this.workspaceService.addMemberToWorkspace(memberData, req);
  }

  /**
   * Get workspace members endpoint
   */
  
  @Get('members')
  @HttpCode(HttpStatus.OK)
  async getWorkspaceMembers(@Request() req: any, @Query() query: WorkspaceMembersQueryDto) {
    return await this.workspaceService.getWorkspaceMembers(req);
  }
}