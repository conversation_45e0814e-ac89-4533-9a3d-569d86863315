import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type TagDocument = HydratedDocument<Tag>;

@Schema({ timestamps: true })
export class Tag {
  @Prop({ required: true })
  name: string;

  @Prop()
  text_color?: string;

  @Prop()
  background_color?: string;

  @Prop({ type: String, required: true })
  createdBy: string; // supabase user id (uuid)

  @Prop({ type: Number, required: true })
  workspaceId: number; // supabase workspace id (bigint)
}

export const TagSchema = SchemaFactory.createForClass(Tag);

// Ensure unique tag names per workspace
TagSchema.index({ workspaceId: 1, name: 1 }, { unique: true });


