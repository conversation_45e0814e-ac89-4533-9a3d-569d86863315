/**
 * Template Validation Utility
 * Reusable validation logic for template operations
 */

import { BadRequestException, UnauthorizedException, NotFoundException } from '@nestjs/common';
import { TEMPLATE_CONSTANTS } from './template-constants.util';

export class TemplateValidationUtil {
  /**
   * Validates user context from request
   */
  static validateUserContext(req: any): any {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.USER_NOT_FOUND);
    }
    return user;
  }

  /**
   * Validates user profile and returns workspace ID
   */
  static validateUserProfile(userProfile: any, userProfileError: any): string {
    if (userProfileError || !userProfile) {
      throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
    }
    return userProfile.workspace_id;
  }

  /**
   * Validates workspace access
   */
  static validateWorkspaceAccess(workspaceMember: any): void {
    if (workspaceMember.error || !workspaceMember.data) {
      throw new UnauthorizedException('You do not have access to this workspace');
    }
  }

  // Legacy validateTemplateCreationData method removed - now using DTO validation

  /**
   * Validates template update data
   */
  static validateTemplateUpdateData(updateDto: any): any {
    const updateData: any = {};

    if (updateDto.name !== undefined) {
      if (!updateDto.name || updateDto.name.trim().length === 0) {
        throw new BadRequestException('Template name cannot be empty');
      }
      if (updateDto.name.length > TEMPLATE_CONSTANTS.VALIDATION.NAME_MAX_LENGTH) {
        throw new BadRequestException(`Template name must be less than ${TEMPLATE_CONSTANTS.VALIDATION.NAME_MAX_LENGTH} characters`);
      }
      updateData.name = updateDto.name.trim();
    }

    if (updateDto.description !== undefined) {
      if (updateDto.description && updateDto.description.length > TEMPLATE_CONSTANTS.VALIDATION.DESCRIPTION_MAX_LENGTH) {
        throw new BadRequestException(`Template description must be less than ${TEMPLATE_CONSTANTS.VALIDATION.DESCRIPTION_MAX_LENGTH} characters`);
      }
      updateData.description = updateDto.description?.trim() || '';
    }

    if (updateDto.language !== undefined) {
      if (!updateDto.language || updateDto.language.trim().length === 0) {
        throw new BadRequestException('Template language cannot be empty');
      }
      updateData.language = updateDto.language.trim();
    }

    if (updateDto.components !== undefined) {
      if (!Array.isArray(updateDto.components) || updateDto.components.length === 0) {
        throw new BadRequestException('Template components are required');
      }
      updateData.components = updateDto.components;
    }

    if (updateDto.category !== undefined) {
      if (!Object.values(TEMPLATE_CONSTANTS.TEMPLATE_CATEGORIES).includes(updateDto.category)) {
        throw new BadRequestException('Invalid template category');
      }
      updateData.meta_template_category = updateDto.category;
    }

    if (updateDto.waba_id !== undefined) {
      if (!updateDto.waba_id || updateDto.waba_id.trim().length === 0) {
        throw new BadRequestException('WABA ID cannot be empty');
      }
      updateData.waba_id = updateDto.waba_id.trim();
    }

    return updateData;
  }

  /**
   * Validates template existence
   */
  static validateTemplateExists(template: any, templateError: any): void {
    if (templateError || !template) {
      if (templateError?.code === 'PGRST116') {
        throw new NotFoundException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_NOT_FOUND);
      }
      throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_FETCH_FAILED);
    }
  }

  /**
   * Validates template approval status
   */
  static validateTemplateApproval(template: any): void {
    if (!template.meta_template_id) {
      throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_NOT_APPROVED);
    }

    if (template.meta_template_status !== TEMPLATE_CONSTANTS.TEMPLATE_STATUSES.APPROVED) {
      throw new BadRequestException(
        `Template status is ${template.meta_template_status}. ${TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_STATUS_INVALID}`
      );
    }
  }

  /**
   * Validates pagination parameters
   */
  static validatePaginationParams(page?: number, limit?: number): { page: number; limit: number; skip: number } {
    const validatedPage = page && !isNaN(Number(page)) && Number(page) > 0 ? Number(page) : TEMPLATE_CONSTANTS.PAGINATION.DEFAULT_PAGE;
    const validatedLimit = limit && !isNaN(Number(limit)) && Number(limit) > 0 ? Number(limit) : TEMPLATE_CONSTANTS.PAGINATION.DEFAULT_LIMIT;
    
    if (validatedLimit > TEMPLATE_CONSTANTS.PAGINATION.MAX_LIMIT) {
      throw new BadRequestException(`Limit cannot exceed ${TEMPLATE_CONSTANTS.PAGINATION.MAX_LIMIT}`);
    }

    return {
      page: validatedPage,
      limit: validatedLimit,
      skip: (validatedPage - 1) * validatedLimit,
    };
  }

  /**
   * Validates workspace ID
   */
  static validateWorkspaceId(workspaceId: string): number {
    const parsedId = parseInt(workspaceId);
    if (isNaN(parsedId) || parsedId <= 0) {
      throw new BadRequestException('Invalid workspace ID');
    }
    return parsedId;
  }

  /**
   * Validates WABA ID
   */
  static validateWabaId(wabaId: string): string {
    if (!wabaId || wabaId.trim().length === 0) {
      throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.WABA_ID_REQUIRED);
    }
    if (wabaId.length > TEMPLATE_CONSTANTS.VALIDATION.WABA_ID_MAX_LENGTH) {
      throw new BadRequestException(`WABA ID must be less than ${TEMPLATE_CONSTANTS.VALIDATION.WABA_ID_MAX_LENGTH} characters`);
    }
    return wabaId.trim();
  }

  /**
   * Validates audio file
   */
  static validateAudioFile(file: any): void {
    if (!file) {
      throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.AUDIO_FILE_REQUIRED);
    }

    if (!file.mimetype || !file.mimetype.startsWith('audio/')) {
      throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.INVALID_AUDIO_FILE);
    }

    if (file.size > TEMPLATE_CONSTANTS.FILE_LIMITS.MAX_AUDIO_SIZE) {
      throw new BadRequestException(`Audio file size must be less than ${TEMPLATE_CONSTANTS.FILE_LIMITS.MAX_AUDIO_SIZE / (1024 * 1024)}MB`);
    }
  }

  /**
   * Validates campaign creation data
   */
  static validateCampaignCreationData(campaignDto: any): void {
    if (!campaignDto.name || campaignDto.name.trim().length === 0) {
      throw new BadRequestException('Campaign name is required');
    }

    if (!campaignDto.template_id || campaignDto.template_id.trim().length === 0) {
      throw new BadRequestException('Template ID is required');
    }

    if (!campaignDto.contact_selection_type || !Object.values(TEMPLATE_CONSTANTS.CONTACT_SELECTION_TYPES).includes(campaignDto.contact_selection_type)) {
      throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.INVALID_CONTACT_SELECTION);
    }

    // Validate contact selection specific data
    if (campaignDto.contact_selection_type === TEMPLATE_CONSTANTS.CONTACT_SELECTION_TYPES.CSV_CONTACTS) {
      if (!campaignDto.csv_contacts_string || campaignDto.csv_contacts_string.trim().length === 0) {
        throw new BadRequestException('CSV contacts data is required for CSV contact selection');
      }
    }

    if (campaignDto.contact_selection_type === TEMPLATE_CONSTANTS.CONTACT_SELECTION_TYPES.QUICK_CONTACTS) {
      if (!campaignDto.quick_contacts || !Array.isArray(campaignDto.quick_contacts) || campaignDto.quick_contacts.length === 0) {
        throw new BadRequestException('Quick contacts array is required for quick contact selection');
      }
    }
  }

  /**
   * Validates campaign action
   */
  static validateCampaignAction(action: string): void {
    if (!Object.values(TEMPLATE_CONSTANTS.CAMPAIGN_ACTIONS).includes(action as any)) {
      throw new BadRequestException('Invalid campaign action');
    }
  }

  /**
   * Validates test message data
   */
  static validateTestMessageData(testMessageDto: any): void {
    if (!testMessageDto.template_id || testMessageDto.template_id.trim().length === 0) {
      throw new BadRequestException('Template ID is required');
    }

    if (!testMessageDto.test_contacts || !Array.isArray(testMessageDto.test_contacts) || testMessageDto.test_contacts.length === 0) {
      throw new BadRequestException('Test contacts array is required');
    }

    if (!testMessageDto.phone_number_id || testMessageDto.phone_number_id.trim().length === 0) {
      throw new BadRequestException('Phone number ID is required');
    }

    if (!testMessageDto.country_code || testMessageDto.country_code.trim().length === 0) {
      throw new BadRequestException('Country code is required');
    }
  }
}
