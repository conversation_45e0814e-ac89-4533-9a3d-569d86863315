import { Test, TestingModule } from '@nestjs/testing';
import { QueueService } from '../../queue.service';
import { getQueueToken } from '@nestjs/bull';

describe('QueueService', () => {
  let service: QueueService;
  let mockCampaignQueue: any;
  let mockScheduledQueue: any;
  let mockRetryQueue: any;

  beforeEach(async () => {
    const mockQueue = {
      add: jest.fn(),
      addBulk: jest.fn(),
      pause: jest.fn(),
      resume: jest.fn(),
      empty: jest.fn(),
      getJobCounts: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QueueService,
        {
          provide: getQueueToken('campaign-messages'),
          useValue: mockQueue,
        },
        {
          provide: getQueueToken('scheduled-campaign-messages'),
          useValue: mockQueue,
        },
        {
          provide: getQueueToken('campaign-retry-messages'),
          useValue: mockQueue,
        },
      ],
    }).compile();

    service = module.get<QueueService>(QueueService);
    mockCampaignQueue = module.get(getQueueToken('campaign-messages'));
    mockScheduledQueue = module.get(getQueueToken('scheduled-campaign-messages'));
    mockRetryQueue = module.get(getQueueToken('campaign-retry-messages'));
  });

  describe('sendCampaignMessage', () => {
    it('should add message to campaign queue', async () => {
      const message = {
        campaignId: 'campaign-123',
        contactId: 'contact-456',
        phoneNumber: '+**********',
        countryCode: '+1',
        templateId: 'template-789',
        phoneNumberId: 'phone-id-123',
        variableMapping: { body: { '1': 'Hello' } },
        workspaceId: 1,
        userId: 'user-123',
        retryCount: 0,
        priority: 'NORMAL' as const,
      };

      mockCampaignQueue.add.mockResolvedValue({ id: 'job-123' });

      await service.sendCampaignMessage(message);

      expect(mockCampaignQueue.add).toHaveBeenCalledWith(
        'process-campaign-message',
        message,
        expect.objectContaining({
          priority: 2,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        })
      );
    });

    it('should handle queue errors', async () => {
      const message = {
        campaignId: 'campaign-123',
        contactId: 'contact-456',
        phoneNumber: '+**********',
        countryCode: '+1',
        templateId: 'template-789',
        phoneNumberId: 'phone-id-123',
        variableMapping: { body: { '1': 'Hello' } },
        workspaceId: 1,
        userId: 'user-123',
        retryCount: 0,
        priority: 'NORMAL' as const,
      };

      mockCampaignQueue.add.mockRejectedValue(new Error('Queue error'));

      await expect(service.sendCampaignMessage(message)).rejects.toThrow('Queue error');
    });
  });

  describe('sendCampaignBatch', () => {
    it('should add batch messages to campaign queue', async () => {
      const batchMessage = {
        campaignId: 'campaign-123',
        messages: [
          {
            campaignId: 'campaign-123',
            contactId: 'contact-456',
            phoneNumber: '+**********',
            countryCode: '+1',
            templateId: 'template-789',
            phoneNumberId: 'phone-id-123',
            variableMapping: { body: { '1': 'Hello' } },
            workspaceId: 1,
            userId: 'user-123',
            retryCount: 0,
            priority: 'NORMAL' as const,
          },
        ],
        workspaceId: 1,
        userId: 'user-123',
      };

      mockCampaignQueue.addBulk.mockResolvedValue([{ id: 'job-123' }]);

      await service.sendCampaignBatch(batchMessage);

      expect(mockCampaignQueue.addBulk).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            name: 'process-campaign-message',
            data: batchMessage.messages[0],
            opts: expect.objectContaining({
              priority: 2,
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
            }),
          }),
        ])
      );
    });
  });

  describe('sendScheduledCampaignMessage', () => {
    it('should add scheduled message to scheduled queue', async () => {
      const message = {
        campaignId: 'campaign-123',
        contactId: 'contact-456',
        phoneNumber: '+**********',
        countryCode: '+1',
        templateId: 'template-789',
        phoneNumberId: 'phone-id-123',
        variableMapping: { body: { '1': 'Hello' } },
        workspaceId: 1,
        userId: 'user-123',
        retryCount: 0,
        priority: 'NORMAL' as const,
        scheduledAt: new Date(Date.now() + 60000),
      };

      mockScheduledQueue.add.mockResolvedValue({ id: 'job-123' });

      await service.sendScheduledCampaignMessage(message);

      expect(mockScheduledQueue.add).toHaveBeenCalledWith(
        'process-scheduled-campaign-message',
        message,
        expect.objectContaining({
          delay: expect.any(Number),
          priority: 2,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        })
      );
    });

    it('should send immediately if scheduled time is in the past', async () => {
      const message = {
        campaignId: 'campaign-123',
        contactId: 'contact-456',
        phoneNumber: '+**********',
        countryCode: '+1',
        templateId: 'template-789',
        phoneNumberId: 'phone-id-123',
        variableMapping: { body: { '1': 'Hello' } },
        workspaceId: 1,
        userId: 'user-123',
        retryCount: 0,
        priority: 'NORMAL' as const,
        scheduledAt: new Date(Date.now() - 60000), // 1 minute ago
      };

      mockCampaignQueue.add.mockResolvedValue({ id: 'job-123' });

      await service.sendScheduledCampaignMessage(message);

      expect(mockCampaignQueue.add).toHaveBeenCalledWith(
        'process-campaign-message',
        message,
        expect.objectContaining({
          priority: 2,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        })
      );
    });
  });

  describe('sendRetryMessage', () => {
    it('should add retry message to retry queue', async () => {
      const message = {
        campaignId: 'campaign-123',
        contactId: 'contact-456',
        phoneNumber: '+**********',
        countryCode: '+1',
        templateId: 'template-789',
        phoneNumberId: 'phone-id-123',
        variableMapping: { body: { '1': 'Hello' } },
        workspaceId: 1,
        userId: 'user-123',
        retryCount: 1,
        priority: 'NORMAL' as const,
      };

      mockRetryQueue.add.mockResolvedValue({ id: 'job-123' });

      await service.sendRetryMessage(message);

      expect(mockRetryQueue.add).toHaveBeenCalledWith(
        'process-retry-campaign-message',
        message,
        expect.objectContaining({
          priority: 2,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        })
      );
    });
  });

  describe('getQueueStats', () => {
    it('should return queue statistics', async () => {
      const mockStats = {
        waiting: 10,
        active: 2,
        completed: 100,
        failed: 5,
      };

      mockCampaignQueue.getJobCounts.mockResolvedValue(mockStats);
      mockScheduledQueue.getJobCounts.mockResolvedValue(mockStats);
      mockRetryQueue.getJobCounts.mockResolvedValue(mockStats);

      const result = await service.getQueueStats();

      expect(result).toEqual({
        campaignMessages: mockStats,
        scheduledMessages: mockStats,
        retryMessages: mockStats,
        total: {
          waiting: 30,
          active: 6,
          completed: 300,
          failed: 15,
        },
      });
    });
  });

  describe('pauseQueue', () => {
    it('should pause campaign messages queue', async () => {
      mockCampaignQueue.pause.mockResolvedValue(undefined);

      await service.pauseQueue('campaign-messages');

      expect(mockCampaignQueue.pause).toHaveBeenCalled();
    });

    it('should pause scheduled messages queue', async () => {
      mockScheduledQueue.pause.mockResolvedValue(undefined);

      await service.pauseQueue('scheduled-campaign-messages');

      expect(mockScheduledQueue.pause).toHaveBeenCalled();
    });

    it('should pause retry messages queue', async () => {
      mockRetryQueue.pause.mockResolvedValue(undefined);

      await service.pauseQueue('campaign-retry-messages');

      expect(mockRetryQueue.pause).toHaveBeenCalled();
    });

    it('should throw error for invalid queue name', async () => {
      await expect(service.pauseQueue('invalid-queue')).rejects.toThrow(
        'Queue with name invalid-queue not found.'
      );
    });
  });

  describe('resumeQueue', () => {
    it('should resume campaign messages queue', async () => {
      mockCampaignQueue.resume.mockResolvedValue(undefined);

      await service.resumeQueue('campaign-messages');

      expect(mockCampaignQueue.resume).toHaveBeenCalled();
    });

    it('should resume scheduled messages queue', async () => {
      mockScheduledQueue.resume.mockResolvedValue(undefined);

      await service.resumeQueue('scheduled-campaign-messages');

      expect(mockScheduledQueue.resume).toHaveBeenCalled();
    });

    it('should resume retry messages queue', async () => {
      mockRetryQueue.resume.mockResolvedValue(undefined);

      await service.resumeQueue('campaign-retry-messages');

      expect(mockRetryQueue.resume).toHaveBeenCalled();
    });

    it('should throw error for invalid queue name', async () => {
      await expect(service.resumeQueue('invalid-queue')).rejects.toThrow(
        'Queue with name invalid-queue not found.'
      );
    });
  });

  describe('clearQueue', () => {
    it('should clear campaign messages queue', async () => {
      mockCampaignQueue.empty.mockResolvedValue(undefined);

      await service.clearQueue('campaign-messages');

      expect(mockCampaignQueue.empty).toHaveBeenCalled();
    });

    it('should clear scheduled messages queue', async () => {
      mockScheduledQueue.empty.mockResolvedValue(undefined);

      await service.clearQueue('scheduled-campaign-messages');

      expect(mockScheduledQueue.empty).toHaveBeenCalled();
    });

    it('should clear retry messages queue', async () => {
      mockRetryQueue.empty.mockResolvedValue(undefined);

      await service.clearQueue('campaign-retry-messages');

      expect(mockRetryQueue.empty).toHaveBeenCalled();
    });

    it('should throw error for invalid queue name', async () => {
      await expect(service.clearQueue('invalid-queue')).rejects.toThrow(
        'Queue with name invalid-queue not found.'
      );
    });
  });

  describe('getBullPriority', () => {
    it('should return correct priority for HIGH', () => {
      const result = (service as any).getBullPriority('HIGH');
      expect(result).toBe(1);
    });

    it('should return correct priority for NORMAL', () => {
      const result = (service as any).getBullPriority('NORMAL');
      expect(result).toBe(2);
    });

    it('should return correct priority for LOW', () => {
      const result = (service as any).getBullPriority('LOW');
      expect(result).toBe(3);
    });

    it('should return default priority for unknown', () => {
      const result = (service as any).getBullPriority('UNKNOWN');
      expect(result).toBe(2);
    });
  });
});
