import { IsS<PERSON>, IsNot<PERSON>mpty, IsOptional, IsEmail, IsNumberString, IsBoolean, IsIn, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Matches } from 'class-validator';

/**
 * DTO for creating a workspace
 */

export class CreateWorkspaceDto {
  @IsString({ message: 'Workspace name must be a string' })
  @IsNotEmpty({ message: 'Workspace name is required' })
  @MinLength(2, { message: 'Workspace name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Workspace name must not exceed 100 characters' })
  name: string;

  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;

  @IsOptional()
  @IsString({ message: 'Industry must be a string' })
  @MaxLength(50, { message: 'Industry must not exceed 50 characters' })
  industry?: string;

  @IsOptional()
  @IsString({ message: 'Website must be a string' })
  @MaxLength(200, { message: 'Website must not exceed 200 characters' })
  website?: string;

  @IsOptional()
  @IsString({ message: 'Timezone must be a string' })
  @MaxLength(50, { message: 'Timezone must not exceed 50 characters' })
  timezone?: string;

  @IsOptional()
  @IsString({ message: 'Language must be a string' })
  @MaxLength(10, { message: 'Language must not exceed 10 characters' })
  language?: string;

  
  @IsString({ message: 'Team size must be a string' })
  team_size?: string;

}

/**
 * DTO for adding a member to workspace
 */
export class CreateMemberDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
  })
  password: string;

  @IsString({ message: 'First name must be a string' })
  @IsNotEmpty({ message: 'First name is required' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  @MaxLength(50, { message: 'First name must not exceed 50 characters' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'First name can only contain letters and spaces' })
  first_name: string;

  @IsString({ message: 'Last name must be a string' })
  @IsNotEmpty({ message: 'Last name is required' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  @MaxLength(50, { message: 'Last name must not exceed 50 characters' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'Last name can only contain letters and spaces' })
  last_name: string;

  @IsNumberString({}, { message: 'Phone number must contain only numbers' })
  @IsNotEmpty({ message: 'Phone number is required' })
  @Matches(/^[0-9]{10,15}$/, { message: 'Phone number must be 10-15 digits' })
  phone: string;

  @IsString({ message: 'Country code must be a string' })
  @IsNotEmpty({ message: 'Country code is required' })
  @Matches(/^\+[1-9]\d{0,3}$/, { message: 'Country code must be in format +XXX' })
  country_code: string;

  @IsString({ message: 'Country must be a string' })
  @IsNotEmpty({ message: 'Country is required' })
  @MaxLength(50, { message: 'Country must not exceed 50 characters' })
  country: string;

  @IsIn(['Admin', 'Manager', 'Team Member'], { message: 'Role must be one of: Admin, Manager, Team Member' })
  @IsNotEmpty({ message: 'Role is required' })
  role: 'Admin' | 'Manager' | 'Team Member';

  @IsOptional()
  @IsString({ message: 'Reports to must be a string' })
  reports_to?: string;

  @IsBoolean({ message: 'WABA access must be a boolean' })
  @IsNotEmpty({ message: 'WABA access is required' })
  waba_access: boolean;

  @IsOptional()
  @IsString({ message: 'Role ID must be a string' })
  role_id?: string;
}

/**
 * DTO for updating workspace
 */
export class UpdateWorkspaceDto {
  @IsOptional()
  @IsString({ message: 'Workspace name must be a string' })
  @MinLength(2, { message: 'Workspace name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Workspace name must not exceed 100 characters' })
  name?: string;

  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;

  @IsOptional()
  @IsString({ message: 'Industry must be a string' })
  @MaxLength(50, { message: 'Industry must not exceed 50 characters' })
  industry?: string;

  @IsOptional()
  @IsString({ message: 'Website must be a string' })
  @MaxLength(200, { message: 'Website must not exceed 200 characters' })
  website?: string;

  @IsOptional()
  @IsString({ message: 'Timezone must be a string' })
  @MaxLength(50, { message: 'Timezone must not exceed 50 characters' })
  timezone?: string;

  @IsOptional()
  @IsString({ message: 'Language must be a string' })
  @MaxLength(10, { message: 'Language must not exceed 10 characters' })
  language?: string;
}

/**
 * DTO for workspace member query parameters
 */
export class WorkspaceMembersQueryDto {
  @IsOptional()
  @IsString({ message: 'Search must be a string' })
  search?: string;

  @IsOptional()
  @IsNumberString({}, { message: 'Page must be a number' })
  page?: string;

  @IsOptional()
  @IsNumberString({}, { message: 'Limit must be a number' })
  limit?: string;

  @IsOptional()
  @IsString({ message: 'Sort by must be a string' })
  @IsIn(['created_at', 'updated_at', 'first_name', 'last_name', 'email', 'role'], {
    message: 'Sort by must be one of: created_at, updated_at, first_name, last_name, email, role'
  })
  sortBy?: string;

  @IsOptional()
  @IsString({ message: 'Sort order must be a string' })
  @IsIn(['asc', 'desc'], { message: 'Sort order must be asc or desc' })
  sortOrder?: string;
}
