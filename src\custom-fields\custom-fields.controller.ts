import { 
  Body, 
  Controller, 
  Delete, 
  Get, 
  Param, 
  Post, 
  Put, 
  Req, 
  UseGuards, 
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import { CustomFieldsService } from './custom-fields.service';
import { CreateCustomFieldDto, UpdateCustomFieldDto } from './dto';
import { AuthGuard } from '../auth/auth.guard';

/**
 * Refactored CustomFieldsController with improved structure and consistent response handling
 */
@Controller('custom-fields')
export class CustomFieldsController {
  constructor(private readonly customFieldsService: CustomFieldsService) {}

  /**
   * Create custom field endpoint
   */
  @Post()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async createCustomField(@Body() createDto: CreateCustomFieldDto, @Req() req: any) {
    return await this.customFieldsService.createCustomField(createDto, req);
  }

  /**
   * Get all custom fields for workspace endpoint
   */
  @Get()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getCustomFields(@Req() req: any) {
    return await this.customFieldsService.getCustomFieldsForWorkspace(req);
  }

  /**
   * Get custom field by ID endpoint
   */
  @Get(':id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getCustomField(@Param('id') id: string, @Req() req: any) {
    return await this.customFieldsService.getCustomFieldById(id, req);
  }

  /**
   * Update custom field endpoint
   */
  @Put(':id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async updateCustomField(
    @Param('id') id: string,
    @Body() updateDto: UpdateCustomFieldDto,
    @Req() req: any,
  ) {
    return await this.customFieldsService.updateCustomField(id, updateDto, req);
  }

  /**
   * Delete custom field endpoint
   */
  @Delete(':id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async deleteCustomField(@Param('id') id: string, @Req() req: any) {
    return await this.customFieldsService.deleteCustomField(id, req);
  }
}


