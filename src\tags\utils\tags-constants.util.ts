/**
 * Constants for tags module
 */
export const TAGS_CONSTANTS = {
  // Default values
  DEFAULTS: {
    TEXT_COLOR: '#FFFFFF',
    BACKGROUND_COLOR: '#3B82F6',
  },

  // Error messages
  ERROR_MESSAGES: {
    TAG_CREATION_FAILED: 'Failed to create tag',
    TAG_NOT_FOUND: 'Tag not found',
    TAG_UPDATE_FAILED: 'Failed to update tag',
    TAG_DELETE_FAILED: 'Failed to delete tag',
    TAGS_FETCH_FAILED: 'Failed to fetch tags',
    USER_WORKSPACE_NOT_FOUND: 'User workspace not found',
    DUPLICATE_TAG_NAME: 'Tag name already exists in workspace',
    INVALID_COLOR_FORMAT: 'Invalid color format. Use hex format (e.g., #FF0000)',
    MISSING_TAG_NAME: 'Tag name is required',
  },

  // Success messages
  SUCCESS_MESSAGES: {
    TAG_CREATED: 'Tag created successfully',
    TAG_UPDATED: 'Tag updated successfully',
    TAG_DELETED: 'Tag deleted successfully',
    TAGS_FETCHED: 'Tags fetched successfully',
    TAG_FETCHED: 'Tag fetched successfully',
  },

  // HTTP status codes
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    NOT_FOUND: 404,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
  },

  // Validation rules
  VALIDATION: {
    NAME_MIN_LENGTH: 1,
    NAME_MAX_LENGTH: 50,
    COLOR_REGEX: /^#[0-9A-Fa-f]{6}$/,
  },
} as const;

/**
 * Type for tags constants
 */
export type TagsConstants = typeof TAGS_CONSTANTS;
