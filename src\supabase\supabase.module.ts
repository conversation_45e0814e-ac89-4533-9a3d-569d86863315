import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { SupabaseService } from './supabase.service';
import { Campaign, CampaignSchema } from 'src/schema/campaign.schema';
import { MetaApiModule } from 'src/meta-api/meta-api.module';

@Module({
  imports: [
    ConfigModule,
    MetaApiModule,
    MongooseModule.forFeature([
      { name: Campaign.name, schema: CampaignSchema }
    ])
  ],
  providers: [SupabaseService],
  exports: [SupabaseService],
})
export class SupabaseModule {} 