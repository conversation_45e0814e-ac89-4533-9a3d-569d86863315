# Template API Documentation

## Overview

The Template API allows you to create WhatsApp Business templates using Meta's exact format requirements. This endpoint accepts components directly in Meta's format, eliminating the need for backend conversion and ensuring 100% compliance with Meta's API specifications.

## Endpoint

```
POST /templates
```

## Authentication

All requests require JWT authentication:

```http
Authorization: Bearer <jwt_token>
```

## Request Format

### Headers
```http
Content-Type: application/json
Authorization: Bearer <jwt_token>
```

### Request Body

```json
{
  "name": "appointment_confirmation_v5",
  "description": "Utility template with dynamic header",
  "category": "UTILITY",
  "language": "en",
  "waba_id": "1159844736009453",
  "components": [
    {
      "type": "HEADER",
      "format": "TEXT",
      "text": "{{1}}",
      "example": {
        "header_text": ["Dental Appointment"]
      }
    },
    {
      "type": "BODY",
      "text": "Hi {{1}}, your booking is confirmed for the selected time. Please contact us if you need to make changes.",
      "example": {
        "body_text": [["Rahul"]]
      }
    }
  ]
}
```

## Field Specifications

### Required Fields

| Field | Type | Description | Validation |
|-------|------|-------------|------------|
| `name` | string | Template name | Max 512 chars, unique per workspace |
| `category` | string | Template category | MARKETING, UTILITY, or AUTHENTICATION |
| `language` | string | Template language | Standard language codes (en_US, es_ES, etc.) |
| `waba_id` | string | WhatsApp Business Account ID | Max 50 chars |
| `components` | array | Template components | Min 1, Max 4 components |

### Optional Fields

| Field | Type | Description | Validation |
|-------|------|-------------|------------|
| `description` | string | Template description | Max 1024 chars |

## Component Types

### 1. HEADER Component

**Required for:** Templates with headers (text, image, video, document, location)

```json
{
  "type": "HEADER",
  "format": "TEXT|IMAGE|VIDEO|DOCUMENT|LOCATION",
  "text": "{{1}}", // Required for TEXT format
  "example": {
    "header_text": ["Sample Header"] // Required if text contains variables
  }
}
```

**Validation Rules:**
- `format` is required
- `text` is required for TEXT format
- `example.header_text` is required if text contains variables
- Only one HEADER component allowed per template

### 2. BODY Component

**Required for:** All templates (mandatory by Meta)

```json
{
  "type": "BODY",
  "text": "Hi {{1}}, your appointment is confirmed.",
  "example": {
    "body_text": [["Rahul"]] // Required if text contains variables
  }
}
```

**Validation Rules:**
- Exactly one BODY component required
- `text` is mandatory
- `example.body_text` is required if text contains variables
- Format: `[["value1", "value2", ...]]` (nested array)

### 3. FOOTER Component

**Optional:** For templates with footer text

```json
{
  "type": "FOOTER",
  "text": "Thank you for choosing us!"
}
```

**Validation Rules:**
- Only one FOOTER component allowed
- `text` is required if component exists
- Max 60 characters

### 4. BUTTONS Component

**Optional:** For interactive templates

```json
{
  "type": "BUTTONS",
  "buttons": [
    {
      "type": "QUICK_REPLY",
      "text": "Yes"
    },
    {
      "type": "URL",
      "text": "Visit Website",
      "url": "https://example.com"
    }
  ]
}
```

**Validation Rules:**
- Multiple BUTTONS components allowed
- Each button must have `type` and `text`
- URL buttons require `url` field
- Phone buttons require `phone_number` field

## Variable Format

### Meta Requirements

1. **Independent Numbering**: Each component starts from `{{1}}`
2. **Sequential Variables**: No gaps ({{1}}, {{2}}, {{3}})
3. **No Special Characters**: No #, $, % in variables
4. **No Dangling Parameters**: Template cannot start/end with {{}}

### Examples

**Correct:**
```json
{
  "text": "Hi {{1}}, your {{2}} is confirmed.",
  "example": {
    "body_text": [["John", "appointment"]]
  }
}
```

**Incorrect:**
```json
{
  "text": "{{1}} starts here", // Dangling parameter
  "example": {
    "body_text": [["John"]]
  }
}
```

## Response Format

### Success Response (201 Created)

```json
{
  "status": "success",
  "code": 201,
  "message": "Template created successfully",
  "data": {
    "template": {
      "id": "template_uuid",
      "name": "appointment_confirmation_v5",
      "meta_template_id": "*********",
      "meta_template_status": "PENDING",
      "category": "UTILITY",
      "language": "en",
      "components": [...],
      "created_at": "2024-01-01T00:00:00.000Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response (400 Bad Request)

```json
{
  "status": "error",
  "code": 400,
  "message": "BODY component is required for Meta templates",
  "data": null,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Common Error Messages

| Error Code | Message | Solution |
|------------|---------|----------|
| 400 | BODY component is required | Add a BODY component to your template |
| 400 | HEADER component must have format specified | Add format field to HEADER component |
| 400 | TEXT HEADER component must have text content | Add text content to HEADER component |
| 400 | Duplicate component types are not allowed | Remove duplicate components (except multiple BUTTONS) |
| 400 | Template name already exists | Use a different template name |
| 400 | Meta credentials not found | Ensure WABA is properly connected |
| 400 | Invalid template data | Check Meta API format compliance |

## Best Practices

### 1. Template Naming
- Use descriptive, unique names
- Include version numbers for updates
- Follow naming conventions (lowercase, underscores)

### 2. Variable Usage
- Keep variables minimal and meaningful
- Use clear example values
- Test with actual data

### 3. Content Guidelines
- Keep messages concise and clear
- Follow WhatsApp Business Policy
- Use appropriate language codes

### 4. Testing
- Always test templates before production
- Use the test endpoint for validation
- Monitor template approval status

## Template Categories

### UTILITY
- Transaction confirmations
- Account updates
- Service notifications
- Appointment reminders

### MARKETING
- Promotional messages
- Product announcements
- Event invitations
- Newsletter content

### AUTHENTICATION
- OTP codes
- Login notifications
- Security alerts
- Verification codes

## Language Codes

| Language | Code |
|----------|------|
| English (US) | en_US |
| English (UK) | en_GB |
| Spanish | es_ES |
| French | fr_FR |
| German | de_DE |
| Hindi | hi_IN |

## Rate Limits

- **Template Creation**: 100 requests per hour per workspace
- **Template Updates**: 50 requests per hour per workspace
- **Template Deletion**: 20 requests per hour per workspace

## Webhook Integration

Template status updates are sent via webhooks:

```json
{
  "event": "template.status_changed",
  "data": {
    "template_id": "template_uuid",
    "meta_template_id": "*********",
    "status": "APPROVED",
    "rejection_reason": null
  }
}
```

## Migration from Legacy API

If migrating from the legacy template API:

1. **Convert Components**: Transform your template structure to Meta format
2. **Update Variables**: Ensure independent numbering per component
3. **Test Thoroughly**: Validate with Meta's requirements
4. **Monitor Status**: Track approval process

## Support

For issues or questions:
- Check error messages for specific validation failures
- Review Meta's official documentation
- Contact support with template ID and error details
