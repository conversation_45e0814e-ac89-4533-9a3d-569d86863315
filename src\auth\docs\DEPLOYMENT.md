# 🚀 Auth Module Deployment Guide

## 📋 Overview
This guide covers deployment, configuration, and maintenance of the auth module in production environments.

## 🔧 Prerequisites

### **System Requirements**
- Node.js 18+ 
- NestJS 10+
- PostgreSQL (via Supabase)
- Redis (optional, for caching)

### **Dependencies**
```json
{
  "@nestjs/common": "^10.0.0",
  "@nestjs/core": "^10.0.0",
  "@nestjs/config": "^3.0.0",
  "class-validator": "^0.14.0",
  "class-transformer": "^0.5.0",
  "@supabase/supabase-js": "^2.0.0"
}
```

## 🌍 Environment Configuration

### **Required Environment Variables**
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=3600

# Application Configuration
NODE_ENV=production
PORT=3000

# Database Configuration (if using direct connection)
DATABASE_URL=postgresql://user:password@host:port/database

# Optional: Redis Configuration
REDIS_URL=redis://localhost:6379
```

### **Environment File Structure**
```
.env
├── .env.development
├── .env.staging
└── .env.production
```

## 🐳 Docker Deployment

### **Dockerfile**
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Expose port
EXPOSE 3000

# Start application
CMD ["npm", "run", "start:prod"]
```

### **Docker Compose**
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
```

## ☁️ Cloud Deployment

### **AWS Deployment**

#### **1. EC2 Instance**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2

# Clone repository
git clone <repository-url>
cd automate-whatsapp-backend

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Start with PM2
pm2 start dist/main.js --name "auth-backend"
pm2 save
pm2 startup
```

#### **2. ECS with Fargate**
```yaml
# task-definition.json
{
  "family": "auth-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "auth-backend",
      "image": "your-account.dkr.ecr.region.amazonaws.com/auth-backend:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "SUPABASE_URL",
          "valueFrom": "arn:aws:ssm:region:account:parameter/auth/supabase-url"
        }
      ]
    }
  ]
}
```

### **Google Cloud Platform**

#### **Cloud Run Deployment**
```yaml
# cloudbuild.yaml
steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/auth-backend', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/auth-backend']
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'auth-backend',
      '--image', 'gcr.io/$PROJECT_ID/auth-backend',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated'
    ]
```

### **Azure Deployment**

#### **Container Instances**
```bash
# Build and push to Azure Container Registry
az acr build --registry myregistry --image auth-backend .

# Deploy to Container Instances
az container create \
  --resource-group myResourceGroup \
  --name auth-backend \
  --image myregistry.azurecr.io/auth-backend:latest \
  --cpu 1 \
  --memory 1 \
  --ports 3000 \
  --environment-variables \
    NODE_ENV=production \
    SUPABASE_URL=$SUPABASE_URL
```

## 🔒 Security Configuration

### **1. HTTPS Configuration**
```typescript
// main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Enable CORS
  app.enableCors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    credentials: true,
  });
  
  // Security headers
  app.use((req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    next();
  });
  
  await app.listen(process.env.PORT || 3000);
}
bootstrap();
```

### **2. Rate Limiting**
```typescript
// app.module.ts
import { ThrottlerModule } from '@nestjs/throttler';

@Module({
  imports: [
    ThrottlerModule.forRoot({
      ttl: 60,
      limit: 10,
    }),
  ],
})
export class AppModule {}
```

### **3. Environment Security**
```bash
# Use secrets management
aws ssm put-parameter \
  --name "/auth/supabase-url" \
  --value "https://your-project.supabase.co" \
  --type "SecureString"

# Rotate secrets regularly
aws ssm update-parameter \
  --name "/auth/jwt-secret" \
  --value "new-secret-key"
```

## 📊 Monitoring & Logging

### **1. Application Monitoring**
```typescript
// monitoring.service.ts
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);

  logAuthEvent(event: string, userId: string, metadata?: any) {
    this.logger.log({
      event,
      userId,
      timestamp: new Date().toISOString(),
      metadata,
    });
  }
}
```

### **2. Health Checks**
```typescript
// health.controller.ts
import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService } from '@nestjs/terminus';

@Controller('health')
export class HealthController {
  constructor(private health: HealthCheckService) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.checkDatabase(),
      () => this.checkSupabase(),
    ]);
  }
}
```

### **3. Logging Configuration**
```typescript
// logger.config.ts
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';

export const loggerConfig = WinstonModule.createLogger({
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.colorize(),
        winston.format.simple(),
      ),
    }),
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json(),
      ),
    }),
  ],
});
```

## 🔄 CI/CD Pipeline

### **GitHub Actions**
```yaml
# .github/workflows/deploy.yml
name: Deploy Auth Module

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          # Deployment commands
          echo "Deploying to production..."
```

### **GitLab CI**
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

test:
  stage: test
  script:
    - npm ci
    - npm run test
    - npm run build

deploy:
  stage: deploy
  script:
    - echo "Deploying to production..."
  only:
    - main
```

## 🛠️ Maintenance

### **1. Database Migrations**
```bash
# Run migrations
npm run migration:run

# Generate new migration
npm run migration:generate -- --name=add-user-roles

# Revert migration
npm run migration:revert
```

### **2. Backup Strategy**
```bash
# Database backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL | gzip > "backups/backup_$DATE.sql.gz"
aws s3 cp "backups/backup_$DATE.sql.gz" s3://your-backup-bucket/
```

### **3. Performance Monitoring**
```typescript
// performance.middleware.ts
import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class PerformanceMiddleware implements NestMiddleware {
  private readonly logger = new Logger(PerformanceMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      this.logger.log(`${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
    });
    
    next();
  }
}
```

## 🚨 Troubleshooting

### **Common Issues**

#### **1. Database Connection Issues**
```bash
# Check connection
npm run db:check

# Reset connection pool
npm run db:reset
```

#### **2. JWT Token Issues**
```typescript
// Debug JWT issues
console.log('Token:', token);
console.log('Decoded:', jwt.decode(token));
```

#### **3. Supabase Connection Issues**
```typescript
// Test Supabase connection
const { data, error } = await supabase
  .from('user_profile')
  .select('count')
  .limit(1);

if (error) {
  console.error('Supabase connection error:', error);
}
```

## 📈 Scaling Considerations

### **1. Horizontal Scaling**
- Use load balancer (ALB, NLB)
- Implement session stickiness
- Database connection pooling

### **2. Vertical Scaling**
- Monitor CPU and memory usage
- Optimize database queries
- Implement caching strategies

### **3. Database Scaling**
- Read replicas for read-heavy operations
- Connection pooling
- Query optimization
