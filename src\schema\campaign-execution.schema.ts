import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type CampaignExecutionDocument = CampaignExecution & Document;

@Schema({ timestamps: true })
export class CampaignExecution {
  @Prop({ required: true, type: Types.ObjectId, ref: 'Campaign' })
  campaign_id: Types.ObjectId;

  @Prop({ required: true })
  execution_id: string;

  @Prop({ required: true, enum: ['PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'PAUSED', 'CANCELLED'] })
  status: string;

  @Prop({ required: true })
  total_contacts: number;

  @Prop({ default: 0 })
  processed_contacts: number;

  @Prop({ default: 0 })
  sent_contacts: number;

  @Prop({ default: 0 })
  failed_contacts: number;

  @Prop({ default: 0 })
  pending_contacts: number;

  @Prop({ type: Object })
  progress: {
    percentage: number;
    current_batch: number;
    total_batches: number;
    batch_size: number;
  };

  @Prop({ type: Object })
  execution_stats: {
    start_time: Date;
    end_time?: Date;
    duration_ms?: number;
    avg_processing_time_ms?: number;
    messages_per_second?: number;
  };

  @Prop({ type: [Object] })
  batch_logs: Array<{
    batch_number: number;
    batch_size: number;
    start_time: Date;
    end_time?: Date;
    status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
    sent_count: number;
    failed_count: number;
    error_message?: string;
  }>;

  @Prop({ type: [Object] })
  error_logs: Array<{
    timestamp: Date;
    level: 'ERROR' | 'WARNING' | 'INFO';
    message: string;
    details?: any;
  }>;

  @Prop({ type: Object })
  kafka_metadata: {
    producer_id: string;
    consumer_group: string;
    topics: string[];
    partitions: number[];
    message_count: number;
  };

  @Prop({ type: Object })
  rate_limiting: {
    enabled: boolean;
    messages_per_second: number;
    delay_between_batches_ms: number;
    last_batch_time?: Date;
  };

  @Prop({ type: Object })
  retry_config: {
    max_retries: number;
    retry_delay_ms: number;
    exponential_backoff: boolean;
    retry_count: number;
  };

  @Prop()
  started_at: Date;

  @Prop()
  completed_at?: Date;

  @Prop()
  paused_at?: Date;

  @Prop()
  resumed_at?: Date;

  @Prop()
  cancelled_at?: Date;

  @Prop({ default: false })
  is_active: boolean;

  @Prop({ type: Object })
  metadata: {
    workspace_id: number;
    user_id: string;
    template_id: string;
    send_type: string;
    scheduled_at?: Date;
  };
}

export const CampaignExecutionSchema = SchemaFactory.createForClass(CampaignExecution);

// Indexes for better performance
CampaignExecutionSchema.index({ campaign_id: 1, status: 1 });
CampaignExecutionSchema.index({ execution_id: 1 });
CampaignExecutionSchema.index({ 'metadata.workspace_id': 1, status: 1 });
CampaignExecutionSchema.index({ started_at: 1 });
CampaignExecutionSchema.index({ 'execution_stats.start_time': 1 });

