import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueService } from './queue.service';
import { QueueController } from './queue.controller';
import { CampaignMessageProcessor, ScheduledCampaignMessageProcessor, RetryCampaignMessageProcessor } from './queue.processor';
import { TemplateModule } from '../template/template.module';
import { MetaApiModule } from '../meta-api/meta-api.module';
import { MetaOnboardingModule } from '../meta-onboarding/meta-onboarding.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Campaign, CampaignSchema } from '../schema/campaign.schema';
import { CampaignExecution, CampaignExecutionSchema } from '../schema/campaign-execution.schema';
import { MessageStatusService } from './message-status.service';
import { AuthModule } from 'src/auth/auth.module';

@Module({
  imports: [
    ConfigModule,
    TemplateModule,
    MetaApiModule,
    MetaOnboardingModule,
    AuthModule,
    MongooseModule.forFeature([
      { name: Campaign.name, schema: CampaignSchema },
      { name: CampaignExecution.name, schema: CampaignExecutionSchema }
    ]),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const redisConfig = {
          host: configService.get<string>('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
          password: configService.get<string>('REDIS_PASSWORD') || undefined,
          db: configService.get<number>('REDIS_DB', 0),
          connectTimeout: 10000,
          lazyConnect: true,
          keepAlive: 30000,
          family: 4, // Force IPv4
          maxLoadingTimeout: 5000,
          enableReadyCheck: true,
          maxRetriesPerRequest: 3,
          retryDelayOnFailover: 100,
          enableOfflineQueue: false,
        };

        // Remove undefined password to avoid Redis auth issues
        if (!redisConfig.password) {
          delete redisConfig.password;
        }

        return {
          redis: redisConfig,
          defaultJobOptions: {
            removeOnComplete: 100,
            removeOnFail: 50,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
          },
        };
      },
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      {
        name: 'campaign-messages',
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      },
      {
        name: 'scheduled-campaign-messages',
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      },
      {
        name: 'campaign-retry-messages',
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 25,
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      },
    ),
  ],
  controllers: [QueueController],
  providers: [
    QueueService,
    CampaignMessageProcessor,
    ScheduledCampaignMessageProcessor,
    RetryCampaignMessageProcessor,
    MessageStatusService,
  ],
  exports: [QueueService, BullModule],
})
export class QueueModule {}
