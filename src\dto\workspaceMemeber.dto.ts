import {
  IsBoolean,
  IsEnum,
  IsNot<PERSON>mpty,
  IsString,
  IsO<PERSON>al,
  IsEmail,
  IsNumberString,
} from 'class-validator';

export class CreateMemberdto {
  @IsEnum(['Admin', 'Team Member', 'Manager'], {
    message:
      'permission must be one of the following values: <PERSON><PERSON>, Team Member, Manager',
  })
  @IsNotEmpty({ message: 'Role is required' })
  role: string;

  @IsString({ message: 'Invite password must be a string' })
  @IsNotEmpty({ message: 'Invite password is required' })
  invite_password: string;

  @IsString({ message: 'Reports to must be a string' })
  @IsOptional({ message: 'Reports to is optional' })
  reports_to?: string;

  @IsBoolean({ message: 'Waba access must be a boolean' })
  @IsOptional()
  waba_access?: boolean;

  @IsBoolean({ message: 'Cards access must be a boolean' })
  @IsOptional()
  cards_access?: boolean;

  @IsString({ message: 'Role ID must be a string' })
  @IsOptional({ message: 'Role ID is required when waba_access is true' })
  role_id?: string;

  // New user creation fields
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @IsNotEmpty({ message: 'Email is required for new user creation' })
  email: string;

  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required for new user creation' })
  password: string;

  @IsString({ message: 'First name must be a string' })
  @IsNotEmpty({ message: 'First name is required' })
  first_name: string;

  @IsString({ message: 'Last name must be a string' })
  @IsNotEmpty({ message: 'Last name is required' })
  last_name: string;

  @IsNumberString({}, { message: 'Phone must be a string' })
  @IsNotEmpty({ message: 'Phone is required' })
  phone: string;

  @IsNumberString({}, { message: 'Country code must be a numeric string' })
  @IsNotEmpty({ message: 'Country code is required' })
  country_code: string;

  @IsString({ message: 'Status must be a string' })
  @IsOptional({ message: 'Status is optional' })
  status?: string;

  @IsString({ message: 'Country must be a string' })
  @IsNotEmpty({ message: 'Country is required' })
  country: string;
}
