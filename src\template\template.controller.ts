import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  HttpCode,
  HttpStatus,
  UploadedFile,
  UseInterceptors
} from '@nestjs/common';
import { TemplateService } from './template.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto, CreateAiTemplateDto } from './dto';

/**
 * Refactored TemplateController with improved structure and consistent response handling
 */
@Controller('templates')
export class TemplateController {
  constructor(private readonly templateService: TemplateService) {}

  // ==================== TEMPLATE CRUD ENDPOINTS ====================

  /**
   * Create template endpoint (Meta format)
   */
  @Post()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async createTemplate(@Body() createTemplateDto: CreateTemplateDto, @Req() req: any) {
    return await this.templateService.createTemplate(createTemplateDto, req);
  }

  /**
   * Create draft template endpoint
   */
  @Post('draft')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async createDraftTemplate(@Body() createTemplateDto: CreateTemplateDto, @Req() req: any) {
    return await this.templateService.createDraftTemplate(createTemplateDto, req);
  }

  /**
   * Get user templates endpoint
   */
  @Get()
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getTemplates(@Query() query: TemplateQueryDto, @Req() req: any) {
    return await this.templateService.getTemplates(req, query);
  }

  /**
   * Get workspace templates endpoint
   */
  @Get('workspace/:workspaceId')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getTemplatesByWorkspace(
    @Param('workspaceId') workspaceId: string,
    @Query() query: TemplateQueryDto,
    @Req() req: any
  ) {
    return await this.templateService.getTemplatesByWorkspace(workspaceId, req, query);
  }

  /**
   * Get template by ID endpoint
   */
  @Get(':id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getTemplateById(@Param('id') id: string, @Req() req: any) {
    return await this.templateService.getTemplateById(id, req);
  }

  /**
   * Update template endpoint
   */
  @Put(':id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async updateTemplate(
    @Param('id') id: string,
    @Body() updateTemplateDto: UpdateTemplateDto,
    @Req() req: any
  ) {
    return await this.templateService.updateTemplate(id, updateTemplateDto, req);
  }

  /**
   * Delete template endpoint
   */
  @Delete(':wabaId/template/:templateId')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async deleteTemplate(
    @Param('wabaId') wabaId: string,
    @Param('templateId') templateId: string,
    @Req() req: any
  ) {
    return await this.templateService.deleteTemplate(templateId, wabaId, req);
  }

  // ==================== META INTEGRATION ENDPOINTS ====================

  /**
   * Sync all templates with Meta endpoint
   */
  @Get('meta/sync/waba/:wabaId')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async syncAllWithMeta(@Param('wabaId') wabaId: string, @Req() req: any) {
    return await this.templateService.syncAllWithMeta(wabaId, req);
  }

  /**
   * Sync single template with Meta endpoint
   */
  @Post(':id/sync')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async syncWithMeta(@Param('id') id: string, @Req() req: any) {
    return await this.templateService.syncWithMeta(id, req);
  }

  /**
   * Get Meta templates endpoint
   */
  @Get('meta/templates')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getMetaTemplates(@Req() req: any) {
    return await this.templateService.getMetaTemplates(req);
  }

  // ==================== AI GENERATION ENDPOINTS ====================

  /**
   * Generate AI template endpoint
   */
  @Post('ai/generate')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async generateAiTemplate(@Body() createAiTemplateDto: CreateAiTemplateDto, @Req() req: any) {
    return await this.templateService.generateAiTemplate(createAiTemplateDto, req);
  }

  /**
   * Generate template from voice endpoint
   */
  @Post('voice/generate')
  @UseGuards(AuthGuard)
  @UseInterceptors(FileInterceptor('audioFile', {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
      // Accept audio files only
      if (file.mimetype.startsWith('audio/')) {
        cb(null, true);
      } else {
        cb(new Error('Only audio files are allowed'), false);
      }
    }
  }))
  @HttpCode(HttpStatus.CREATED)
  async generateTemplateFromVoice(
    @UploadedFile() audioFile: any,
    @Body() body: any,
    @Req() req: any
  ) {
    return await this.templateService.generateTemplateFromVoice(audioFile, body, req);
  }
} 