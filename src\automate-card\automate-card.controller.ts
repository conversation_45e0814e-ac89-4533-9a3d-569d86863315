import { CreateWorkspaceDto } from './../dto/create-workspace.dto';
import { Body, Controller, Post, Req, Res, UseGuards, Get, Put, Param, Delete } from '@nestjs/common';
import { AutomateCardService } from './automate-card.service';
import { CreateMemberdto } from 'src/dto/workspaceMemeber.dto';
import { CreateCompanyProfileDto, UpdateCompanyProfileDto } from 'src/dto/company-profile.dto';
import { UpdateUserProfileCardDto } from 'src/dto/user-profile-card.dto';
import { UpdateMemberAccessDto } from 'src/dto/update-member-access.dto';
import { AuthGuard } from 'src/auth/auth.guard';

@Controller('automate-card')
export class AutomateCardController {
    constructor(private readonly automateCardService: AutomateCardService) {}

    @Post('create/workspace')
    @UseGuards(AuthGuard)
    async createWorkspace(@Body() createWorkspaceDto: CreateWorkspaceDto, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.createCardWorkspace(createWorkspaceDto,req,res);
    }

    @Post('add/member')
    @UseGuards(AuthGuard)
    async addMember(@Body() memberData: CreateMemberdto, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.addMemberToWorkspace(memberData,req,res);
    }

    // Company Profile Endpoints
    @Post('company-profile')
    @UseGuards(AuthGuard)
    async createCompanyProfile(@Body() createCompanyProfileDto: CreateCompanyProfileDto, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.createCompanyProfile(createCompanyProfileDto, req, res);
    }

    @Get('company-profile')
    @UseGuards(AuthGuard)
    async getCompanyProfile(@Req() req: any, @Res() res: Response) {
        return this.automateCardService.getCompanyProfile(req, res);
    }

    @Get('company-profile/:id')
    @UseGuards(AuthGuard)
    async getCompanyProfileById(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.getCompanyProfileById(id, req, res);
    }

    @Put('company-profile/:id')
    @UseGuards(AuthGuard)
    async updateCompanyProfile(@Param('id') id: string, @Body() updateCompanyProfileDto: UpdateCompanyProfileDto, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.updateCompanyProfile(id, updateCompanyProfileDto, req, res);
    }

    @Delete('company-profile/:id')
    @UseGuards(AuthGuard)
    async deleteCompanyProfile(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.deleteCompanyProfile(id, req, res);
    }

    // User Profile Card Endpoints
    @Get('my-card')
    @UseGuards(AuthGuard)
    async getUserProfileCard( @Req() req: any, @Res() res: Response) {
        return this.automateCardService.getUsermyCard(req, res);
    }

    @Put('user-profile-card/:id')
    @UseGuards(AuthGuard)
    async updateUserProfileCard(@Param('id') id: string, @Body() updateUserProfileCardDto: UpdateUserProfileCardDto, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.updateUserProfileCard(id, updateUserProfileCardDto, req, res);
    }

    @Get('user-profile-cards/:id')
    @UseGuards(AuthGuard)
    async getAllUserProfileCards(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.getAllUserProfileCards(id,req, res);
    }

    @Get('my-card/:id')
    @UseGuards(AuthGuard)
    async getSpecificUserCard(@Param('id') id: string, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.getSpecificUserCard(id,req, res);
    }

    // Member Access Management Endpoints
    @Put('member/card-access')
    @UseGuards(AuthGuard)
    async updateMemberCardAccess(@Body() updateMemberAccessDto: UpdateMemberAccessDto, @Req() req: any, @Res() res: Response) {
        return this.automateCardService.updateMemberCardAccess(updateMemberAccessDto, req, res);
    }

}
