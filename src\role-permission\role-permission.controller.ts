import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
  HttpCode,
  HttpStatus,
  Query
} from '@nestjs/common';
import { RolePermissionService } from './role-permission.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { CreateRoleDto, UpdateRoleDto, CreatePermissionDto, UpdatePermissionDto, AssignPermissionDto, RolePermissionQueryDto } from './dto';

/**
 * Refactored RolePermissionController with improved structure and consistent response handling
 */
@Controller('role-permission')
export class RolePermissionController {
  constructor(private readonly rolePermissionService: RolePermissionService) {}

  // ==================== ROLE ENDPOINTS ====================

  /**
   * Create role endpoint
   */
  @Post('roles')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async createRole(@Body() createDto: CreateRoleDto, @Req() req: any) {
    return await this.rolePermissionService.createRole(createDto, req);
  }

  /**
   * Get all roles for workspace endpoint
   */
  @Get('roles')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getRoles(@Query() query: RolePermissionQueryDto, @Req() req: any) {
    return await this.rolePermissionService.getRolesForWorkspace(req, query);
  }

  /**
   * Get role by ID endpoint
   */
  @Get('roles/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getRole(@Param('id') id: string, @Req() req: any) {
    return await this.rolePermissionService.getRoleById(id, req);
  }

  /**
   * Update role endpoint
   */
  @Put('roles/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async updateRole(
    @Param('id') id: string,
    @Body() updateDto: UpdateRoleDto,
    @Req() req: any,
  ) {
    return await this.rolePermissionService.updateRole(id, updateDto, req);
  }

  /**
   * Delete role endpoint
   */
  @Delete('roles/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async deleteRole(@Param('id') id: string, @Req() req: any) {
    return await this.rolePermissionService.deleteRole(id, req);
  }

  // ==================== PERMISSION ENDPOINTS ====================

  /**
   * Create permission endpoint
   */
  @Post('permissions')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async createPermission(@Body() createDto: CreatePermissionDto, @Req() req: any) {
    return await this.rolePermissionService.createPermission(createDto, req);
  }

  /**
   * Get all permissions for workspace endpoint
   */
  @Get('permissions')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getPermissions(@Query() query: RolePermissionQueryDto, @Req() req: any) {
    return await this.rolePermissionService.getPermissionsForWorkspace(req, query);
  }

  /**
   * Get permission by ID endpoint
   */
  @Get('permissions/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getPermission(@Param('id') id: string, @Req() req: any) {
    return await this.rolePermissionService.getPermissionById(id, req);
  }

  /**
   * Update permission endpoint
   */
  @Put('permissions/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async updatePermission(
    @Param('id') id: string,
    @Body() updateDto: UpdatePermissionDto,
    @Req() req: any,
  ) {
    return await this.rolePermissionService.updatePermission(id, updateDto, req);
  }

  /**
   * Delete permission endpoint
   */
  @Delete('permissions/:id')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async deletePermission(@Param('id') id: string, @Req() req: any) {
    return await this.rolePermissionService.deletePermission(id, req);
  }

  // ==================== ROLE-PERMISSION ASSIGNMENT ENDPOINTS ====================

  /**
   * Assign permission to role endpoint
   */
  @Post('assign')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.CREATED)
  async assignPermission(@Body() assignDto: AssignPermissionDto, @Req() req: any) {
    return await this.rolePermissionService.assignPermissionToRole(assignDto, req);
  }

  /**
   * Revoke permission from role endpoint
   */
  @Post('revoke')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async revokePermission(@Body() assignDto: AssignPermissionDto, @Req() req: any) {
    return await this.rolePermissionService.revokePermissionFromRole(assignDto, req);
  }

  /**
   * Get role permissions endpoint
   */
  @Get('roles/:id/permissions')
  @UseGuards(AuthGuard)
  @HttpCode(HttpStatus.OK)
  async getRolePermissions(@Param('id') id: string, @Req() req: any) {
    return await this.rolePermissionService.getRolePermissions(id, req);
  }
}
