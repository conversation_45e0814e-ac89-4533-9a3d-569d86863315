/**
 * Template Constants Utility
 * Centralized constants, error messages, and success messages for the template module
 */

export const TEMPLATE_CONSTANTS = {
  // Table names
  TABLES: {
    TEMPLATES: 'automate_whatsapp_templates',
    META_CREDENTIALS: 'automate_whatsapp_meta_credentials',
  },

  // Template types
  TEMPLATE_TYPES: {
    TEXT: 'text',
    IMAGE: 'image',
    VIDEO: 'video',
    AUDIO: 'audio',
    DOCUMENT: 'document',
    LOCATION: 'location',
    CONTACT: 'contact',
    INTERACTIVE: 'interactive',
  },

  // Template categories
  TEMPLATE_CATEGORIES: {
    MARKETING: 'MARKETING',
    UTILITY: 'UTILITY',
    AUTHENTICATION: 'AUTHENTICATION',
  },

  // Template statuses
  TEMPLATE_STATUSES: {
    DRAFT: 'DRAFT',
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED',
    DISABLED: 'DISABLED',
  },

  // <PERSON><PERSON> types
  BUTTON_TYPES: {
    QUICK_REPLY: 'quick_reply',
    URL: 'url',
    PHONE_NUMBER: 'phone_number',
  },

  // Section types
  SECTION_TYPES: {
    PRODUCT_LIST: 'product_list',
    PRODUCT_CATALOG: 'product_catalog',
  },

  // Pagination defaults
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100,
  },

  // File upload limits
  FILE_LIMITS: {
    MAX_AUDIO_SIZE: 10 * 1024 * 1024, // 10MB
    SUPPORTED_AUDIO_TYPES: ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'],
  },

  // Error messages
  ERROR_MESSAGES: {
    USER_NOT_FOUND: 'User not found',
    USER_PROFILE_NOT_FOUND: 'User profile not found',
    TEMPLATE_NOT_FOUND: 'Template not found',
    TEMPLATE_CREATION_FAILED: 'Failed to create template',
    TEMPLATE_UPDATE_FAILED: 'Failed to update template',
    TEMPLATE_DELETE_FAILED: 'Failed to delete template',
    TEMPLATE_FETCH_FAILED: 'Failed to fetch templates',
    DUPLICATE_TEMPLATE: 'Template with this name and language already exists in your workspace',
    META_CREDENTIALS_NOT_FOUND: 'Meta credentials not found or inactive',
    META_API_ERROR: 'Meta API error occurred',
    META_TOKEN_EXPIRED: 'Your Meta access token has expired. Please reconnect your WhatsApp Business Account to refresh the token.',
    META_PERMISSION_DENIED: 'You do not have permission to create templates. Please check your Meta Business account permissions.',
    META_API_UNAVAILABLE: 'Meta API is temporarily unavailable. Please try again later.',
    META_TEMPLATE_NAME_EXISTS: 'A template with this name already exists in Meta. Please use a different name.',
    META_TEMPLATE_FORMAT_INVALID: 'Template format is invalid. Please check your template structure and try again.',
    TEMPLATE_SYNC_FAILED: 'Failed to sync template with Meta',
    TEMPLATE_BULK_SYNC_FAILED: 'Failed to sync templates from Meta',
    AI_TEMPLATE_GENERATION_FAILED: 'Failed to generate AI template',
    VOICE_TEMPLATE_GENERATION_FAILED: 'Voice-based template generation failed',
    AUDIO_FILE_REQUIRED: 'Audio file is required',
    WABA_ID_REQUIRED: 'WABA ID is required',
    INVALID_AUDIO_FILE: 'Only audio files are allowed',
    TEMPLATE_NOT_APPROVED: 'Template is not approved by Meta. Please get it approved first.',
    TEMPLATE_STATUS_INVALID: 'Only APPROVED templates can be used for sending messages',
    INVALID_CSV_FORMAT: 'Invalid CSV format',
    CSV_MISSING_PHONE: 'CSV must contain at least a header row and one data row',
    INVALID_CONTACT_SELECTION: 'Invalid contact selection type',
    NO_CONTACTS_FOUND: 'No contacts found',
    CAMPAIGN_CREATION_FAILED: 'Failed to create campaign',
    CAMPAIGN_NOT_FOUND: 'Campaign not found',
    CAMPAIGN_UPDATE_FAILED: 'Failed to update campaign',
    CAMPAIGN_DELETE_FAILED: 'Failed to delete campaign',
    CAMPAIGN_FETCH_FAILED: 'Failed to fetch campaigns',
    CAMPAIGN_ACTION_FAILED: 'Failed to execute campaign action',
    TEST_MESSAGE_FAILED: 'Failed to send test messages',
    CAMPAIGN_STATS_FAILED: 'Failed to get campaign statistics',
    CAMPAIGN_CREATION_DATA_FAILED: 'Failed to get campaign creation data',
    TEMPLATE_VARIABLES_FAILED: 'Failed to get template variables',
    SEGMENT_CONDITIONS_FAILED: 'Failed to get segment conditions',
  },

  // Success messages
  SUCCESS_MESSAGES: {
    TEMPLATE_CREATED: 'Template created successfully',
    TEMPLATE_CREATED_WITH_META: 'Template created successfully in both local DB and Meta',
    TEMPLATE_CREATED_LOCAL_ONLY: 'Template created successfully in local DB only',
    TEMPLATE_UPDATED: 'Template updated successfully',
    TEMPLATE_UPDATED_WITH_META: 'Template updated successfully in both local DB and Meta',
    TEMPLATE_UPDATED_LOCAL_ONLY: 'Template updated successfully in local DB only',
    TEMPLATE_DELETED: 'Template deleted successfully',
    TEMPLATE_DELETED_WITH_META: 'Template deleted successfully from both Meta and local database',
    TEMPLATE_DELETED_LOCAL_ONLY: 'Draft template deleted successfully from local database',
    TEMPLATES_FETCHED: 'Templates retrieved successfully',
    WORKSPACE_TEMPLATES_FETCHED: 'Workspace templates retrieved successfully',
    TEMPLATE_FETCHED: 'Template retrieved successfully',
    TEMPLATE_SYNCED: 'Template synced with Meta successfully',
    TEMPLATES_BULK_SYNCED: 'Bulk sync from Meta completed',
    META_TEMPLATES_FETCHED: 'Meta templates retrieved successfully',
    AI_TEMPLATE_GENERATED: 'AI template generated and saved as draft successfully',
    VOICE_TEMPLATE_GENERATED: 'Template generated from voice successfully',
    CAMPAIGN_CREATED: 'Campaign created successfully',
    CAMPAIGNS_FETCHED: 'Campaigns retrieved successfully',
    CAMPAIGN_FETCHED: 'Campaign retrieved successfully',
    CAMPAIGN_UPDATED: 'Campaign updated successfully',
    CAMPAIGN_DELETED: 'Campaign deleted successfully',
    CAMPAIGN_STARTED: 'Campaign started successfully',
    CAMPAIGN_PAUSED: 'Campaign paused successfully',
    CAMPAIGN_RESUMED: 'Campaign resumed successfully',
    CAMPAIGN_CANCELLED: 'Campaign cancelled successfully',
    TEST_MESSAGES_SENT: 'Test messages sent successfully',
    CAMPAIGN_STATS_FETCHED: 'Campaign statistics retrieved successfully',
    CAMPAIGN_CREATION_DATA_FETCHED: 'Campaign creation data retrieved successfully',
    TEMPLATE_VARIABLES_FETCHED: 'Template variables extracted successfully',
    SEGMENT_CONDITIONS_FETCHED: 'Segment conditions retrieved successfully',
  },

  // HTTP status codes
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    NOT_FOUND: 404,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
  },

  // Validation rules
  VALIDATION: {
    NAME_MIN_LENGTH: 1,
    NAME_MAX_LENGTH: 512,
    DESCRIPTION_MAX_LENGTH: 1024,
    CONTENT_MAX_LENGTH: 4096,
    HEADER_TEXT_MAX_LENGTH: 60,
    FOOTER_MAX_LENGTH: 60,
    IMAGE_CAPTION_MAX_LENGTH: 1024,
    LANGUAGE_MAX_LENGTH: 10,
    WABA_ID_MAX_LENGTH: 50,
    PHONE_NUMBER_MAX_LENGTH: 20,
    COUNTRY_CODE_MAX_LENGTH: 5,
  },

  // Contact selection types
  CONTACT_SELECTION_TYPES: {
    ALL_CONTACTS: 'all_contacts',
    SEGMENTED: 'segmented',
    CSV_CONTACTS: 'csv_contacts',
    QUICK_CONTACTS: 'quick_contacts',
  },

  // Campaign statuses
  CAMPAIGN_STATUSES: {
    DRAFT: 'DRAFT',
    ACTIVE: 'ACTIVE',
    PAUSED: 'PAUSED',
    COMPLETED: 'COMPLETED',
    CANCELLED: 'CANCELLED',
    SCHEDULED: 'SCHEDULED',
  },

  // Campaign actions
  CAMPAIGN_ACTIONS: {
    START: 'START',
    PAUSE: 'PAUSE',
    RESUME: 'RESUME',
    CANCEL: 'CANCEL',
  },

  // Send types
  SEND_TYPES: {
    IMMEDIATE: 'immediate',
    SCHEDULED: 'scheduled',
  },
} as const;
