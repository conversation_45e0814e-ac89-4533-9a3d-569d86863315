# Tags Module Documentation

## Overview

The Tags module provides functionality for creating and managing tags within workspaces. Tags allow users to categorize and organize contacts with custom colors and labels.

## Table of Contents

- [Architecture](ARCHITECTURE.md) - Module architecture and design patterns
- [API Endpoints](API_ENDPOINTS.md) - Complete API documentation
- [Security](SECURITY.md) - Security considerations and best practices
- [Testing](TESTING.md) - Testing strategies and test cases
- [Deployment](DEPLOYMENT.md) - Deployment guidelines

## Features

- ✅ Create tags with custom names and colors
- ✅ Update existing tags
- ✅ Delete tags
- ✅ List all tags for workspace
- ✅ Get specific tag by ID
- ✅ Workspace-scoped access control
- ✅ Comprehensive validation
- ✅ Consistent error handling
- ✅ Detailed logging

## Quick Start

### Prerequisites

- User must be authenticated
- User must belong to a workspace
- Proper permissions for tag management

### Basic Usage

```typescript
// Create a tag with default colors
POST /tags
{
  "name": "VIP Customer"
}

// Create a tag with custom colors
POST /tags
{
  "name": "High Priority",
  "text_color": "#FFFFFF",
  "background_color": "#FF0000"
}
```

## Module Structure

```
tags/
├── tags.controller.ts        # API endpoints
├── tags.service.ts           # Business logic
├── tags.module.ts            # Module configuration
├── dto/                      # Data transfer objects
│   ├── create-tag.dto.ts
│   ├── update-tag.dto.ts
│   └── index.ts
├── utils/                    # Utility classes
│   ├── tags-constants.util.ts
│   ├── tags-validation.util.ts
│   └── tags-response.util.ts
└── docs/                     # Documentation
    ├── INDEX.md
    ├── ARCHITECTURE.md
    ├── API_ENDPOINTS.md
    ├── SECURITY.md
    ├── TESTING.md
    └── DEPLOYMENT.md
```

## Tag Properties

### Required Fields

- **`name`**: Tag display name (1-50 characters, alphanumeric + spaces/hyphens/underscores)

### Optional Fields

- **`text_color`**: Text color in hex format (e.g., `#FFFFFF`)
- **`background_color`**: Background color in hex format (e.g., `#3B82F6`)

### Default Values

- **`text_color`**: `#FFFFFF` (white)
- **`background_color`**: `#3B82F6` (blue)

## Color Format

Tags support hex color codes for both text and background colors:

- **Format**: `#RRGGBB` (6-digit hex)
- **Examples**: `#FF0000` (red), `#00FF00` (green), `#0000FF` (blue)
- **Case**: Automatically converted to uppercase

## API Endpoints

All endpoints follow the same patterns as the auth module:

- `POST /tags` - Create tag
- `GET /tags` - Get all tags
- `GET /tags/:id` - Get tag by ID
- `PUT /tags/:id` - Update tag
- `DELETE /tags/:id` - Delete tag

## Response Format

All responses follow the standardized format:

```json
{
  "status": "success|error",
  "code": 200,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Validation Rules

### Name Validation

- Required field
- Must be a string
- 1-50 characters long
- Can contain letters, numbers, spaces, hyphens, and underscores
- Automatically trimmed of whitespace

### Color Validation

- Optional fields
- Must be valid hex color format (`#RRGGBB`)
- Automatically converted to uppercase
- Default values applied if not provided

## Security Features

- ✅ Authentication required for all endpoints
- ✅ Workspace-scoped access control
- ✅ Input validation and sanitization
- ✅ SQL injection prevention
- ✅ XSS protection
- ✅ Rate limiting support

## Performance Considerations

- Database indexes on workspaceId and name
- Lean queries for read operations
- Efficient validation using utility classes
- Proper error handling to prevent resource leaks

## Monitoring and Logging

- Comprehensive logging at service level
- Error tracking and monitoring
- Performance metrics collection
- Audit trail for tag operations

## Dependencies

- `@nestjs/common` - NestJS core functionality
- `@nestjs/mongoose` - MongoDB integration
- `class-validator` - DTO validation
- `mongoose` - MongoDB ODM
- `../auth/auth.guard` - Authentication
- `../supabase/supabase.service` - User profile management

## Use Cases

### Contact Organization

Tags are commonly used to:
- Categorize contacts by type (Customer, Lead, VIP)
- Mark contact status (Active, Inactive, Blocked)
- Group by source (Website, Referral, Campaign)
- Priority levels (High, Medium, Low)

### Integration with Segments

Tags can be used in segment rules:
```json
{
  "field": "tagsId",
  "operator": "hasAnyTag",
  "value": ["tag1", "tag2"]
}
```

### Visual Organization

Custom colors help with:
- Quick visual identification
- Brand consistency
- User interface organization
- Status indication

## Version History

- **v1.0.0** - Initial implementation with basic CRUD operations
- **v1.1.0** - Added color support and validation
- **v1.2.0** - Added comprehensive validation and error handling
- **v1.3.0** - Refactored following auth module patterns
- **v1.4.0** - Added complete documentation and test coverage
