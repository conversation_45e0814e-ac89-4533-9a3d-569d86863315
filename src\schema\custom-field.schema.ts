import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CustomFieldDocument = CustomField & Document;

export enum CustomFieldTypeEnum {
	TEXT = 'text',
	NUMBER = 'number',
	DATE = 'date',
	DATETIME = 'datetime',
	DROPDOWN = 'dropdown',
	BOOL = 'bool',
}

@Schema({ timestamps: true })
export class CustomField {
	@Prop({ type: String, required: true })
	label!: string;

	@Prop({ type: String, enum: Object.values(CustomFieldTypeEnum), required: true })
	type!: CustomFieldTypeEnum;

	@Prop({ type: [String], default: [] })
	options?: string[]; // used when type is dropdown

	@Prop({ type: Boolean, default: false })
	showOnContact?: boolean;

	@Prop({ type: Boolean, default: false })
	showOnChat?: boolean;

	@Prop({ type: Number, required: true })
	workspaceId!: number;

	@Prop({ type: String, required: true })
	createdBy!: string;
}

export const CustomFieldSchema = SchemaFactory.createForClass(CustomField);

// Unique key within a workspace
CustomFieldSchema.index({ workspaceId: 1, label: 1 }, { unique: true });


