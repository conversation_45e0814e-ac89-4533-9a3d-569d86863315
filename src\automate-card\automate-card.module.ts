import { Module } from '@nestjs/common';
import { AutomateCardService } from './automate-card.service';
import { AutomateCardController } from './automate-card.controller';
import { AuthModule } from 'src/auth/auth.module';
import { SupabaseModule } from 'src/supabase/supabase.module';
import { PublicProfileController } from './public-profile.controller';

@Module({
  imports:[SupabaseModule,
    AuthModule],
  providers: [AutomateCardService],
  controllers: [AutomateCardController,PublicProfileController]
})
export class AutomateCardModule {}
