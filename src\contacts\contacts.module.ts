import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ContactsController } from './contacts.controller';
import { ContactsService } from './contacts.service';
import { AuthModule } from 'src/auth/auth.module';
import { Contact, ContactSchema } from 'src/schema/contacts.schema';
import { SupabaseModule } from 'src/supabase/supabase.module';

@Module({
  imports: [
    AuthModule,
    SupabaseModule,
    MongooseModule.forFeature([{ name: Contact.name, schema: ContactSchema }])
  ],
  controllers: [ContactsController],
  providers: [ContactsService]

})
export class ContactsModule {}
