import { IsEmail, IsNot<PERSON>mpty, IsOptional, IsString, MinLength, Matches, IsNumberString, IsBoolean } from 'class-validator';

export class SignUpDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, { 
    message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number' 
  })
  password: string;

  @IsString({ message: 'First name must be a string' })
  @IsNotEmpty({ message: 'First name is required' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'First name can only contain letters and spaces' })
  first_name: string;

  @IsString({ message: 'Last name must be a string' })
  @IsNotEmpty({ message: 'Last name is required' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'Last name can only contain letters and spaces' })
  last_name: string;

  @IsNumberString({}, { message: 'Phone number must contain only numbers' })
  @IsNotEmpty({ message: 'Phone number is required' })
  @MinLength(10, { message: 'Phone number must be at least 10 digits' })
  @Matches(/^[0-9]+$/, { message: 'Phone number must contain only digits' })
  phoneNumber: string;


  @IsOptional()
  @IsString({ message: 'Country code must be a string' })
  @Matches(/^\+?[1-9]\d{0,3}$/, { message: 'Please provide a valid country code' })
  countrycode?: string;

  @IsNotEmpty({ message: 'Country is required' })
  @IsString({ message: 'Country must be a string' })
  @MinLength(2, { message: 'Country name must be at least 2 characters long' })
  country?: string;

  @IsNotEmpty({ message: 'Terms and conditions are required' })
  @IsBoolean({ message: 'Terms and conditions must be a boolean' })
  terms_conditions: boolean;
}

export class SignInDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  password: string;
}

export class UpdateProfileDto {
  @IsOptional()
  @IsString({ message: 'First name must be a string' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'First name can only contain letters and spaces' })
  first_name?: string;

  @IsOptional()
  @IsString({ message: 'Last name must be a string' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  @Matches(/^[a-zA-Z\s]+$/, { message: 'Last name can only contain letters and spaces' })
  last_name?: string;

  @IsOptional()
  @IsNumberString({}, { message: 'Phone number must contain only numbers' })
  @MinLength(10, { message: 'Phone number must be at least 10 digits' })
  @Matches(/^[0-9]+$/, { message: 'Phone number must contain only digits' })
  phone?: string;

  @IsOptional()
  @IsString({ message: 'Country code must be a string' })
  @Matches(/^\+?[1-9]\d{0,3}$/, { message: 'Please provide a valid country code' })
  country_code?: string;

  @IsOptional()
  @IsString({ message: 'Country must be a string' })
  @MinLength(2, { message: 'Country name must be at least 2 characters long' })
  country?: string;

  @IsOptional()
  @IsString({ message: 'Time zone must be a string' })
  time_zone?: string;

  @IsOptional()
  @IsString({ message: 'Language must be a string' })
  language?: string;

  @IsOptional()
  @IsString({ message: 'Profile picture must be a string' })
  profile_image?: string;
}

export class ResetPasswordDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @IsString({ message: 'Redirect URL must be a string' })
  @IsNotEmpty({ message: 'Redirect URL is required' })
  redirect_url: string;
} 

export class RefreshTokenDto {
  @IsString({ message: 'Refresh token must be a string' })
  @IsNotEmpty({ message: 'Refresh token is required' })
  refresh_token: string;
}

export class ChangePasswordDto {
  @IsString({ message: 'Current password must be a string' })
  @IsNotEmpty({ message: 'Current password is required' })
  current_password: string;

  @IsString({ message: 'New password must be a string' })
  @IsNotEmpty({ message: 'New password is required' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
  })
  new_password: string;
}