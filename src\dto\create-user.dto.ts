import { IsEmail, IsNotEmpty, Is<PERSON>umberString, IsO<PERSON>al, IsString, Min<PERSON><PERSON><PERSON>, Matches, IsIn } from 'class-validator';

export class CreateUserDto {
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @IsString({ message: 'First name must be a string' })
  @IsNotEmpty({ message: 'First name is required' })
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  first_name: string;

  @IsString({ message: 'Last name must be a string' })
  @IsNotEmpty({ message: 'Last name is required' })
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  last_name: string;

  @IsNumberString({}, { message: 'Phone number must contain only numbers' })
  @IsNotEmpty({ message: 'Phone number is required' })
  @MinLength(10, { message: 'Phone number must be at least 10 digits' })
  @Matches(/^[0-9]+$/, { message: 'Phone number must contain only digits' })
  phoneNumber: string;

  @IsOptional()
  @IsString({ message: 'Profile picture must be a string' })
  profilePicture?: string;

  @IsNumberString({}, { message: 'Country code must contain only numbers' })
  @IsNotEmpty({ message: 'Country code is required' })
  @MinLength(1, { message: 'Country code must be at least 1 character' })
  countrycode: string;

  @IsString({ message: 'Country must be a string' })
  @IsNotEmpty({ message: 'Country is required' })
  @MinLength(2, { message: 'Country must be at least 2 characters' })
  country: string;

} 