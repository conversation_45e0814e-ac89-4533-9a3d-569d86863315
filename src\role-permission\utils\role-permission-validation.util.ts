import { BadRequestException, UnauthorizedException, NotFoundException } from '@nestjs/common';
import { ROLE_PERMISSION_CONSTANTS } from './role-permission-constants.util';

/**
 * Validation utilities for Role-Permission module
 */
export class RolePermissionValidationUtil {
  /**
   * Validates user context from request
   */
  static validateUserContext(req: any): any {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException(ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.USER_NOT_FOUND);
    }
    return user;
  }

  /**
   * Validates user profile and returns workspace ID
   */
  static validateUserProfile(userProfile: any, userProfileError: any): number {
    if (userProfileError || !userProfile) {
      throw new BadRequestException(ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
    }
    return userProfile.workspace_id;
  }

  /**
   * Validates role creation data
   */
  static validateRoleCreationData(createDto: any, userId: string, workspaceId: number): any {
    if (!createDto.name || typeof createDto.name !== 'string') {
      throw new BadRequestException('Role name is required and must be a string');
    }

    if (createDto.name.length < ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_NAME_MIN_LENGTH ||
        createDto.name.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_NAME_MAX_LENGTH) {
      throw new BadRequestException(
        `Role name must be between ${ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_NAME_MIN_LENGTH} and ${ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_NAME_MAX_LENGTH} characters`
      );
    }

    if (createDto.description && createDto.description.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_DESCRIPTION_MAX_LENGTH) {
      throw new BadRequestException(
        `Role description must not exceed ${ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_DESCRIPTION_MAX_LENGTH} characters`
      );
    }

    return {
      name: createDto.name.trim(),
      description: createDto.description?.trim() || '',
      status: createDto.status || ROLE_PERMISSION_CONSTANTS.DEFAULTS.ROLE_STATUS,
      workspaceId,
      createdBy: userId,
    };
  }

  /**
   * Validates role update data
   */
  static validateRoleUpdateData(updateDto: any): any {
    const updateData: any = {};

    if (updateDto.name !== undefined) {
      if (typeof updateDto.name !== 'string') {
        throw new BadRequestException('Role name must be a string');
      }
      if (updateDto.name.length < ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_NAME_MIN_LENGTH ||
          updateDto.name.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_NAME_MAX_LENGTH) {
        throw new BadRequestException(
          `Role name must be between ${ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_NAME_MIN_LENGTH} and ${ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_NAME_MAX_LENGTH} characters`
        );
      }
      updateData.name = updateDto.name.trim();
    }

    if (updateDto.description !== undefined) {
      if (typeof updateDto.description !== 'string') {
        throw new BadRequestException('Role description must be a string');
      }
      if (updateDto.description.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_DESCRIPTION_MAX_LENGTH) {
        throw new BadRequestException(
          `Role description must not exceed ${ROLE_PERMISSION_CONSTANTS.VALIDATION.ROLE_DESCRIPTION_MAX_LENGTH} characters`
        );
      }
      updateData.description = updateDto.description.trim();
    }

    if (updateDto.status !== undefined) {
      if (!['active', 'inactive'].includes(updateDto.status)) {
        throw new BadRequestException('Role status must be either "active" or "inactive"');
      }
      updateData.status = updateDto.status;
    }

    return updateData;
  }

  /**
   * Validates permission creation data
   */
  static validatePermissionCreationData(createDto: any, userId: string, workspaceId: number): any {
    if (!createDto.name || typeof createDto.name !== 'string') {
      throw new BadRequestException('Permission name is required and must be a string');
    }

    if (createDto.name.length < ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_NAME_MIN_LENGTH ||
        createDto.name.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_NAME_MAX_LENGTH) {
      throw new BadRequestException(
        `Permission name must be between ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_NAME_MIN_LENGTH} and ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_NAME_MAX_LENGTH} characters`
      );
    }

    if (!createDto.resource || typeof createDto.resource !== 'string') {
      throw new BadRequestException('Permission resource is required and must be a string');
    }

    if (createDto.resource.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_RESOURCE_MAX_LENGTH) {
      throw new BadRequestException(
        `Permission resource must not exceed ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_RESOURCE_MAX_LENGTH} characters`
      );
    }

    if (!createDto.action || typeof createDto.action !== 'string') {
      throw new BadRequestException('Permission action is required and must be a string');
    }

    if (createDto.action.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_ACTION_MAX_LENGTH) {
      throw new BadRequestException(
        `Permission action must not exceed ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_ACTION_MAX_LENGTH} characters`
      );
    }

    if (createDto.description && createDto.description.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_DESCRIPTION_MAX_LENGTH) {
      throw new BadRequestException(
        `Permission description must not exceed ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_DESCRIPTION_MAX_LENGTH} characters`
      );
    }

    return {
      name: createDto.name.trim(),
      resource: createDto.resource.trim(),
      action: createDto.action.trim(),
      description: createDto.description?.trim() || '',
      status: createDto.status || ROLE_PERMISSION_CONSTANTS.DEFAULTS.PERMISSION_STATUS,
      workspaceId,
      createdBy: userId,
    };
  }

  /**
   * Validates permission update data
   */
  static validatePermissionUpdateData(updateDto: any): any {
    const updateData: any = {};

    if (updateDto.name !== undefined) {
      if (typeof updateDto.name !== 'string') {
        throw new BadRequestException('Permission name must be a string');
      }
      if (updateDto.name.length < ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_NAME_MIN_LENGTH ||
          updateDto.name.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_NAME_MAX_LENGTH) {
        throw new BadRequestException(
          `Permission name must be between ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_NAME_MIN_LENGTH} and ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_NAME_MAX_LENGTH} characters`
        );
      }
      updateData.name = updateDto.name.trim();
    }

    if (updateDto.resource !== undefined) {
      if (typeof updateDto.resource !== 'string') {
        throw new BadRequestException('Permission resource must be a string');
      }
      if (updateDto.resource.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_RESOURCE_MAX_LENGTH) {
        throw new BadRequestException(
          `Permission resource must not exceed ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_RESOURCE_MAX_LENGTH} characters`
        );
      }
      updateData.resource = updateDto.resource.trim();
    }

    if (updateDto.action !== undefined) {
      if (typeof updateDto.action !== 'string') {
        throw new BadRequestException('Permission action must be a string');
      }
      if (updateDto.action.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_ACTION_MAX_LENGTH) {
        throw new BadRequestException(
          `Permission action must not exceed ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_ACTION_MAX_LENGTH} characters`
        );
      }
      updateData.action = updateDto.action.trim();
    }

    if (updateDto.description !== undefined) {
      if (typeof updateDto.description !== 'string') {
        throw new BadRequestException('Permission description must be a string');
      }
      if (updateDto.description.length > ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_DESCRIPTION_MAX_LENGTH) {
        throw new BadRequestException(
          `Permission description must not exceed ${ROLE_PERMISSION_CONSTANTS.VALIDATION.PERMISSION_DESCRIPTION_MAX_LENGTH} characters`
        );
      }
      updateData.description = updateDto.description.trim();
    }

    if (updateDto.status !== undefined) {
      if (!['active', 'inactive'].includes(updateDto.status)) {
        throw new BadRequestException('Permission status must be either "active" or "inactive"');
      }
      updateData.status = updateDto.status;
    }

    return updateData;
  }

  /**
   * Validates permission assignment data
   */
  static validatePermissionAssignmentData(assignDto: any): any {
    if (!assignDto.roleId || typeof assignDto.roleId !== 'string') {
      throw new BadRequestException('Role ID is required and must be a string');
    }

    if (!assignDto.permissionId || typeof assignDto.permissionId !== 'string') {
      throw new BadRequestException('Permission ID is required and must be a string');
    }

    return {
      roleId: assignDto.roleId.trim(),
      permissionId: assignDto.permissionId.trim(),
    };
  }

  /**
   * Validates pagination parameters
   */
  static validatePaginationParams(page?: number, limit?: number): { page: number; limit: number } {
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.min(
      Math.max(1, limit || ROLE_PERMISSION_CONSTANTS.DEFAULTS.PAGINATION_LIMIT),
      ROLE_PERMISSION_CONSTANTS.DEFAULTS.PAGINATION_MAX_LIMIT
    );

    return { page: validatedPage, limit: validatedLimit };
  }

  /**
   * Validates role exists
   */
  static validateRoleExists(role: any, roleId: string): void {
    if (!role) {
      throw new NotFoundException(`Role with ID ${roleId} not found`);
    }
  }

  /**
   * Validates permission exists
   */
  static validatePermissionExists(permission: any, permissionId: string): void {
    if (!permission) {
      throw new NotFoundException(`Permission with ID ${permissionId} not found`);
    }
  }
}
