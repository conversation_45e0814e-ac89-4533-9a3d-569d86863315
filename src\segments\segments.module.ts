import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Segment, SegmentSchema } from 'src/schema/segment.schema';
import { SegmentsService } from './segments.service';
import { SegmentsController } from './segments.controller';
import { AuthModule } from 'src/auth/auth.module';
import { Contact, ContactSchema } from 'src/schema/contacts.schema';
import { SupabaseModule } from 'src/supabase/supabase.module';

@Module({
  imports: [
    AuthModule,
    MongooseModule.forFeature([
      { name: Segment.name, schema: SegmentSchema },
      { name: Contact.name, schema: ContactSchema },
    ]),
    SupabaseModule,
  ],
  controllers: [SegmentsController],
  providers: [SegmentsService],
})
export class SegmentsModule {}


