import { Injectable, BadRequestException, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Tag } from 'src/schema/tag.schema';
import { SupabaseService } from 'src/supabase/supabase.service';
import { CreateTagDto, UpdateTagDto } from './dto';
import { TagsResponseUtil, TagsResponseData } from './utils/tags-response.util';
import { TagsValidationUtil } from './utils/tags-validation.util';
import { TAGS_CONSTANTS } from './utils/tags-constants.util';

/**
 * Refactored TagsService with improved structure and consistent response handling
 */
@Injectable()
export class TagsService {
  private readonly logger = new Logger(TagsService.name);
  
  constructor(
    @InjectModel(Tag.name) private tagModel: Model<Tag>,
    private readonly supabaseService: SupabaseService,
  ) {}

  // ==================== PUBLIC METHODS ====================

  /**
   * Create tag
   */
  async createTag(createDto: CreateTagDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting tag creation process');

      const user = TagsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = TagsValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Validate and prepare data
      const payload = TagsValidationUtil.validateTagCreationData(createDto, user.id, workspaceId);

      // Check for duplicate name in workspace
      const existing = await this.tagModel.findOne({ workspaceId, name: payload.name });
      if (existing) {
        return TagsResponseUtil.createDuplicateErrorResponse(
          TAGS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_TAG_NAME
        );
      }

      const tag = new this.tagModel(payload);
      
      let savedTag;
      try {
        savedTag = await tag.save();
      } catch (error: any) {
        // Handle duplicate key error from Mongo unique index
        if (error?.code === 11000) {
          return TagsResponseUtil.createDuplicateErrorResponse(
            TAGS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_TAG_NAME
          );
        }
        this.logger.error('Failed to create tag', error);
        throw new BadRequestException(TAGS_CONSTANTS.ERROR_MESSAGES.TAG_CREATION_FAILED);
      }

      this.logger.log(`Tag created successfully: ${savedTag._id}`);

      const responseData = TagsResponseUtil.createTagCreationData(savedTag);
      return TagsResponseUtil.createSuccessResponse(
        responseData,
        TAGS_CONSTANTS.SUCCESS_MESSAGES.TAG_CREATED,
        TAGS_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Create tag failed:', error);
      this.handleTagsError(error);
    }
  }

  /**
   * Get all tags for workspace
   */
  async getTagsForWorkspace(req: any): Promise<any> {
    try {
      this.logger.log('Starting get tags for workspace process');

      const user = TagsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = TagsValidationUtil.validateUserProfile(userProfile, userProfileError);

      const tags = await this.tagModel
        .find({ workspaceId })
        .sort({ name: 1 })
        .lean();

      this.logger.log(`Tags fetched successfully: ${tags.length} total tags`);

      const responseData = TagsResponseUtil.createTagsListData(tags);
      return TagsResponseUtil.createSuccessResponse(
        responseData,
        TAGS_CONSTANTS.SUCCESS_MESSAGES.TAGS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get tags for workspace failed:', error);
      this.handleTagsError(error);
    }
  }

  /**
   * Get tag by ID
   */
  async getTagById(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get tag process for ID: ${id}`);

      const user = TagsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = TagsValidationUtil.validateUserProfile(userProfile, userProfileError);

      const tag = await this.tagModel
        .findOne({ _id: id, workspaceId })
        .lean();

      TagsValidationUtil.validateTagExists(tag, id);

      this.logger.log(`Tag fetched successfully: ${id}`);

      const responseData = TagsResponseUtil.createTagCreationData(tag);
      return TagsResponseUtil.createSuccessResponse(
        responseData,
        TAGS_CONSTANTS.SUCCESS_MESSAGES.TAG_FETCHED
      );

    } catch (error) {
      this.logger.error('Get tag failed:', error);
      this.handleTagsError(error);
    }
  }

  /**
   * Update tag
   */
  async updateTag(id: string, updateDto: UpdateTagDto, req: any): Promise<any> {
    try {
      this.logger.log(`Starting update tag process for ID: ${id}`);

      const user = TagsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = TagsValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Get current tag to validate existence
      const currentTag = await this.tagModel.findOne({ _id: id, workspaceId });
      TagsValidationUtil.validateTagExists(currentTag, id);

      // Validate update data
      const updateData = TagsValidationUtil.validateTagUpdateData(updateDto);

      // Check for duplicate name if name is being updated
      if (updateData.name) {
        const existing = await this.tagModel.findOne({
          _id: { $ne: id },
          workspaceId,
          name: updateData.name
        });
        if (existing) {
          return TagsResponseUtil.createDuplicateErrorResponse(
            TAGS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_TAG_NAME
          );
        }
      }

      try {
        const updated = await this.tagModel.findOneAndUpdate(
          { _id: id, workspaceId },
          { $set: updateData },
          { new: true }
        );

        this.logger.log(`Tag updated successfully: ${id}`);

        const responseData = TagsResponseUtil.createTagCreationData(updated);
        return TagsResponseUtil.createSuccessResponse(
          responseData,
          TAGS_CONSTANTS.SUCCESS_MESSAGES.TAG_UPDATED
        );

      } catch (error: any) {
        if (error?.code === 11000) {
          return TagsResponseUtil.createDuplicateErrorResponse(
            TAGS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_TAG_NAME
          );
        }
        this.logger.error('Failed to update tag', error);
        throw new BadRequestException(TAGS_CONSTANTS.ERROR_MESSAGES.TAG_UPDATE_FAILED);
      }

    } catch (error) {
      this.logger.error('Update tag failed:', error);
      this.handleTagsError(error);
    }
  }

  /**
   * Delete tag
   */
  async deleteTag(id: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting delete tag process for ID: ${id}`);

      const user = TagsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = TagsValidationUtil.validateUserProfile(userProfile, userProfileError);

      const deletedTag = await this.tagModel.findOneAndDelete({ _id: id, workspaceId });
      TagsValidationUtil.validateTagExists(deletedTag, id);

      this.logger.log(`Tag deleted successfully: ${id}`);

      return TagsResponseUtil.createSuccessResponse(
        {},
        TAGS_CONSTANTS.SUCCESS_MESSAGES.TAG_DELETED
      );

    } catch (error) {
      this.logger.error('Delete tag failed:', error);
      this.handleTagsError(error);
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Handles tags-related errors
   */
  private handleTagsError(error: any): void {
    if (error instanceof BadRequestException || 
        error instanceof NotFoundException || 
        error instanceof ConflictException) {
      throw error;
    }
    
    this.logger.error('Unexpected tags error:', error);
    throw new BadRequestException('Internal server error');
  }
}


