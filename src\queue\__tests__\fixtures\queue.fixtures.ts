export const mockCampaignMessage = {
  campaignId: 'campaign-123',
  contactId: 'contact-456',
  phoneNumber: '+1234567890',
  countryCode: '+1',
  templateId: 'template-789',
  phoneNumberId: 'phone-id-123',
  variableMapping: {
    body: { '1': 'Hello World!' },
    header: { '1': 'Welcome' },
  },
  workspaceId: 1,
  userId: 'user-123',
  retryCount: 0,
  priority: 'NORMAL' as const,
};

export const mockScheduledMessage = {
  ...mockCampaignMessage,
  scheduledAt: new Date(Date.now() + 60000), // 1 minute from now
};

export const mockRetryMessage = {
  ...mockCampaignMessage,
  retryCount: 1,
  originalError: 'Network timeout',
};

export const mockQueueStats = {
  campaignMessages: {
    waiting: 10,
    active: 2,
    completed: 100,
    failed: 5,
  },
  scheduledMessages: {
    waiting: 5,
    active: 1,
    completed: 50,
    failed: 2,
  },
  retryMessages: {
    waiting: 3,
    active: 0,
    completed: 20,
    failed: 1,
  },
};

export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  role: 'admin',
  permissions: ['queue:read', 'queue:write', 'queue:admin'],
  workspaceId: 1,
};
