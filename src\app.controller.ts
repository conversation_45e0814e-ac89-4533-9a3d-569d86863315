import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';
import { DatabaseService } from './database/database.service';
import { QueueService } from './queue/queue.service';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly databaseService: DatabaseService,
    private readonly queueService: QueueService,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('health')
  async getHealth() {
    try {
      const dbConnected = await this.databaseService.isDatabaseConnected();
      const dbStatus = this.databaseService.getConnectionStatus();

      let redisStatus = 'unknown';
      let redisConnected = false;

      try {
        await this.queueService.getQueueStats();
        redisConnected = true;
        redisStatus = 'connected';
      } catch (error) {
        redisStatus = `error: ${error.message}`;
      }

      const overallStatus = dbConnected && redisConnected ? 'ok' : 'degraded';

      return {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        database: {
          connected: dbConnected,
          status: dbStatus,
        },
        redis: {
          connected: redisConnected,
          status: redisStatus,
        },
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  @Get('health/database')
  async getDatabaseHealth() {
    const dbConnected = await this.databaseService.isDatabaseConnected();
    const dbStatus = this.databaseService.getConnectionStatus();
    const dbInfo = await this.databaseService.getDatabaseInfo();

    return {
      connected: dbConnected,
      status: dbStatus,
      info: dbInfo,
    };
  }

  @Get('health/redis')
  async getRedisHealth() {
    try {
      const queueStats = await this.queueService.getQueueStats();
      return {
        connected: true,
        status: 'connected',
        queues: queueStats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        connected: false,
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get('health/detailed')
  async getDetailedHealth() {
    try {
      const [dbConnected, queueStats] = await Promise.all([
        this.databaseService.isDatabaseConnected(),
        this.queueService.getQueueStats().catch(err => ({ error: err.message }))
      ]);

      const dbStatus = this.databaseService.getConnectionStatus();
      const redisConnected = !queueStats.error;

      return {
        status: dbConnected && redisConnected ? 'healthy' : 'degraded',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: {
          database: {
            connected: dbConnected,
            status: dbStatus,
          },
          redis: {
            connected: redisConnected,
            status: redisConnected ? 'connected' : 'error',
            queues: redisConnected ? queueStats : undefined,
            error: queueStats.error || undefined,
          },
        },
        system: {
          memory: {
            used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
            external: Math.round(process.memoryUsage().external / 1024 / 1024),
          },
          cpu: process.cpuUsage(),
          pid: process.pid,
        },
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }
}
