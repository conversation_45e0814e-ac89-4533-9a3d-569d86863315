import { Response } from 'express';

/**
 * Standard workspace response interface
 */
export interface StandardWorkspaceResponse {
  status: 'success' | 'error';
  code: number;
  message: string;
  data?: any;
  error?: any;
  timestamp: string;
}

/**
 * Workspace response data interface
 */
export interface WorkspaceResponseData {
  workspace?: any;
  members?: any[];
  member?: any;
  user?: any;
  profile?: any;
  automate_member?: any;
  created_at?: string;
  user_id?: string;
  user_profile_updated?: boolean;
  user_profile?: any;
  workspace_member?: any;
  admin_role?: any;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    nextPage: number | null;
    prevPage: number | null;
  };
  filters?: {
    search?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

/**
 * Utility class for creating consistent workspace responses
 */
export class WorkspaceResponseUtil {
  /**
   * Creates a success response
   */
  static createSuccessResponse(
    data: WorkspaceResponseData | any,
    message: string = 'Operation completed successfully',
    code: number = 200
  ): StandardWorkspaceResponse {
    return {
      status: 'success',
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates an error response
   */
  static createErrorResponse(
    message: string,
    error?: any,
    code: number = 400
  ): StandardWorkspaceResponse {
    return {
      status: 'error',
      code,
      message,
      error: error?.message || error,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates pagination metadata
   */
  static createPaginationMetadata(
    page: number,
    limit: number,
    total: number
  ) {
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      currentPage: page,
      totalPages,
      totalItems: total,
      itemsPerPage: limit,
      hasNextPage,
      hasPrevPage,
      nextPage: hasNextPage ? page + 1 : null,
      prevPage: hasPrevPage ? page - 1 : null
    };
  }

  /**
   * Creates workspace creation response data
   */
  static createWorkspaceCreationData(
    workspace: any,
    userProfile: any,
    workspaceMember: any,
    adminRole: any,
    automateMember: any,
    userId: string
  ): WorkspaceResponseData {
    return {
      workspace,
      created_at: new Date().toISOString(),
      user_id: userId,
      user_profile_updated: true,
      user_profile: userProfile,
      workspace_member: workspaceMember,
      admin_role: adminRole,
      automate_member: automateMember
    };
  }

  /**
   * Creates member addition response data
   */
  static createMemberAdditionData(
    user: any,
    profile: any,
    member: any,
    automateMember?: any
  ): WorkspaceResponseData {
    return {
      user,
      profile,
      member,
      automate_member: automateMember
    };
  }

  /**
   * Creates workspace members response data
   */
  static createMembersResponseData(
    members: any[],
    pagination: any,
    filters: any
  ): WorkspaceResponseData {
    return {
      members,
      pagination,
      filters
    };
  }
}
