import { IsString, IsNotEmpty, IsOptional, IsArray, IsObject, IsEnum, IsDateString, IsNumber, IsBoolean, ValidateNested, IsPhoneNumber, Matches, MinLength } from 'class-validator';
import { Type } from 'class-transformer';
import { PartialType } from '@nestjs/mapped-types';

export class FilterConditionDto {
  @IsString()
  @IsNotEmpty()
  field: string;

  @IsString()
  @IsNotEmpty()
  operator: string;

  @IsNotEmpty()
  value: any;
}

export class ContactFiltersDto {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FilterConditionDto)
  conditions?: FilterConditionDto[];
}

export class CsvContactDto {
  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  email?: string;


  @IsString()
  @IsNotEmpty()
  country_code?: string;

  @IsOptional()
  @IsObject()
  custom_fields?: Record<string, any>;
}

export class CsvMappingDto {
  @IsNumber()
  phone_column: number; // 0-based index of phone number column

  @IsOptional()
  @IsNumber()
  name_column?: number; // 0-based index of name column

  @IsOptional()
  @IsNumber()
  email_column?: number; // 0-based index of email column

  @IsNumber()
  @IsNotEmpty()
  country_code_column:number;

  @IsOptional()
  @IsObject()
  custom_field_mapping?: Record<string, number>; // field_name: column_index
}

export class VariableMappingDto {
  @IsOptional()
  @IsObject()
  header?: Record<string, string>;

  @IsOptional()
  @IsObject()
  body?: Record<string, string>;

  @IsOptional()
  @IsObject()
  buttons?: Record<string, string>;

  @IsOptional()
  @IsObject()
  footer?: Record<string, string>;
}

export class RecurringSettingsDto {
  @IsEnum(['DAILY', 'WEEKLY', 'MONTHLY'])
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';

  @IsNumber()
  interval: number;

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  days_of_week?: number[];

  @IsOptional()
  @IsNumber()
  day_of_month?: number;

  @IsOptional()
  @IsDateString()
  end_date?: string;

  @IsOptional()
  @IsNumber()
  max_occurrences?: number;
}

export class CreateCampaignDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  @IsNotEmpty()
  template_id: string;

  @IsString()
  @IsNotEmpty()
  phone_number_id: string;

  // Contact Selection Configuration
  @IsEnum(['all_contacts', 'segmented', 'csv_contacts','quick_contacts'])
  contact_selection_type: 'all_contacts' | 'segmented' | 'csv_contacts'|'quick_contacts';

  @IsOptional()
  @ValidateNested()
  @Type(() => ContactFiltersDto)
  contact_filters?: ContactFiltersDto;

  @IsOptional()
  @IsString()
  csv_contacts_string?: string; // CSV string from frontend

  @IsOptional()
  @ValidateNested()
  @Type(() => CsvMappingDto)
  csv_mapping?: CsvMappingDto; // Column mapping for CSV file

  // Template Variables Mapping
  @IsOptional()
  @ValidateNested()
  @Type(() => VariableMappingDto)
  variable_mapping?: VariableMappingDto;
  
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  quick_contacts?: string[];

  // Campaign Settings
  @IsEnum(['IMMEDIATE', 'SCHEDULED', 'RECURRING'])
  send_type: 'IMMEDIATE' | 'SCHEDULED' | 'RECURRING';

  @IsOptional()
  @IsDateString()
  scheduled_at?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => RecurringSettingsDto)
  recurring_settings?: RecurringSettingsDto;
}


export class UpdateCampaignDto extends PartialType(CreateCampaignDto) {}


export class CampaignQueryDto {
  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  send_type?: string;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsNumber()
  page?: number;

  @IsOptional()
  @IsNumber()
  limit?: number;

  @IsOptional()
  @IsString()
  sort_by?: string;

  @IsOptional()
  @IsString()
  sort_order?: 'asc' | 'desc';
}

export class SendTestMessageDto {
  @IsString()
  @IsNotEmpty()
  phone_number_id: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;

  @IsArray()
  @IsString({ each: true })
  @Matches(/^[0-9]+$/, { each: true, message: 'Each phone number must contain only digits' })
  test_contacts: string[];

  @IsString()
  @IsNotEmpty()
  template_id: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => VariableMappingDto)
  variable_mapping?: VariableMappingDto;
}

export class CampaignActionDto {
  @IsEnum(['START', 'PAUSE', 'RESUME', 'CANCEL'])
  action: 'START' | 'PAUSE' | 'RESUME' | 'CANCEL';
}

export class CampaignStatsDto {
  @IsString()
  @IsNotEmpty()
  campaign_id: string;

  @IsOptional()
  @IsDateString()
  start_date?: string;

  @IsOptional()
  @IsDateString()
  end_date?: string;
}




