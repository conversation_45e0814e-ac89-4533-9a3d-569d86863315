import { BadRequestException, UnauthorizedException, NotFoundException } from '@nestjs/common';
import { META_ONBOARDING_CONSTANTS } from './meta-onboarding-constants.util';

/**
 * Validation utilities for Meta-Onboarding module
 */
export class MetaOnboardingValidationUtil {
  /**
   * Validates user context from request
   */
  static validateUserContext(req: any): any {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.USER_NOT_FOUND);
    }
    return user;
  }

  /**
   * Validates user profile and returns workspace ID
   */
  static validateUserProfile(userProfile: any, userProfileError: any): number {
    if (userProfileError || !userProfile) {
      throw new BadRequestException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
    }
    return userProfile.workspace_id;
  }

  /**
   * Validates meta credentials creation data
   */
  static validateMetaCredentialsCreationData(createDto: any, userId: string, workspaceId: number): any {
    if (!createDto.whatsapp_business_id || typeof createDto.whatsapp_business_id !== 'string') {
      throw new BadRequestException('WhatsApp Business ID is required and must be a string');
    }

    if (createDto.whatsapp_business_id.length < META_ONBOARDING_CONSTANTS.VALIDATION.WHATSAPP_BUSINESS_ID_MIN_LENGTH ||
        createDto.whatsapp_business_id.length > META_ONBOARDING_CONSTANTS.VALIDATION.WHATSAPP_BUSINESS_ID_MAX_LENGTH) {
      throw new BadRequestException(
        `WhatsApp Business ID must be between ${META_ONBOARDING_CONSTANTS.VALIDATION.WHATSAPP_BUSINESS_ID_MIN_LENGTH} and ${META_ONBOARDING_CONSTANTS.VALIDATION.WHATSAPP_BUSINESS_ID_MAX_LENGTH} characters`
      );
    }

    if (!createDto.phone_number_id || typeof createDto.phone_number_id !== 'string') {
      throw new BadRequestException('Phone Number ID is required and must be a string');
    }

    if (createDto.phone_number_id.length < META_ONBOARDING_CONSTANTS.VALIDATION.PHONE_NUMBER_ID_MIN_LENGTH ||
        createDto.phone_number_id.length > META_ONBOARDING_CONSTANTS.VALIDATION.PHONE_NUMBER_ID_MAX_LENGTH) {
      throw new BadRequestException(
        `Phone Number ID must be between ${META_ONBOARDING_CONSTANTS.VALIDATION.PHONE_NUMBER_ID_MIN_LENGTH} and ${META_ONBOARDING_CONSTANTS.VALIDATION.PHONE_NUMBER_ID_MAX_LENGTH} characters`
      );
    }

    if (!createDto.access_token || typeof createDto.access_token !== 'string') {
      throw new BadRequestException('Access token is required and must be a string');
    }

    if (createDto.access_token.length < META_ONBOARDING_CONSTANTS.VALIDATION.ACCESS_TOKEN_MIN_LENGTH ||
        createDto.access_token.length > META_ONBOARDING_CONSTANTS.VALIDATION.ACCESS_TOKEN_MAX_LENGTH) {
      throw new BadRequestException(
        `Access token must be between ${META_ONBOARDING_CONSTANTS.VALIDATION.ACCESS_TOKEN_MIN_LENGTH} and ${META_ONBOARDING_CONSTANTS.VALIDATION.ACCESS_TOKEN_MAX_LENGTH} characters`
      );
    }

    if (createDto.status && !Object.values(META_ONBOARDING_CONSTANTS.STATUS_VALUES).includes(createDto.status)) {
      throw new BadRequestException(`Status must be one of: ${Object.values(META_ONBOARDING_CONSTANTS.STATUS_VALUES).join(', ')}`);
    }

    return {
      whatsapp_business_id: createDto.whatsapp_business_id.trim(),
      phone_number_id: createDto.phone_number_id.trim(),
      access_token: createDto.access_token.trim(),
      workspace_id: workspaceId,
      created_by: userId,
      status: createDto.status || META_ONBOARDING_CONSTANTS.DEFAULTS.CREDENTIALS_STATUS,
    };
  }

  /**
   * Validates meta credentials update data
   */
  static validateMetaCredentialsUpdateData(updateDto: any): any {
    const updateData: any = {};

    if (updateDto.whatsapp_business_id !== undefined) {
      if (typeof updateDto.whatsapp_business_id !== 'string') {
        throw new BadRequestException('WhatsApp Business ID must be a string');
      }
      if (updateDto.whatsapp_business_id.length < META_ONBOARDING_CONSTANTS.VALIDATION.WHATSAPP_BUSINESS_ID_MIN_LENGTH ||
          updateDto.whatsapp_business_id.length > META_ONBOARDING_CONSTANTS.VALIDATION.WHATSAPP_BUSINESS_ID_MAX_LENGTH) {
        throw new BadRequestException(
          `WhatsApp Business ID must be between ${META_ONBOARDING_CONSTANTS.VALIDATION.WHATSAPP_BUSINESS_ID_MIN_LENGTH} and ${META_ONBOARDING_CONSTANTS.VALIDATION.WHATSAPP_BUSINESS_ID_MAX_LENGTH} characters`
        );
      }
      updateData.whatsapp_business_id = updateDto.whatsapp_business_id.trim();
    }

    if (updateDto.phone_number_id !== undefined) {
      if (typeof updateDto.phone_number_id !== 'string') {
        throw new BadRequestException('Phone Number ID must be a string');
      }
      if (updateDto.phone_number_id.length < META_ONBOARDING_CONSTANTS.VALIDATION.PHONE_NUMBER_ID_MIN_LENGTH ||
          updateDto.phone_number_id.length > META_ONBOARDING_CONSTANTS.VALIDATION.PHONE_NUMBER_ID_MAX_LENGTH) {
        throw new BadRequestException(
          `Phone Number ID must be between ${META_ONBOARDING_CONSTANTS.VALIDATION.PHONE_NUMBER_ID_MIN_LENGTH} and ${META_ONBOARDING_CONSTANTS.VALIDATION.PHONE_NUMBER_ID_MAX_LENGTH} characters`
        );
      }
      updateData.phone_number_id = updateDto.phone_number_id.trim();
    }

    if (updateDto.access_token !== undefined) {
      if (typeof updateDto.access_token !== 'string') {
        throw new BadRequestException('Access token must be a string');
      }
      if (updateDto.access_token.length < META_ONBOARDING_CONSTANTS.VALIDATION.ACCESS_TOKEN_MIN_LENGTH ||
          updateDto.access_token.length > META_ONBOARDING_CONSTANTS.VALIDATION.ACCESS_TOKEN_MAX_LENGTH) {
        throw new BadRequestException(
          `Access token must be between ${META_ONBOARDING_CONSTANTS.VALIDATION.ACCESS_TOKEN_MIN_LENGTH} and ${META_ONBOARDING_CONSTANTS.VALIDATION.ACCESS_TOKEN_MAX_LENGTH} characters`
        );
      }
      updateData.access_token = updateDto.access_token.trim();
    }

    if (updateDto.status !== undefined) {
      if (!Object.values(META_ONBOARDING_CONSTANTS.STATUS_VALUES).includes(updateDto.status)) {
        throw new BadRequestException(`Status must be one of: ${Object.values(META_ONBOARDING_CONSTANTS.STATUS_VALUES).join(', ')}`);
      }
      updateData.status = updateDto.status;
    }

    return updateData;
  }

  /**
   * Validates pagination parameters
   */
  static validatePaginationParams(page?: number, limit?: number): { page: number; limit: number } {
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.min(
      Math.max(1, limit || META_ONBOARDING_CONSTANTS.DEFAULTS.PAGINATION_LIMIT),
      META_ONBOARDING_CONSTANTS.DEFAULTS.PAGINATION_MAX_LIMIT
    );

    return { page: validatedPage, limit: validatedLimit };
  }

  /**
   * Validates workspace access
   */
  static validateWorkspaceAccess(workspaceMember: any, workspaceMemberError: any, workspaceId: string): void {
    if (workspaceMemberError || !workspaceMember) {
      throw new UnauthorizedException(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.WORKSPACE_ACCESS_DENIED);
    }
  }

  /**
   * Validates credentials exist
   */
  static validateCredentialsExist(credentials: any, credentialsId: string): void {
    if (!credentials) {
      throw new NotFoundException(`Meta credentials with ID ${credentialsId} not found`);
    }
  }
}
