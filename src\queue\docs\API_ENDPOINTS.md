# Queue Module API Endpoints

## Overview

This document provides comprehensive documentation for all API endpoints in the Queue Module, including request/response formats, authentication requirements, and usage examples.

## Base URL

```
http://localhost:3000/queue
```

## Authentication

All endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### Queue Management

#### 1. Get Queue Statistics

Get comprehensive statistics for all queues.

#### Endpoint
```
GET /queue/stats
```

#### Description
Retrieves real-time statistics for all configured queues including waiting, active, completed, and failed job counts.

#### Authentication
- **Required**: Yes
- **Roles**: Admin, Manager

#### Request

**Headers:**
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Query Parameters:**
```
None
```

#### Response

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Queue statistics retrieved successfully",
  "data": {
    "campaignMessages": {
      "waiting": 10,
      "active": 2,
      "completed": 100,
      "failed": 5,
      "delayed": 3
    },
    "scheduledMessages": {
      "waiting": 5,
      "active": 1,
      "completed": 50,
      "failed": 2,
      "delayed": 0
    },
    "retryMessages": {
      "waiting": 3,
      "active": 0,
      "completed": 20,
      "failed": 1,
      "delayed": 0
    },
    "total": {
      "waiting": 18,
      "active": 3,
      "completed": 170,
      "failed": 8,
      "delayed": 3
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (401):**
```json
{
  "status": "error",
  "code": 401,
  "message": "Unauthorized access",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (500):**
```json
{
  "status": "error",
  "code": 500,
  "message": "Failed to retrieve queue statistics",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Example Usage

```bash
curl -X GET "http://localhost:3000/queue/stats" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

#### 2. Pause Queue

Pause a specific queue to stop processing new jobs.

#### Endpoint
```
POST /queue/pause/:queueName
```

#### Description
Pauses the specified queue, preventing new jobs from being processed while allowing currently active jobs to complete.

#### Authentication
- **Required**: Yes
- **Roles**: Admin, Manager

#### Request

**Headers:**
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Path Parameters:**
- `queueName` (string, required): Name of the queue to pause
  - Valid values: `campaign-messages`, `scheduled-campaign-messages`, `campaign-retry-messages`

#### Response

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Queue campaign-messages paused successfully",
  "data": {
    "queueName": "campaign-messages",
    "status": "paused",
    "pausedAt": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (400):**
```json
{
  "status": "error",
  "code": 400,
  "message": "Invalid queue name. Valid values: campaign-messages, scheduled-campaign-messages, campaign-retry-messages",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (404):**
```json
{
  "status": "error",
  "code": 404,
  "message": "Queue not found",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Example Usage

```bash
curl -X POST "http://localhost:3000/queue/pause/campaign-messages" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

#### 3. Resume Queue

Resume a paused queue to start processing jobs again.

#### Endpoint
```
POST /queue/resume/:queueName
```

#### Description
Resumes the specified queue, allowing it to process waiting jobs again.

#### Authentication
- **Required**: Yes
- **Roles**: Admin, Manager

#### Request

**Headers:**
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Path Parameters:**
- `queueName` (string, required): Name of the queue to resume
  - Valid values: `campaign-messages`, `scheduled-campaign-messages`, `campaign-retry-messages`

#### Response

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Queue campaign-messages resumed successfully",
  "data": {
    "queueName": "campaign-messages",
    "status": "active",
    "resumedAt": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (400):**
```json
{
  "status": "error",
  "code": 400,
  "message": "Invalid queue name. Valid values: campaign-messages, scheduled-campaign-messages, campaign-retry-messages",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (404):**
```json
{
  "status": "error",
  "code": 404,
  "message": "Queue not found",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Example Usage

```bash
curl -X POST "http://localhost:3000/queue/resume/campaign-messages" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

#### 4. Clear Queue

Clear all jobs from a specific queue.

#### Endpoint
```
POST /queue/clear/:queueName
```

#### Description
Removes all jobs (waiting, active, completed, failed) from the specified queue.

#### Authentication
- **Required**: Yes
- **Roles**: Admin

#### Request

**Headers:**
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Path Parameters:**
- `queueName` (string, required): Name of the queue to clear
  - Valid values: `campaign-messages`, `scheduled-campaign-messages`, `campaign-retry-messages`

#### Response

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Queue campaign-messages cleared successfully",
  "data": {
    "queueName": "campaign-messages",
    "clearedAt": "2024-01-01T00:00:00.000Z",
    "jobsRemoved": {
      "waiting": 10,
      "active": 2,
      "completed": 100,
      "failed": 5
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (400):**
```json
{
  "status": "error",
  "code": 400,
  "message": "Invalid queue name. Valid values: campaign-messages, scheduled-campaign-messages, campaign-retry-messages",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response (404):**
```json
{
  "status": "error",
  "code": 404,
  "message": "Queue not found",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Example Usage

```bash
curl -X POST "http://localhost:3000/queue/clear/campaign-messages" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

### Message Status Management

#### 5. Get Campaign Statistics

Get comprehensive statistics for a specific campaign.

#### Endpoint
```
GET /queue/campaign/:campaignId/stats
```

#### Description
Retrieves detailed statistics for a specific campaign including total contacts, sent messages, failed messages, and execution details.

#### Authentication
- **Required**: Yes
- **Roles**: Admin, Manager, User

#### Request

**Headers:**
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Path Parameters:**
- `campaignId` (string, required): ID of the campaign

#### Response

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Campaign statistics retrieved successfully",
  "data": {
    "total": 1000,
    "sent": 850,
    "failed": 50,
    "processed": 900,
    "pending": 100,
    "status": "RUNNING",
    "startTime": "2024-01-01T00:00:00.000Z",
    "endTime": null,
    "duration": 300000
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Example Usage

```bash
curl -X GET "http://localhost:3000/queue/campaign/campaign-123/stats" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

#### 6. Get Message Status for Contact

Get the delivery status of a message for a specific contact.

#### Endpoint
```
GET /queue/campaign/:campaignId/status/:contactId
```

#### Description
Retrieves the delivery status and details for a specific contact in a campaign.

#### Authentication
- **Required**: Yes
- **Roles**: Admin, Manager, User

#### Request

**Headers:**
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Path Parameters:**
- `campaignId` (string, required): ID of the campaign
- `contactId` (string, required): ID of the contact

#### Response

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Message status retrieved successfully",
  "data": {
    "contactId": "contact-456",
    "status": "SENT",
    "errorMessage": null,
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Example Usage

```bash
curl -X GET "http://localhost:3000/queue/campaign/campaign-123/status/contact-456" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

#### 7. Get All Message Statuses for Campaign

Get all message statuses and error logs for a campaign.

#### Endpoint
```
GET /queue/campaign/:campaignId/statuses
```

#### Description
Retrieves all message statuses, error logs, and execution details for a campaign.

#### Authentication
- **Required**: Yes
- **Roles**: Admin, Manager

#### Request

**Headers:**
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
```

**Path Parameters:**
- `campaignId` (string, required): ID of the campaign

#### Response

**Success Response (200):**
```json
{
  "status": "success",
  "code": 200,
  "message": "Campaign message statuses retrieved successfully",
  "data": {
    "totalContacts": 1000,
    "sentContacts": 850,
    "failedContacts": 50,
    "processedContacts": 900,
    "errorLogs": [
      {
        "timestamp": "2024-01-01T00:00:00.000Z",
        "level": "ERROR",
        "message": "Network timeout",
        "details": {
          "contactId": "contact-456",
          "messageId": "msg-789",
          "retryCount": 2
        }
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Example Usage

```bash
curl -X GET "http://localhost:3000/queue/campaign/campaign-123/statuses" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

## Queue Types

### 1. Campaign Messages Queue

**Queue Name:** `campaign-messages`

**Purpose:** Process immediate campaign messages

**Job Type:** `process-campaign-message`

**Job Data:**
```typescript
interface CampaignMessage {
  campaignId: string;
  contactId: string;
  phoneNumber: string;
  countryCode: string;
  templateId: string;
  phoneNumberId: string;
  variableMapping: Record<string, any>;
  workspaceId: number;
  userId: string;
  retryCount: number;
  priority: 'HIGH' | 'NORMAL' | 'LOW';
}
```

### 2. Scheduled Campaign Messages Queue

**Queue Name:** `scheduled-campaign-messages`

**Purpose:** Process delayed/scheduled campaign messages

**Job Type:** `process-scheduled-campaign-message`

**Job Data:**
```typescript
interface ScheduledCampaignMessage extends CampaignMessage {
  scheduledAt: Date;
}
```

### 3. Campaign Retry Messages Queue

**Queue Name:** `campaign-retry-messages`

**Purpose:** Process failed message retries

**Job Type:** `process-retry-campaign-message`

**Job Data:**
```typescript
interface RetryCampaignMessage extends CampaignMessage {
  retryCount: number;
  originalError?: string;
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Queue not found |
| 500 | Internal Server Error - System error |

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Rate Limit:** 100 requests per minute per user
- **Burst Limit:** 10 requests per second
- **Headers:** 
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Time when limit resets

## Webhook Integration

### Queue Events

The system can send webhooks for queue events:

**Event Types:**
- `queue.paused`
- `queue.resumed`
- `queue.cleared`
- `job.completed`
- `job.failed`

**Webhook Payload:**
```json
{
  "event": "queue.paused",
  "data": {
    "queueName": "campaign-messages",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "userId": "user-123"
  }
}
```

## SDK Examples

### JavaScript/TypeScript

```typescript
import axios from 'axios';

class QueueAPI {
  private baseURL = 'http://localhost:3000/queue';
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  async getQueueStats() {
    const response = await axios.get(`${this.baseURL}/stats`, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }

  async pauseQueue(queueName: string) {
    const response = await axios.post(`${this.baseURL}/pause/${queueName}`, {}, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }

  async resumeQueue(queueName: string) {
    const response = await axios.post(`${this.baseURL}/resume/${queueName}`, {}, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }

  async clearQueue(queueName: string) {
    const response = await axios.post(`${this.baseURL}/clear/${queueName}`, {}, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }
}

// Usage
const queueAPI = new QueueAPI('your-jwt-token');
const stats = await queueAPI.getQueueStats();
console.log(stats);
```

### Python

```python
import requests
import json

class QueueAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def get_queue_stats(self):
        response = requests.get(f'{self.base_url}/stats', headers=self.headers)
        return response.json()

    def pause_queue(self, queue_name):
        response = requests.post(f'{self.base_url}/pause/{queue_name}', headers=self.headers)
        return response.json()

    def resume_queue(self, queue_name):
        response = requests.post(f'{self.base_url}/resume/{queue_name}', headers=self.headers)
        return response.json()

    def clear_queue(self, queue_name):
        response = requests.post(f'{self.base_url}/clear/{queue_name}', headers=self.headers)
        return response.json()

# Usage
queue_api = QueueAPI('http://localhost:3000/queue', 'your-jwt-token')
stats = queue_api.get_queue_stats()
print(stats)
```

## Testing

### Postman Collection

Import the following Postman collection for testing:

```json
{
  "info": {
    "name": "Queue Module API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Get Queue Stats",
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{jwt_token}}"
          }
        ],
        "url": {
          "raw": "{{base_url}}/queue/stats",
          "host": ["{{base_url}}"],
          "path": ["queue", "stats"]
        }
      }
    }
  ]
}
```

### Unit Tests

```typescript
describe('QueueController', () => {
  let controller: QueueController;
  let queueService: QueueService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [QueueController],
      providers: [
        {
          provide: QueueService,
          useValue: {
            getQueueStats: jest.fn(),
            pauseQueue: jest.fn(),
            resumeQueue: jest.fn(),
            clearQueue: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<QueueController>(QueueController);
    queueService = module.get<QueueService>(QueueService);
  });

  it('should get queue stats', async () => {
    const mockStats = {
      campaignMessages: { waiting: 10, active: 2, completed: 100, failed: 5 },
      scheduledMessages: { waiting: 5, active: 1, completed: 50, failed: 2 },
      retryMessages: { waiting: 3, active: 0, completed: 20, failed: 1 },
    };

    jest.spyOn(queueService, 'getQueueStats').mockResolvedValue(mockStats);

    const result = await controller.getQueueStats();
    expect(result).toEqual(mockStats);
  });
});
```

## Best Practices

### 1. Error Handling

```typescript
try {
  const stats = await queueAPI.getQueueStats();
  console.log('Queue stats:', stats);
} catch (error) {
  if (error.response?.status === 401) {
    console.error('Authentication failed');
  } else if (error.response?.status === 404) {
    console.error('Queue not found');
  } else {
    console.error('Unexpected error:', error.message);
  }
}
```

### 2. Retry Logic

```typescript
async function retryOperation(operation: () => Promise<any>, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 3. Monitoring

```typescript
// Monitor queue health
setInterval(async () => {
  try {
    const stats = await queueAPI.getQueueStats();
    if (stats.data.total.failed > 100) {
      console.warn('High failure rate detected');
    }
  } catch (error) {
    console.error('Failed to get queue stats:', error);
  }
}, 60000); // Check every minute
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify JWT token is valid
   - Check token expiration
   - Ensure proper Authorization header format

2. **Queue Not Found**
   - Verify queue name spelling
   - Check if queue is properly configured
   - Ensure Redis connection is active

3. **Permission Denied**
   - Verify user has required role
   - Check workspace permissions
   - Ensure user is active

4. **Rate Limiting**
   - Implement exponential backoff
   - Cache responses when possible
   - Monitor rate limit headers

### Debug Mode

Enable debug logging by setting the environment variable:

```bash
DEBUG=queue:*
npm run start:dev
```

This will provide detailed logging for queue operations and help identify issues.
