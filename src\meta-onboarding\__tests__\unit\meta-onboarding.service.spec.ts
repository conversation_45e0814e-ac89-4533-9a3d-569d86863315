import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { MetaOnboardingService } from '../../meta-onboarding.service';
import { SupabaseService } from '../../../supabase/supabase.service';
import { MetaOnboardingValidationUtil } from '../../utils/meta-onboarding-validation.util';
import { MetaOnboardingResponseUtil } from '../../utils/meta-onboarding-response.util';
import { META_ONBOARDING_CONSTANTS } from '../../utils/meta-onboarding-constants.util';

describe('MetaOnboardingService', () => {
  let service: MetaOnboardingService;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>'
  };

  const mockUserProfile = {
    workspace_id: 1,
    id: 'user-123'
  };

  const mockCredentials = {
    id: 'credentials-123',
    whatsapp_business_id: '123456789',
    phone_number_id: '987654321',
    access_token: 'mock-access-token',
    status: 'Active',
    workspace_id: 1,
    created_by: 'user-123',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      getUserProfile: jest.fn(),
      getClient: jest.fn().mockReturnValue({
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn(),
              neq: jest.fn().mockReturnValue({
                single: jest.fn()
              })
            }),
            neq: jest.fn().mockReturnValue({
              single: jest.fn()
            }),
            order: jest.fn().mockReturnValue({
              range: jest.fn()
            }),
            range: jest.fn()
          }),
          insert: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn()
            })
          }),
          update: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn()
              })
            })
          }),
          delete: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn()
              })
            })
          })
        })
      }),
      getWorkspaceMember: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MetaOnboardingService,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService
        }
      ],
    }).compile();

    service = module.get<MetaOnboardingService>(MetaOnboardingService);
    supabaseService = module.get(SupabaseService);
  });

  describe('createMetaCredentials', () => {
    it('should create meta credentials successfully', async () => {
      const createDto = {
        whatsapp_business_id: '123456789',
        phone_number_id: '987654321',
        access_token: 'mock-access-token',
        status: 'Active' as const
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      // Mock existing check (no existing credentials)
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // Not found error
      });

      // Mock insert
      mockFrom.insert().select().single.mockResolvedValue({
        data: mockCredentials,
        error: null
      });

      const result = await service.createMetaCredentials(createDto, req);

      expect(result.status).toBe('success');
      expect(result.data.credentials).toEqual(mockCredentials);
      expect(result.message).toBe(META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_CREATED);
    });

    it('should return duplicate error when credentials already exist', async () => {
      const createDto = {
        whatsapp_business_id: '123456789',
        phone_number_id: '987654321',
        access_token: 'mock-access-token',
        status: 'Active' as const
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      // Mock existing check (credentials exist)
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: { id: 'existing-credentials' },
        error: null
      });

      const result = await service.createMetaCredentials(createDto, req);

      expect(result.status).toBe('error');
      expect(result.code).toBe(META_ONBOARDING_CONSTANTS.HTTP_STATUS.CONFLICT);
      expect(result.message).toBe(META_ONBOARDING_CONSTANTS.ERROR_MESSAGES.DUPLICATE_CREDENTIALS);
    });

    it('should throw BadRequestException when user profile not found', async () => {
      const createDto = {
        whatsapp_business_id: '123456789',
        phone_number_id: '987654321',
        access_token: 'mock-access-token',
        status: 'Active' as const
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: null,
        error: { message: 'Profile not found' }
      });

      await expect(service.createMetaCredentials(createDto, req)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getMetaCredentialsByWorkspace', () => {
    it('should get credentials by workspace successfully', async () => {
      const workspaceId = '1';
      const queryDto = { page: 1, limit: 10 };
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      supabaseService.getWorkspaceMember.mockResolvedValue({
        data: { id: 'member-123' },
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      mockFrom.select().eq().order().range.mockResolvedValue({
        data: [mockCredentials],
        error: null,
        count: 1
      });

      const result = await service.getMetaCredentialsByWorkspace(workspaceId, req, queryDto);

      expect(result.status).toBe('success');
      expect(result.data.credentialsList).toEqual([mockCredentials]);
      expect(result.data.pagination).toBeDefined();
      expect(result.message).toBe(META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.WORKSPACE_CREDENTIALS_FETCHED);
    });

    it('should throw UnauthorizedException when user has no workspace access', async () => {
      const workspaceId = '1';
      const queryDto = { page: 1, limit: 10 };
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      supabaseService.getWorkspaceMember.mockResolvedValue({
        data: null,
        error: { message: 'Access denied' }
      });

      await expect(service.getMetaCredentialsByWorkspace(workspaceId, req, queryDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('getMetaCredentialsByUser', () => {
    it('should get user credentials successfully', async () => {
      const queryDto = { page: 1, limit: 10 };
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      mockFrom.select().eq().order().range.mockResolvedValue({
        data: [mockCredentials],
        error: null,
        count: 1
      });

      const result = await service.getMetaCredentialsByUser(req, queryDto);

      expect(result.status).toBe('success');
      expect(result.data.credentialsList).toEqual([mockCredentials]);
      expect(result.data.pagination).toBeDefined();
      expect(result.message).toBe(META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.USER_CREDENTIALS_FETCHED);
    });
  });

  describe('getMetaCredentialsById', () => {
    it('should get credentials by ID successfully', async () => {
      const credentialsId = 'credentials-123';
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: mockCredentials,
        error: null
      });

      const result = await service.getMetaCredentialsById(credentialsId, req);

      expect(result.status).toBe('success');
      expect(result.data.credentials).toEqual(mockCredentials);
      expect(result.message).toBe(META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_FETCHED);
    });

    it('should throw NotFoundException when credentials not found', async () => {
      const credentialsId = 'non-existent-credentials';
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: null,
        error: { message: 'Not found' }
      });

      await expect(service.getMetaCredentialsById(credentialsId, req)).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateMetaCredentials', () => {
    it('should update credentials successfully', async () => {
      const credentialsId = 'credentials-123';
      const updateDto = {
        whatsapp_business_id: 'updated-business-id',
        status: 'Inactive' as const
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      // Mock current credentials check
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: mockCredentials,
        error: null
      });

      // Mock existing check (no existing credentials with same business ID)
      mockFrom.select().eq().eq().neq().single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // Not found error
      });

      // Mock update
      const updatedCredentials = { ...mockCredentials, ...updateDto };
      mockFrom.update().eq().eq().select().single.mockResolvedValue({
        data: updatedCredentials,
        error: null
      });

      const result = await service.updateMetaCredentials(credentialsId, updateDto, req);

      expect(result.status).toBe('success');
      expect(result.data.credentials).toEqual(updatedCredentials);
      expect(result.message).toBe(META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_UPDATED);
    });
  });

  describe('deleteMetaCredentials', () => {
    it('should delete credentials successfully', async () => {
      const credentialsId = 'credentials-123';
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      mockFrom.delete().eq().eq().select().single.mockResolvedValue({
        data: mockCredentials,
        error: null
      });

      const result = await service.deleteMetaCredentials(credentialsId, req);

      expect(result.status).toBe('success');
      expect(result.message).toBe(META_ONBOARDING_CONSTANTS.SUCCESS_MESSAGES.CREDENTIALS_DELETED);
    });

    it('should throw NotFoundException when credentials not found for deletion', async () => {
      const credentialsId = 'non-existent-credentials';
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from(META_ONBOARDING_CONSTANTS.TABLES.META_CREDENTIALS);
      
      mockFrom.delete().eq().eq().select().single.mockResolvedValue({
        data: null,
        error: { message: 'Not found' }
      });

      await expect(service.deleteMetaCredentials(credentialsId, req)).rejects.toThrow(NotFoundException);
    });
  });
});
