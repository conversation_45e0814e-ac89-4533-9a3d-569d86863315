version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: whatsapp-redis
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --bind 0.0.0.0
      --tcp-keepalive 60
      --timeout 300
      --tcp-backlog 511
      --databases 16
      ${REDIS_PASSWORD:+--requirepass ${REDIS_PASSWORD}}
    volumes:
      - redis-data:/data
    networks:
      - whatsapp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander
    depends_on:
      redis:
        condition: service_healthy
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0${REDIS_PASSWORD:+:${REDIS_PASSWORD}}
      - HTTP_USER=${REDIS_COMMANDER_USER:-admin}
      - HTTP_PASSWORD=${REDIS_COMMANDER_PASSWORD:-admin}
    networks:
      - whatsapp-network
    restart: unless-stopped

volumes:
  redis-data:
    driver: local

networks:
  whatsapp-network:
    driver: bridge
