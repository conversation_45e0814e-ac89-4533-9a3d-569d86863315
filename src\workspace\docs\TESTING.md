# Workspace Module Testing Guide

## Overview

This guide covers testing strategies, examples, and best practices for the Workspace Module. The module includes comprehensive unit tests for both service and controller layers.

## Test Structure

### Test Files
- `workspace.service.spec.ts` - Service layer unit tests
- `workspace.controller.spec.ts` - Controller layer unit tests

### Test Categories
1. **Unit Tests** - Test individual components in isolation
2. **Integration Tests** - Test component interactions
3. **End-to-End Tests** - Test complete request/response cycles

## Running Tests

### Run All Workspace Tests
```bash
npm test -- --testPathPattern="workspace"
```

### Run Specific Test File
```bash
npm test -- workspace.service.spec.ts
npm test -- workspace.controller.spec.ts
```

### Run with Coverage
```bash
npm test -- --testPathPattern="workspace" --coverage
```

## Service Tests (`workspace.service.spec.ts`)

### Test Setup
```typescript
describe('WorkspaceService', () => {
  let service: WorkspaceService;
  let supabaseService: jest.Mocked<SupabaseService>;

  beforeEach(async () => {
    const mockSupabaseService = {
      getUserProfile: jest.fn(),
      getWorkspaceByCreatedBy: jest.fn(),
      insertWorkspace: jest.fn(),
      updateUserProfile: jest.fn(),
      insertWorkspaceMember: jest.fn(),
      createAdminRoleWithPermissions: jest.fn(),
      insertAutomateWhatsappMember: jest.fn(),
      deleteWorkspace: jest.fn(),
      getWorkspaceById: jest.fn(),
      getUserProfileByEmail: jest.fn(),
      signUp: jest.fn(),
      insertUserProfile: jest.fn(),
      getWorkspaceMember: jest.fn(),
      getWorkspaceMembersWithSearch: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceService,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    }).compile();

    service = module.get<WorkspaceService>(WorkspaceService);
    supabaseService = module.get(SupabaseService);
  });
});
```

### Test Cases

#### 1. Workspace Creation Tests
```typescript
describe('create', () => {
  it('should create workspace successfully', async () => {
    // Arrange
    const createWorkspaceDto = {
      name: 'Test Workspace',
      description: 'Test Description',
      industry: 'Technology',
      website: 'https://test.com',
      timezone: 'UTC',
      language: 'en'
    };

    const mockReq = { user: { id: 'user-123', email: '<EMAIL>' } };

    // Mock successful responses
    supabaseService.getUserProfile.mockResolvedValue({ 
      data: { id: 'user-123', workspace_id: null }, 
      error: null 
    });
    supabaseService.getWorkspaceByCreatedBy.mockResolvedValue(null);
    supabaseService.insertWorkspace.mockResolvedValue({ 
      data: { id: 1, name: 'Test Workspace' }, 
      error: null 
    });
    // ... other mocks

    // Act
    const result = await service.create(createWorkspaceDto, mockReq);

    // Assert
    expect(result.status).toBe('success');
    expect(result.code).toBe(201);
    expect(result.message).toBe(WORKSPACE_CONSTANTS.SUCCESS_MESSAGES.WORKSPACE_CREATED);
    expect(result.data.workspace).toBeDefined();
  });

  it('should throw BadRequestException when user already has workspace', async () => {
    // Arrange
    supabaseService.getUserProfile.mockResolvedValue({ 
      data: { id: 'user-123', workspace_id: null }, 
      error: null 
    });
    supabaseService.getWorkspaceByCreatedBy.mockResolvedValue({ id: 1 });

    // Act & Assert
    await expect(service.create(createWorkspaceDto, mockReq))
      .rejects.toThrow(BadRequestException);
  });
});
```

#### 2. Member Addition Tests
```typescript
describe('addMemberToWorkspace', () => {
  it('should add member to workspace successfully', async () => {
    // Arrange
    const createMemberDto = {
      email: '<EMAIL>',
      password: 'Password123',
      first_name: 'John',
      last_name: 'Doe',
      phone: '1234567890',
      country_code: '+1',
      country: 'USA',
      role: 'User' as const,
      waba_access: true,
      role_id: 'role-123'
    };

    // Mock successful responses
    supabaseService.getUserProfile.mockResolvedValue({ 
      data: { id: 'user-123', workspace_id: 1 }, 
      error: null 
    });
    supabaseService.getWorkspaceById.mockResolvedValue({ 
      data: { id: 1, name: 'Test Workspace' }, 
      error: null 
    });
    // ... other mocks

    // Act
    const result = await service.addMemberToWorkspace(createMemberDto, mockReq);

    // Assert
    expect(result.status).toBe('success');
    expect(result.code).toBe(201);
    expect(result.message).toBe(WORKSPACE_CONSTANTS.SUCCESS_MESSAGES.MEMBER_ADDED);
  });
});
```

#### 3. Get Members Tests
```typescript
describe('getWorkspaceMembers', () => {
  it('should get workspace members successfully', async () => {
    // Arrange
    const mockReq = {
      user: { id: 'user-123' },
      query: { search: 'test', page: '1', limit: '10' }
    };

    supabaseService.getUserProfile.mockResolvedValue({ 
      data: { id: 'user-123', workspace_id: 1 }, 
      error: null 
    });
    supabaseService.getWorkspaceMembersWithSearch.mockResolvedValue({ 
      data: [{ id: 1, user_id: 'user-1', role: 'Admin' }], 
      error: null, 
      count: 1 
    });

    // Act
    const result = await service.getWorkspaceMembers(mockReq);

    // Assert
    expect(result.status).toBe('success');
    expect(result.data.members).toHaveLength(1);
    expect(result.data.pagination.totalItems).toBe(1);
  });
});
```

## Controller Tests (`workspace.controller.spec.ts`)

### Test Setup
```typescript
describe('WorkspaceController', () => {
  let controller: WorkspaceController;
  let service: jest.Mocked<WorkspaceService>;

  beforeEach(async () => {
    const mockWorkspaceService = {
      create: jest.fn(),
      addMemberToWorkspace: jest.fn(),
      getWorkspaceMembers: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceController],
      providers: [
        {
          provide: WorkspaceService,
          useValue: mockWorkspaceService,
        },
        {
          provide: AuthGuard,
          useValue: { canActivate: jest.fn().mockReturnValue(true) },
        },
        {
          provide: 'AuthService',
          useValue: { validateToken: jest.fn().mockResolvedValue({ id: 'user-123' }) },
        },
      ],
    }).compile();

    controller = module.get<WorkspaceController>(WorkspaceController);
    service = module.get(WorkspaceService);
  });
});
```

### Test Cases

#### 1. Controller Endpoint Tests
```typescript
describe('create', () => {
  it('should create workspace successfully', async () => {
    // Arrange
    const createWorkspaceDto = {
      name: 'Test Workspace',
      description: 'Test Description'
    };

    const mockReq = { user: { id: 'user-123' } };
    const mockResponse = {
      status: 'success',
      code: 201,
      message: WORKSPACE_CONSTANTS.SUCCESS_MESSAGES.WORKSPACE_CREATED,
      data: { workspace: { id: 1, name: 'Test Workspace' } },
      timestamp: new Date().toISOString()
    };

    service.create.mockResolvedValue(mockResponse);

    // Act
    const result = await controller.create(createWorkspaceDto, mockReq);

    // Assert
    expect(result).toEqual(mockResponse);
    expect(service.create).toHaveBeenCalledWith(createWorkspaceDto, mockReq);
  });

  it('should handle service errors', async () => {
    // Arrange
    const error = new Error('Service error');
    service.create.mockRejectedValue(error);

    // Act & Assert
    await expect(controller.create(createWorkspaceDto, mockReq))
      .rejects.toThrow(error);
  });
});
```

## Test Data and Mocks

### Mock Data
```typescript
const mockUser = {
  id: 'user-123',
  email: '<EMAIL>'
};

const mockUserProfile = {
  id: 'user-123',
  email: '<EMAIL>',
  workspace_id: 1
};

const mockWorkspace = {
  id: 1,
  name: 'Test Workspace',
  created_by: 'user-123',
  status: 'active'
};

const mockWorkspaceMember = {
  id: 1,
  workspace_id: 1,
  user_id: 'user-123',
  role: 'Admin',
  status: 'active'
};
```

### Mock Responses
```typescript
const mockSuccessResponse = {
  status: 'success',
  code: 200,
  message: 'Operation successful',
  data: {},
  timestamp: new Date().toISOString()
};

const mockErrorResponse = {
  status: 'error',
  code: 400,
  message: 'Error message',
  timestamp: new Date().toISOString()
};
```

## Testing Best Practices

### 1. Test Organization
- **Arrange-Act-Assert** pattern
- **Descriptive test names** that explain the scenario
- **Group related tests** using `describe` blocks
- **Clean up** after each test with `afterEach`

### 2. Mocking Strategy
- **Mock external dependencies** (SupabaseService)
- **Mock return values** for different scenarios
- **Verify mock calls** with `toHaveBeenCalledWith`
- **Reset mocks** between tests

### 3. Assertion Patterns
```typescript
// Test success responses
expect(result.status).toBe('success');
expect(result.code).toBe(201);
expect(result.data).toBeDefined();

// Test error responses
await expect(service.method()).rejects.toThrow(BadRequestException);

// Test mock interactions
expect(mockService.method).toHaveBeenCalledWith(expectedArgs);
expect(mockService.method).toHaveBeenCalledTimes(1);
```

### 4. Edge Cases
- **Empty data** scenarios
- **Invalid input** validation
- **Network errors** and timeouts
- **Database errors** and rollbacks
- **Authentication failures**

## Integration Testing

### Database Integration
```typescript
describe('WorkspaceService Integration', () => {
  let service: WorkspaceService;
  let supabaseService: SupabaseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [SupabaseModule],
      providers: [WorkspaceService],
    }).compile();

    service = module.get<WorkspaceService>(WorkspaceService);
    supabaseService = module.get<SupabaseService>(SupabaseService);
  });

  it('should create workspace with real database', async () => {
    // Test with actual database connection
    // Use test database
    // Clean up after test
  });
});
```

### End-to-End Testing
```typescript
describe('Workspace E2E', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/workspace (POST)', () => {
    return request(app.getHttpServer())
      .post('/workspace')
      .set('Authorization', 'Bearer valid-token')
      .send({
        name: 'Test Workspace',
        description: 'Test Description'
      })
      .expect(201)
      .expect((res) => {
        expect(res.body.status).toBe('success');
        expect(res.body.data.workspace).toBeDefined();
      });
  });
});
```

## Performance Testing

### Load Testing
```typescript
describe('Workspace Performance', () => {
  it('should handle multiple concurrent workspace creations', async () => {
    const promises = Array.from({ length: 10 }, (_, i) => 
      service.create({
        name: `Workspace ${i}`,
        description: `Description ${i}`
      }, { user: { id: `user-${i}` } })
    );

    const results = await Promise.all(promises);
    expect(results).toHaveLength(10);
    results.forEach(result => {
      expect(result.status).toBe('success');
    });
  });
});
```

## Test Coverage

### Coverage Goals
- **Service Layer**: 95%+ coverage
- **Controller Layer**: 90%+ coverage
- **Utility Classes**: 100% coverage
- **Error Handling**: 100% coverage

### Coverage Commands
```bash
# Generate coverage report
npm test -- --coverage --testPathPattern="workspace"

# View coverage in browser
npm run test:coverage:open
```

## Debugging Tests

### Debug Mode
```bash
# Run tests in debug mode
npm test -- --testPathPattern="workspace" --verbose

# Run specific test with debug
npm test -- --testNamePattern="should create workspace successfully"
```

### Common Issues
1. **Mock not working** - Check mock setup and return values
2. **Async/await issues** - Ensure proper async handling
3. **Database connection** - Use test database for integration tests
4. **Authentication** - Mock AuthGuard properly

## Continuous Integration

### GitHub Actions
```yaml
name: Workspace Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test -- --testPathPattern="workspace" --coverage
```


