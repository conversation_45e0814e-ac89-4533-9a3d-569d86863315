import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException, ConflictException } from '@nestjs/common';
import { RolePermissionService } from '../../role-permission.service';
import { SupabaseService } from '../../../supabase/supabase.service';
import { RolePermissionValidationUtil } from '../../utils/role-permission-validation.util';
import { RolePermissionResponseUtil } from '../../utils/role-permission-response.util';
import { ROLE_PERMISSION_CONSTANTS } from '../../utils/role-permission-constants.util';

describe('RolePermissionService', () => {
  let service: RolePermissionService;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>'
  };

  const mockUserProfile = {
    workspace_id: 1,
    id: 'user-123'
  };

  const mockRole = {
    id: 'role-123',
    name: 'Admin',
    description: 'Administrator role',
    status: 'active',
    workspace_id: 1,
    created_by: 'user-123',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  };

  const mockPermission = {
    id: 'permission-123',
    name: 'manage_users',
    resource: 'users',
    action: 'manage',
    description: 'Manage user accounts',
    status: 'active',
    workspace_id: 1,
    created_by: 'user-123',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  };

  beforeEach(async () => {
    const mockSupabaseService = {
      getUserProfile: jest.fn(),
      getClient: jest.fn().mockReturnValue({
        from: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn(),
              neq: jest.fn().mockReturnValue({
                single: jest.fn()
              })
            }),
            neq: jest.fn().mockReturnValue({
              single: jest.fn()
            })
          }),
          insert: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn()
            })
          }),
          update: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn()
              })
            })
          }),
          delete: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn()
              })
            })
          }),
          order: jest.fn().mockReturnValue({
            range: jest.fn()
          }),
          range: jest.fn()
        })
      }),
      getWorkspaceMember: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolePermissionService,
        {
          provide: SupabaseService,
          useValue: mockSupabaseService
        }
      ],
    }).compile();

    service = module.get<RolePermissionService>(RolePermissionService);
    supabaseService = module.get(SupabaseService);
  });

  describe('createRole', () => {
    it('should create a role successfully', async () => {
      const createDto = {
        name: 'Admin',
        description: 'Administrator role',
        status: 'active' as const
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from('roles');
      
      // Mock existing check (no existing role)
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // Not found error
      });

      // Mock insert
      mockFrom.insert().select().single.mockResolvedValue({
        data: mockRole,
        error: null
      });

      const result = await service.createRole(createDto, req);

      expect(result.status).toBe('success');
      expect(result.data.role).toEqual(mockRole);
      expect(result.message).toBe(ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLE_CREATED);
    });

    it('should return duplicate error when role name already exists', async () => {
      const createDto = {
        name: 'Admin',
        description: 'Administrator role',
        status: 'active' as const
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from('roles');
      
      // Mock existing check (role exists)
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: { id: 'existing-role' },
        error: null
      });

      const result = await service.createRole(createDto, req);

      expect(result.status).toBe('error');
      expect(result.code).toBe(ROLE_PERMISSION_CONSTANTS.HTTP_STATUS.CONFLICT);
      expect(result.message).toBe(ROLE_PERMISSION_CONSTANTS.ERROR_MESSAGES.DUPLICATE_ROLE_NAME);
    });

    it('should throw BadRequestException when user profile not found', async () => {
      const createDto = {
        name: 'Admin',
        description: 'Administrator role',
        status: 'active' as const
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: null,
        error: { message: 'Profile not found' }
      });

      await expect(service.createRole(createDto, req)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getRolesForWorkspace', () => {
    it('should get roles for workspace successfully', async () => {
      const queryDto = { page: 1, limit: 10 };
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from('roles');
      
      mockFrom.select().eq().order().range.mockResolvedValue({
        data: [mockRole],
        error: null,
        count: 1
      });

      const result = await service.getRolesForWorkspace(req, queryDto);

      expect(result.status).toBe('success');
      expect(result.data.roles).toEqual([mockRole]);
      expect(result.data.pagination).toBeDefined();
      expect(result.message).toBe(ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLES_FETCHED);
    });
  });

  describe('getRoleById', () => {
    it('should get role by ID successfully', async () => {
      const roleId = 'role-123';
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from('roles');
      
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: mockRole,
        error: null
      });

      const result = await service.getRoleById(roleId, req);

      expect(result.status).toBe('success');
      expect(result.data.role).toEqual(mockRole);
      expect(result.message).toBe(ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.ROLE_FETCHED);
    });

    it('should throw NotFoundException when role not found', async () => {
      const roleId = 'non-existent-role';
      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from('roles');
      
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: null,
        error: { message: 'Not found' }
      });

      await expect(service.getRoleById(roleId, req)).rejects.toThrow(NotFoundException);
    });
  });

  describe('createPermission', () => {
    it('should create a permission successfully', async () => {
      const createDto = {
        name: 'manage_users',
        resource: 'users',
        action: 'manage',
        description: 'Manage user accounts',
        status: 'active' as const
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from('permissions');
      
      // Mock existing check (no existing permission)
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // Not found error
      });

      // Mock insert
      mockFrom.insert().select().single.mockResolvedValue({
        data: mockPermission,
        error: null
      });

      const result = await service.createPermission(createDto, req);

      expect(result.status).toBe('success');
      expect(result.data.permission).toEqual(mockPermission);
      expect(result.message).toBe(ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSION_CREATED);
    });
  });

  describe('assignPermissionToRole', () => {
    it('should assign permission to role successfully', async () => {
      const assignDto = {
        roleId: 'role-123',
        permissionId: 'permission-123'
      };

      const req = { user: mockUser };

      supabaseService.getUserProfile.mockResolvedValue({
        data: mockUserProfile,
        error: null
      });

      const mockClient = supabaseService.getClient();
      const mockFrom = mockClient.from('roles');
      
      // Mock role exists check
      mockFrom.select().eq().eq().single.mockResolvedValue({
        data: { id: 'role-123' },
        error: null
      });

      // Mock permission exists check
      const mockPermissionFrom = mockClient.from('permissions');
      mockPermissionFrom.select().eq().eq().single.mockResolvedValue({
        data: { id: 'permission-123' },
        error: null
      });

      // Mock existing assignment check (no existing assignment)
      const mockRolePermissionsFrom = mockClient.from('role_permissions');
      mockRolePermissionsFrom.select().eq().eq().single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // Not found error
      });

      // Mock insert assignment
      mockRolePermissionsFrom.insert().select().single.mockResolvedValue({
        data: { id: 'assignment-123' },
        error: null
      });

      const result = await service.assignPermissionToRole(assignDto, req);

      expect(result.status).toBe('success');
      expect(result.message).toBe(ROLE_PERMISSION_CONSTANTS.SUCCESS_MESSAGES.PERMISSION_ASSIGNED);
    });
  });
});
