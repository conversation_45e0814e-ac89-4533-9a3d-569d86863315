# Automate WhatsApp Backend

A NestJS-based backend service for WhatsApp automation with Supabase integration.

## 🚀 Features

- **NestJS Framework**: Modern, scalable Node.js framework
- **Supabase Integration**: Database and authentication services
- **MongoDB Support**: Flexible document database
- **Authentication**: JWT-based authentication with guards
- **Workspace Management**: Multi-tenant workspace support
- **Docker Ready**: Containerized deployment

## 📋 Prerequisites

- Node.js 20+ 
- Docker & Docker Compose
- Supabase account and project
- MongoDB (optional, if using local database)

## 🛠️ Installation

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd automate-whatsapp-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env

# Automate WhatsApp Backend

A NestJS backend for WhatsApp automation, featuring Supabase integration and MongoDB support.

## 🚀 Features

- NestJS framework for scalable Node.js development
- Supabase integration for authentication and database
- MongoDB support
- JWT-based authentication
- Multi-tenant workspace management
- Docker-ready for easy deployment

## � Project Structure

```
src/
├── app.controller.ts         # Main application controller
├── app.module.ts             # Root module
├── app.service.ts            # Main application service
├── main.ts                   # Application entry point
├── auth/                     # Authentication module
│   ├── auth.controller.ts
│   ├── auth.guard.ts
│   ├── auth.module.ts
│   └── auth.service.ts
├── database/                 # Database configuration
│   ├── database.module.ts
│   ├── database.service.spec.ts
│   └── database.service.ts
├── dto/                      # Data Transfer Objects
│   ├── auth.dto.ts
│   ├── create-user.dto.ts
│   ├── create-workspace.dto.ts
│   └── update-user.dto.ts
├── schema/                   # Database schemas
│   └── user.schema.ts
├── supabase/                 # Supabase integration
│   ├── supabase.module.ts
│   └── supabase.service.ts
├── workspace/                # Workspace management
│   ├── workspace.controller.ts
│   ├── workspace.module.ts
│   └── workspace.service.ts
```

## 🛠️ Prerequisites

- Node.js 18+
- Docker & Docker Compose
- Supabase account/project
- MongoDB (Atlas or local)

## ⚙️ Environment Setup

1. Copy `.env.example` to `.env` and fill in your values:
   ```
   PORT=8000
   NODE_ENV=production
   MONGODB_URI=your_mongodb_uri
   MONGODB_DATABASE_NAME=your_db_name
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

## 🐳 Docker Compose Deployment

1. Build and start the backend:
   ```powershell
   docker-compose build
   docker-compose up -d
   ```

2. Access the API at `http://localhost:8000` (or your server IP).

3. View logs:
   ```powershell
   docker-compose logs -f app
   ```

4. Stop services:
   ```powershell
   docker-compose down
   ```

## 🔧 Manual Docker Build

If you prefer manual Docker commands:
```powershell
docker build -t whatsapp-backend .
docker run -p 8000:8000 --env-file .env whatsapp-backend
```

## 🔍 API Endpoints

### Authentication
- `POST /auth/login` — User login
- `POST /auth/register` — User registration
- `GET /auth/profile` — Get user profile

### Workspace
- `POST /workspace` — Create workspace
- `GET /workspace` — Get workspaces
- `PUT /workspace/:id` — Update workspace
- `DELETE /workspace/:id` — Delete workspace

## 🧪 Testing

Run tests with:
```powershell
npm run test         # Unit tests
npm run test:e2e     # End-to-end tests
npm run test:cov     # Coverage
```

## 🚀 Production Deployment

1. Ensure `.env` has `NODE_ENV=production` and correct values.
2. Build and start with Docker Compose:
   ```powershell
   docker-compose build
   docker-compose up -d
   ```
3. Use a reverse proxy (Nginx) for SSL and routing if needed.

## � Logs

- View Docker logs:
  ```powershell
  docker-compose logs -f app
  docker logs whatsapp-backend
  ```

## 🤝 Contributing

1. Fork the repo
2. Create a feature branch
3. Make changes and add tests
4. Submit a pull request

## 📄 License



## 🆘 Support

- Create an issue in the repository
- Check documentation
- Review logs for debugging
