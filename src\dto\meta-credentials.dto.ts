import { <PERSON>E<PERSON>, <PERSON><PERSON>otEmpty, IsN<PERSON>berString, <PERSON><PERSON><PERSON>al, IsString } from "class-validator";

export class CreateMetaCredentialsDto {
    @IsNumberString({}, { message: 'WhatsApp Business ID must be a number  string' })
    @IsNotEmpty({ message: 'WhatsApp Business ID is required' })
    whatsapp_business_id: string;

    @IsNumberString({}, { message: 'Phone number ID must be a number string' })
    @IsNotEmpty({ message: 'Phone number ID is required' })
    phone_number_id: string;

    @IsString({ message: 'Access token must be a string' })
    @IsNotEmpty({ message: 'Access token is required' })
    access_token: string;

    @IsEnum(['Active', 'Inactive'], { message: 'Status must be either Active or Inactive' })
    @IsOptional()
    status?: 'Active' | 'Inactive';
}
//     @IsString({ message: 'Channel name must be a string' })
//     @IsOptional()
//     channel_name?: string;

//     @IsString({ message: 'WhatsApp Business ID must be a string' })
//     @IsOptional()
//     whatsapp_business_id?: string;

//     @IsNumberString({}, { message: 'Phone number ID must be a number string' })
//     @IsOptional()
//     phone_number_id?: string;

//     @IsString({ message: 'Access token must be a string' })
//     @IsOptional()
//     access_token?: string;

//     @IsEnum(['Active', 'Inactive'], { message: 'Status must be either Active or Inactive' })
//     @IsOptional()
//     status?: 'Active' | 'Inactive';
// } 