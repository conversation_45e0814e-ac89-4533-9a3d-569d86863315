import { BadRequestException, NotFoundException } from '@nestjs/common';
import { CustomFieldTypeEnum } from '../../schema/custom-field.schema';
import { CUSTOM_FIELDS_CONSTANTS } from './custom-fields-constants.util';

/**
 * Utility class for custom fields-related validations
 */
export class CustomFieldsValidationUtil {
  /**
   * Validates custom field label
   */
  static validateLabel(label: string): void {
    if (!label || typeof label !== 'string') {
      throw new BadRequestException('Label is required and must be a string');
    }

    if (label.length < CUSTOM_FIELDS_CONSTANTS.VALIDATION.LABEL_MIN_LENGTH) {
      throw new BadRequestException('Label must be at least 1 character long');
    }

    if (label.length > CUSTOM_FIELDS_CONSTANTS.VALIDATION.LABEL_MAX_LENGTH) {
      throw new BadRequestException('Label must not exceed 100 characters');
    }

    // Check for valid characters (alphanumeric, spaces, hyphens, underscores)
    const labelRegex = /^[a-zA-Z0-9\s\-_]+$/;
    if (!labelRegex.test(label)) {
      throw new BadRequestException('Label can only contain letters, numbers, spaces, hyphens, and underscores');
    }
  }

  /**
   * Validates custom field type
   */
  static validateType(type: string): CustomFieldTypeEnum {
    if (!type || typeof type !== 'string') {
      throw new BadRequestException('Type is required and must be a string');
    }

    const validTypes = Object.values(CustomFieldTypeEnum);
    if (!validTypes.includes(type as CustomFieldTypeEnum)) {
      throw new BadRequestException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.INVALID_CUSTOM_FIELD_TYPE);
    }

    return type as CustomFieldTypeEnum;
  }

  /**
   * Validates options array for dropdown fields
   */
  static validateOptions(options: string[] | undefined, type: CustomFieldTypeEnum): string[] | undefined {
    if (type === CustomFieldTypeEnum.DROPDOWN) {
      if (!options || !Array.isArray(options)) {
        throw new BadRequestException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.MISSING_OPTIONS_FOR_DROPDOWN);
      }

      if (options.length === 0) {
        throw new BadRequestException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.INVALID_OPTIONS_ARRAY);
      }

      if (options.length > CUSTOM_FIELDS_CONSTANTS.VALIDATION.MAX_OPTIONS_PER_FIELD) {
        throw new BadRequestException(`Maximum ${CUSTOM_FIELDS_CONSTANTS.VALIDATION.MAX_OPTIONS_PER_FIELD} options allowed per field`);
      }

      // Validate each option
      options.forEach((option, index) => {
        if (typeof option !== 'string') {
          throw new BadRequestException(`Option at index ${index} must be a string`);
        }

        if (option.length < CUSTOM_FIELDS_CONSTANTS.VALIDATION.OPTIONS_MIN_LENGTH) {
          throw new BadRequestException(`Option at index ${index} must be at least 1 character long`);
        }

        if (option.length > CUSTOM_FIELDS_CONSTANTS.VALIDATION.OPTIONS_MAX_LENGTH) {
          throw new BadRequestException(`Option at index ${index} must not exceed 50 characters`);
        }
      });

      // Check for duplicate options
      const uniqueOptions = new Set(options);
      if (uniqueOptions.size !== options.length) {
        throw new BadRequestException('Duplicate options are not allowed');
      }

      return options;
    } else {
      // For non-dropdown types, options should not be provided
      if (options && options.length > 0) {
        throw new BadRequestException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.INVALID_OPTIONS_FOR_TYPE);
      }
      return undefined;
    }
  }

  /**
   * Validates boolean values
   */
  static validateBoolean(value: any, fieldName: string): boolean {
    if (typeof value === 'boolean') {
      return value;
    }

    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true') return true;
      if (lowerValue === 'false') return false;
    }

    throw new BadRequestException(`${fieldName} must be a boolean value`);
  }

  /**
   * Validates user context from request
   */
  static validateUserContext(req: any): any {
    const user = req.user;
    if (!user || !user.id) {
      throw new BadRequestException('User context not found in request');
    }
    return user;
  }

  /**
   * Validates user profile and extracts workspace ID
   */
  static validateUserProfile(userProfile: any, userProfileError: any): number {
    if (userProfileError) {
      throw new BadRequestException('Failed to fetch user profile');
    }

    if (!userProfile) {
      throw new BadRequestException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.USER_WORKSPACE_NOT_FOUND);
    }

    if (!userProfile.workspace_id) {
      throw new BadRequestException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.USER_WORKSPACE_NOT_FOUND);
    }

    return userProfile.workspace_id;
  }

  /**
   * Validates custom field exists and belongs to workspace
   */
  static validateCustomFieldExists(customField: any, customFieldId: string): void {
    if (!customField) {
      throw new NotFoundException(CUSTOM_FIELDS_CONSTANTS.ERROR_MESSAGES.CUSTOM_FIELD_NOT_FOUND);
    }
  }

  /**
   * Validates custom field creation data
   */
  static validateCustomFieldCreationData(dto: any, userId: string, workspaceId: number): any {
    // Validate label
    this.validateLabel(dto.label);

    // Validate type
    const validatedType = this.validateType(dto.type);

    // Validate options based on type
    const validatedOptions = this.validateOptions(dto.options, validatedType);

    // Validate boolean fields
    const showOnContact = dto.showOnContact !== undefined 
      ? this.validateBoolean(dto.showOnContact, 'showOnContact')
      : CUSTOM_FIELDS_CONSTANTS.DEFAULTS.SHOW_ON_CONTACT;

    const showOnChat = dto.showOnChat !== undefined
      ? this.validateBoolean(dto.showOnChat, 'showOnChat')
      : CUSTOM_FIELDS_CONSTANTS.DEFAULTS.SHOW_ON_CHAT;

    return {
      label: dto.label.trim(),
      type: validatedType,
      options: validatedOptions,
      showOnContact,
      showOnChat,
      workspaceId,
      createdBy: userId,
    };
  }

  /**
   * Validates custom field update data
   */
  static validateCustomFieldUpdateData(dto: any): any {
    const updateData: any = {};

    if (dto.label !== undefined) {
      this.validateLabel(dto.label);
      updateData.label = dto.label.trim();
    }

    if (dto.type !== undefined) {
      const validatedType = this.validateType(dto.type);
      updateData.type = validatedType;

      // Re-validate options if type is being changed
      if (dto.options !== undefined) {
        updateData.options = this.validateOptions(dto.options, validatedType);
      }
    } else if (dto.options !== undefined) {
      // If type is not being updated, we need to get the current type
      // This will be handled in the service
      updateData.options = dto.options;
    }

    if (dto.showOnContact !== undefined) {
      updateData.showOnContact = this.validateBoolean(dto.showOnContact, 'showOnContact');
    }

    if (dto.showOnChat !== undefined) {
      updateData.showOnChat = this.validateBoolean(dto.showOnChat, 'showOnChat');
    }

    return updateData;
  }
}
