import { HttpStatus } from '@nestjs/common';

export interface StandardRolePermissionResponse {
  status: 'success' | 'error';
  code: number;
  message: string;
  data?: any;
  error?: any;
  timestamp: string;
}

export interface RolePermissionResponseData {
  role?: any;
  roles?: any[];
  permission?: any;
  permissions?: any[];
  rolePermissions?: any[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class RolePermissionResponseUtil {
  static createSuccessResponse(
    data: RolePermissionResponseData | any,
    message: string = 'Operation completed successfully',
    code: number = HttpStatus.OK,
  ): StandardRolePermissionResponse {
    return {
      status: 'success',
      code,
      message,
      data,
      timestamp: new Date().toISOString(),
    };
  }

  static createErrorResponse(
    message: string,
    code: number = HttpStatus.BAD_REQUEST,
    error?: any,
  ): StandardRolePermissionResponse {
    return {
      status: 'error',
      code,
      message,
      error: error?.message || error,
      timestamp: new Date().toISOString(),
    };
  }

  static createDuplicateErrorResponse(message: string): StandardRolePermissionResponse {
    return RolePermissionResponseUtil.createErrorResponse(message, HttpStatus.CONFLICT);
  }

  static createValidationErrorResponse(message: string, details?: any): StandardRolePermissionResponse {
    return RolePermissionResponseUtil.createErrorResponse(message, HttpStatus.BAD_REQUEST, details);
  }

  static createNotFoundErrorResponse(message: string): StandardRolePermissionResponse {
    return RolePermissionResponseUtil.createErrorResponse(message, HttpStatus.NOT_FOUND);
  }

  static createUnauthorizedErrorResponse(message: string): StandardRolePermissionResponse {
    return RolePermissionResponseUtil.createErrorResponse(message, HttpStatus.UNAUTHORIZED);
  }

  static createForbiddenErrorResponse(message: string): StandardRolePermissionResponse {
    return RolePermissionResponseUtil.createErrorResponse(message, HttpStatus.FORBIDDEN);
  }

  // Role-specific response data creators
  static createRoleCreationData(role: any): RolePermissionResponseData {
    return { role };
  }

  static createRolesListData(roles: any[], pagination?: any): RolePermissionResponseData {
    return { roles, pagination };
  }

  // Permission-specific response data creators
  static createPermissionCreationData(permission: any): RolePermissionResponseData {
    return { permission };
  }

  static createPermissionsListData(permissions: any[], pagination?: any): RolePermissionResponseData {
    return { permissions, pagination };
  }

  // Role-Permission assignment response data creators
  static createRolePermissionsData(rolePermissions: any[]): RolePermissionResponseData {
    return { rolePermissions };
  }

  // Pagination metadata creator
  static createPaginationMetadata(page: number, limit: number, total: number) {
    const totalPages = Math.ceil(total / limit);
    return { page, limit, total, totalPages };
  }
}
