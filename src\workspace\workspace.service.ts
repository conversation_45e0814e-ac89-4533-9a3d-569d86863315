import { Injectable, Logger, NotFoundException, BadRequestException, InternalServerErrorException, ConflictException } from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';
import { CreateWorkspaceDto, CreateMemberDto, WorkspaceMembersQueryDto } from './dto';
import { WorkspaceResponseUtil, WorkspaceResponseData } from './utils/workspace-response.util';
import { WorkspaceValidationUtil } from './utils/workspace-validation.util';
import { WORKSPACE_CONSTANTS } from './utils/workspace-constants.util';

@Injectable()
export class WorkspaceService {
  private readonly logger = new Logger(WorkspaceService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
  ) {}

  // ==================== PUBLIC METHODS ====================

  /**
   * Create workspace
   */
  async create(createWorkspaceDto: CreateWorkspaceDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting workspace creation process');

      const user = WorkspaceValidationUtil.validateUserContext(req);

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      WorkspaceValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Check if user already has a workspace
      const existingWorkspace = await this.supabaseService.getWorkspaceByCreatedBy(user.id);
      WorkspaceValidationUtil.validateUserHasNoWorkspace(existingWorkspace);

      // Prepare workspace data
      const workspaceData = WorkspaceValidationUtil.validateWorkspaceCreationData(createWorkspaceDto, user.id);
      
      // Create workspace
      const result = await this.supabaseService.insertWorkspace(workspaceData);
      if (result.error) {
        this.logger.error('Failed to create workspace:', result.error);
        throw new BadRequestException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.WORKSPACE_CREATION_FAILED);
      }

      // Update user profile with workspace ID
      const updateUserProfile = await this.supabaseService.updateUserProfile(user.id, {
        workspace_id: result.data.id,
        updated_at: new Date().toISOString()
      });

      if (updateUserProfile.error) {
        this.logger.error('Failed to update user profile:', updateUserProfile.error);
        await this.handleWorkspaceCreationRollback(result.data.id, user.id);
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.ROLLBACK_FAILED);
      }

      // Add user as workspace member (owner/admin role)
      const insertWorkspaceMember = await this.supabaseService.insertWorkspaceMember({
        workspace_id: result.data.id,
        user_id: user.id,
        role: WORKSPACE_CONSTANTS.DEFAULTS.ROLE,
        status: WORKSPACE_CONSTANTS.MEMBER_STATUS.ACTIVE,
        waba_access: true
      });

      if (insertWorkspaceMember.error) {
        this.logger.error('Failed to add user as workspace member:', insertWorkspaceMember.error);
        await this.handleWorkspaceCreationRollback(result.data.id, user.id);
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.MEMBER_ROLLBACK_FAILED);
      }

      // Create admin role with permissions for this workspace
      const createAdminRole = await this.supabaseService.createAdminRoleWithPermissions(result.data.id);
      if (createAdminRole.error) {
        this.logger.error('Failed to create admin role:', createAdminRole.error);
        await this.handleWorkspaceCreationRollback(result.data.id, user.id);
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.ROLE_ROLLBACK_FAILED);
      }

      // Insert automate whatsapp member with the created role
      const insertAutomateMember = await this.supabaseService.insertAutomateWhatsappMember({
        workspace_member_id: insertWorkspaceMember.data.id,
        role_id: Array.isArray(createAdminRole.data) ? createAdminRole.data[0]?.role_id : createAdminRole.data?.role_id,
        user_id: user.id,
        workspace_id: result.data.id,
        status: WORKSPACE_CONSTANTS.MEMBER_STATUS.ACTIVE
      });

      if (insertAutomateMember.error) {
        this.logger.error('Failed to add automate whatsapp member:', insertAutomateMember.error);
        await this.handleWorkspaceCreationRollback(result.data.id, user.id);
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.AUTOMATE_ROLLBACK_FAILED);
      }

      this.logger.log(`Workspace created successfully: ${result.data.id}`);

      const responseData = WorkspaceResponseUtil.createWorkspaceCreationData(
        result.data,
        updateUserProfile.data,
        insertWorkspaceMember.data,
        createAdminRole.data,
        insertAutomateMember.data,
        user.id
      );

      return WorkspaceResponseUtil.createSuccessResponse(
        responseData,
        WORKSPACE_CONSTANTS.SUCCESS_MESSAGES.WORKSPACE_CREATED,
        WORKSPACE_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Create workspace failed:', error);
      this.handleWorkspaceError(error);
    }
  }

  /**
   * Add member to workspace
   */
  async addMemberToWorkspace(memberData: CreateMemberDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting add member to workspace process');

      const user = WorkspaceValidationUtil.validateUserContext(req);

      // Validate that the requesting user has a profile and get their workspace
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      WorkspaceValidationUtil.validateUserProfile(userProfile, userProfileError);
      WorkspaceValidationUtil.validateUserWorkspaceAssociation(userProfile.workspace_id);

      // Validate that the workspace exists
      const { data: workspace, error: workspaceError } = await this.supabaseService.getWorkspaceById(userProfile.workspace_id);
      WorkspaceValidationUtil.validateWorkspaceExists(workspace, workspaceError);

      // Check if email already exists
      const existingUserByEmail = await this.supabaseService.getUserProfileByEmail(memberData.email);
      WorkspaceValidationUtil.validateUserDoesNotExist(existingUserByEmail);

      // Create new user in Supabase Auth
      const signUpResult = await this.supabaseService.signUp(
        memberData.email,
        memberData.password,
        {
          first_name: memberData.first_name,
          last_name: memberData.last_name,
          phoneNumber: memberData.phone,
          countrycode: memberData.country_code,
          country: memberData.country,
        }
      );

      if (signUpResult.error) {
        this.logger.error('Failed to create user in auth:', signUpResult.error);
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.USER_ACCOUNT_CREATION_FAILED);
      }

      const targetUserId = signUpResult.data.user?.id;
      if (!targetUserId) {
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.USER_ID_NOT_FOUND);
      }

      // Insert into user_profile
      const profileData = WorkspaceValidationUtil.validateUserProfileCreationData(memberData, targetUserId, userProfile.workspace_id);
      const { data: newProfile, error: profileError } = await this.supabaseService.insertUserProfile(profileData);
      
      if (profileError) {
        this.logger.error('Failed to create user profile:', profileError);
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_CREATION_FAILED);
      }

      // Check if the target user is already a member of this workspace
      const { data: existingMember, error: existingMemberError } = await this.supabaseService.getWorkspaceMember(
        userProfile.workspace_id, 
        targetUserId
      );
      WorkspaceValidationUtil.validateMemberDoesNotExist(existingMember, existingMemberError);

      // Insert into workspace_members
      const insertMemberData = WorkspaceValidationUtil.validateMemberCreationData(memberData, userProfile.workspace_id, targetUserId);
      const { data: newMember, error: insertError } = await this.supabaseService.insertWorkspaceMember(insertMemberData);
      
      if (insertError) {
        this.logger.error('Failed to add workspace member:', insertError);
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.WORKSPACE_MEMBER_CREATION_FAILED);
      }

      // Insert into automate_whatsapp_members if waba_access is true
      let automateMemberResult: { data: any; error: any } | null = null;
      
      if (memberData.waba_access) {
        WorkspaceValidationUtil.validateRoleIdForWabaAccess(memberData.waba_access, memberData.role_id);

        try {
          automateMemberResult = await this.supabaseService.insertAutomateWhatsappMember({
            workspace_member_id: newMember.id,
            role_id: memberData.role_id!,
            user_id: targetUserId,
            workspace_id: userProfile.workspace_id,
            status: WORKSPACE_CONSTANTS.MEMBER_STATUS.ACTIVE
          });
          
          if (automateMemberResult.error) {
            this.logger.error('Failed to add to automate whatsapp members:', automateMemberResult.error);
            throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.AUTOMATE_MEMBER_CREATION_FAILED);
          }
        } catch (error) {
          this.logger.error('Error in waba access setup:', error);
          throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.WABA_ACCESS_SETUP_FAILED);
        }
      }

      this.logger.log(`Member added to workspace successfully: ${targetUserId}`);

      const responseData = WorkspaceResponseUtil.createMemberAdditionData(
        signUpResult.data.user,
        newProfile,
        newMember,
        automateMemberResult?.data
      );

      return WorkspaceResponseUtil.createSuccessResponse(
        responseData,
        WORKSPACE_CONSTANTS.SUCCESS_MESSAGES.MEMBER_ADDED,
        WORKSPACE_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Add member to workspace failed:', error);
      this.handleWorkspaceError(error);
    }
  }

  /**
   * Get workspace members
   */
  async getWorkspaceMembers(req: any): Promise<any> {
    try {
      this.logger.log('Starting get workspace members process');

      const user = WorkspaceValidationUtil.validateUserContext(req);

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      WorkspaceValidationUtil.validateUserProfile(userProfile, userProfileError);
      WorkspaceValidationUtil.validateUserWorkspaceAssociation(userProfile.workspace_id);

      // Extract and validate query parameters
      const { search = '', page, limit, sortBy, sortOrder } = req.query;
      const { page: pageNum, limit: limitNum } = WorkspaceValidationUtil.validatePaginationParams(page, limit);
      const { sortBy: sortByField, sortOrder: sortOrderField } = WorkspaceValidationUtil.validateSortParams(sortBy, sortOrder);
      
      const offset = (pageNum - 1) * limitNum;

      // Get workspace members with search and pagination
      const { data: workspaceMembers, error: workspaceMembersError, count } = await this.supabaseService.getWorkspaceMembersWithSearch(
        userProfile.workspace_id,
        search as string,
        limitNum,
        offset,
        sortByField,
        sortOrderField
      );
      
      if (workspaceMembersError) {
        this.logger.error('Failed to fetch workspace members:', workspaceMembersError);
        throw new InternalServerErrorException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.MEMBERS_FETCH_FAILED);
      }

      // Create pagination metadata
      const pagination = WorkspaceResponseUtil.createPaginationMetadata(pageNum, limitNum, count || 0);
      
      // Create filters metadata
      const filters = {
        search: search as string,
        sortBy: sortByField,
        sortOrder: sortOrderField
      };

      this.logger.log(`Workspace members fetched successfully: ${count || 0} members`);

      const responseData = WorkspaceResponseUtil.createMembersResponseData(
        workspaceMembers || [],
        pagination,
        filters
      );

      return WorkspaceResponseUtil.createSuccessResponse(
        responseData,
        WORKSPACE_CONSTANTS.SUCCESS_MESSAGES.MEMBERS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get workspace members failed:', error);
      this.handleWorkspaceError(error);
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Handles workspace creation rollback
   */
  private async handleWorkspaceCreationRollback(workspaceId: string, userId: string): Promise<void> {
    try {
      // Delete workspace
      await this.supabaseService.deleteWorkspace(parseInt(workspaceId));
      
      // Revert user profile
      await this.supabaseService.updateUserProfile(userId, {
        workspace_id: null,
        updated_at: new Date().toISOString()
      });
      
      this.logger.log(`Workspace creation rollback completed for workspace: ${workspaceId}`);
    } catch (rollbackError) {
      this.logger.error('Workspace creation rollback failed:', rollbackError);
      // Log critical error but don't throw to avoid masking original error
    }
  }

  /**
   * Handles workspace-related errors
   */
  private handleWorkspaceError(error: any): void {
    if (error instanceof BadRequestException || 
        error instanceof ConflictException || 
        error instanceof InternalServerErrorException) {
      throw error;
    }
    
    this.logger.error('Unexpected workspace error:', error);
    throw new InternalServerErrorException('Internal server error');
  }
}