import { 
  <PERSON>, 
  Post, 
  Get, 
  Body, 
  Headers, 
  HttpCode, 
  HttpStatus, 
  UseGuards, 
  Request,
  BadRequestException,
  UseInterceptors,
  UploadedFile,
  ParseFilePipe,
  MaxFileSizeValidator,
  Logger
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { MetaMediaUploadService } from './meta-media-upload.service';
import { AuthGuard } from '../auth/auth.guard';
import { SupabaseService } from '../supabase/supabase.service';
import { 
  StartUploadSessionDto, 
  UploadFileDto, 
  ResumeUploadDto 
} from './dto';
import { UploadResponseUtil } from './utils/upload-response.util';
import { UPLOAD_CONSTANTS } from './utils/upload-constants.util';

/**
 * Controller for Meta Media Upload operations
 * 
 * Provides endpoints for the Meta Resumable Upload API:
 * 1. Start upload session
 * 2. Upload file data
 * 3. Resume interrupted upload
 */
@Controller('meta-media-upload')
@UseGuards(AuthGuard)
export class MetaMediaUploadController {
  constructor(
    private readonly metaMediaUploadService: MetaMediaUploadService,
    private readonly supabaseService: SupabaseService
  ) {}

  /**
   * Step 1: Start an upload session
   * POST /meta-media-upload/start-session
   */
  @Post('start-session')
  @HttpCode(HttpStatus.CREATED)
  async startUploadSession(
    @Body() startUploadSessionDto: StartUploadSessionDto,
    @Request() req: any
  ) {
    // Validate required headers

    // Extract user information from authenticated request
    const user = this.extractUserFromRequest(req);

    // Get Meta access token from user's stored credentials
    const metaAccessToken = await this.getMetaAccessTokenFromUser(user);

    this.logger.log(`User ${user.id} starting upload session for file: ${startUploadSessionDto.fileName}`);

    const result = await this.metaMediaUploadService.startUploadSession(
      '1470230977691891',
      metaAccessToken,
      startUploadSessionDto
    );

    return result;
  }

  /**
   * Step 2: Upload file data
   * POST /meta-media-upload/upload
   */
  @Post('upload')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: UPLOAD_CONSTANTS.MAX_FILE_SIZE }),
        ],
      }),
    ) file: any,
    @Body() uploadFileDto: UploadFileDto,
    @Request() req: any
  ) {
    // Validate file upload
    this.validateFileUpload(file);

    // Extract user information from authenticated request
    const user = this.extractUserFromRequest(req);

    // Get Meta access token from user's stored credentials
    const metaAccessToken = await this.getMetaAccessTokenFromUser(user);

    this.logger.log(`User ${user.id} uploading file to session: ${uploadFileDto.uploadSessionId}`);

    const result = await this.metaMediaUploadService.uploadFile(
      uploadFileDto.uploadSessionId,
      metaAccessToken,
      file.buffer,
      uploadFileDto.fileOffset || 0
    );

    return result;
  }

  /**
   * Resume an interrupted upload session
   * GET /meta-media-upload/resume/:uploadSessionId
   */
  @Get('resume/:uploadSessionId')
  @HttpCode(HttpStatus.OK)
  async resumeUpload(
    @Body() resumeUploadDto: ResumeUploadDto,
    @Request() req: any
  ) {
    // Extract user information from authenticated request
    const user = this.extractUserFromRequest(req);

    // Get Meta access token from user's stored credentials
    const metaAccessToken = await this.getMetaAccessTokenFromUser(user);

    this.logger.log(`User ${user.id} resuming upload session: ${resumeUploadDto.uploadSessionId}`);

    const result = await this.metaMediaUploadService.resumeUpload(
      resumeUploadDto.uploadSessionId,
      metaAccessToken
    );

    return result;
  }

  /**
   * Health check endpoint
   * GET /meta-media-upload/health
   */
  @Get('health')
  @HttpCode(HttpStatus.OK)
  async healthCheck() {
    return UploadResponseUtil.createSuccessResponse(
      { status: 'healthy', service: 'meta-media-upload' },
      'Meta Media Upload service is running',
      UPLOAD_CONSTANTS.HTTP_STATUS.OK
    );
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Validates app ID header
   */
  private validateAppId(appId: string): void {
    if (!appId || appId.trim().length === 0) {
      throw new BadRequestException('x-meta-app-id header is required');
    }
  }

  /**
   * Gets Meta access token from user's stored credentials
   */
  private async getMetaAccessTokenFromUser(user: any): Promise<string> {
    try {
      // Get user profile to get workspace_id
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      
      if (userProfileError || !userProfile) {
        throw new BadRequestException('User profile not found');
      }

      if (!userProfile.workspace_id) {
        throw new BadRequestException('User is not associated with any workspace');
      }

      // Get Meta credentials for the user's workspace
      const { data: credentials, error: credentialsError } = await this.supabaseService.getClient()
        .from('automate_whatsapp_meta_credentials')
        .select('access_token, status')
        .eq('workspace_id', userProfile.workspace_id)
        .eq('status', 'Active')
        .single();

      if (credentialsError || !credentials) {
        throw new BadRequestException('No active Meta credentials found for your workspace. Please connect your WhatsApp Business account first.');
      }

      if (!credentials.access_token) {
        throw new BadRequestException('Meta access token is not available. Please reconnect your WhatsApp Business account.');
      }

      return credentials.access_token;

    } catch (error) {
      this.logger.error('Failed to get Meta access token from user:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to retrieve Meta credentials. Please ensure your WhatsApp Business account is properly connected.');
    }
  }

  /**
   * Validates uploaded file
   */
  private validateFileUpload(file: any): void {
    if (!file) {
      throw new BadRequestException('File is required for upload');
    }

    if (!file.buffer || file.buffer.length === 0) {
      throw new BadRequestException('File buffer is empty');
    }

    // Validate file type using MIME type
    const supportedMimeTypes = Object.values(UPLOAD_CONSTANTS.SUPPORTED_FILE_TYPES);
    if (!supportedMimeTypes.includes(file.mimetype as any)) {
      throw new BadRequestException(
        `Invalid file type. Supported types: ${supportedMimeTypes.join(', ')}. Received: ${file.mimetype}`
      );
    }

    // Validate file size
    if (file.size > UPLOAD_CONSTANTS.MAX_FILE_SIZE) {
      throw new BadRequestException(UPLOAD_CONSTANTS.ERROR_MESSAGES.FILE_TOO_LARGE);
    }
  }

  /**
   * Extracts user information from authenticated request
   */
  private extractUserFromRequest(req: any): any {
    const user = req.user;
    if (!user) {
      throw new BadRequestException('User information not found in request');
    }
    return user;
  }

  private readonly logger = new Logger(MetaMediaUploadController.name);
}
