import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

/**
 * DTO for starting an upload session with Meta's Resumable Upload API
 */
export class StartUploadSessionDto {
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @IsNumber()
  @Min(1)
  @Max(100 * 1024 * 1024) // 100MB max file size
  fileLength: number;

  @IsString()
  @IsIn(['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'video/mp4'])
  fileType: 'application/pdf' | 'image/jpeg' | 'image/jpg' | 'image/png' | 'video/mp4';
}

/**
 * Response DTO for upload session creation
 */
export class UploadSessionResponseDto {
  id: string;
  success: boolean;
  message: string;
}
