import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';

/**
 * DTO for creating a tag
 */
export class CreateTagDto {
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  @MinLength(1, { message: 'Name must be at least 1 character long' })
  @MaxLength(50, { message: 'Name must not exceed 50 characters' })
  name: string;

  @IsOptional()
  @IsString({ message: 'Text color must be a string' })
  @Matches(/^#[0-9A-Fa-f]{6}$/, { message: 'Text color must be a valid hex color (e.g., #FF0000)' })
  text_color?: string;

  @IsOptional()
  @IsString({ message: 'Background color must be a string' })
  @Matches(/^#[0-9A-Fa-f]{6}$/, { message: 'Background color must be a valid hex color (e.g., #FF0000)' })
  background_color?: string;
}
