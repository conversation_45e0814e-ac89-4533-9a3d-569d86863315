import { Injectable, UnauthorizedException, BadRequestException, NotFoundException, Logger, ConflictException } from '@nestjs/common';
import { SupabaseService } from 'src/supabase/supabase.service';
import { MetaApiService } from 'src/meta-api/meta-api.service';
import { AiService } from 'src/ai/ai.service';
import { ConfigService } from '@nestjs/config';
import { CreateTemplateDto, UpdateTemplateDto, TemplateQueryDto, CreateAiTemplateDto } from './dto';
import { TemplateResponseUtil, TemplateResponseData } from './utils/template-response.util';
import { TemplateValidationUtil } from './utils/template-validation.util';
import { TEMPLATE_CONSTANTS } from './utils/template-constants.util';

/**
 * Refactored TemplateService with improved structure and consistent response handling
 */
@Injectable()
export class TemplateService {
  private readonly logger = new Logger(TemplateService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly metaApiService: MetaApiService,
    private readonly configService: ConfigService,
    private readonly aiService: AiService
  ) {}

  // ==================== PUBLIC METHODS ====================

  /**
   * Create template (unified Meta format)
   */
  async createTemplate(templateDto: CreateTemplateDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting template creation process');
      console.log("create template payload", JSON.stringify(templateDto, null, 2));

      const user = TemplateValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = TemplateValidationUtil.validateUserProfile(userProfile, userProfileError);
      // Check for duplicate template
      const { data: existingTemplate, error: checkError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('id, name, language, waba_id')
        .eq('name', templateDto.name)
        .eq('language', templateDto.language)
        .eq('waba_id', templateDto.waba_id)
        .eq('workspace_id', workspaceId)
        .single();

      if (existingTemplate && !checkError) {
        return TemplateResponseUtil.createDuplicateErrorResponse(
          TEMPLATE_CONSTANTS.ERROR_MESSAGES.DUPLICATE_TEMPLATE,
          existingTemplate
        );
      }

      // Get Meta credentials - REQUIRED for template creation
      const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*')
        .eq('workspace_id', workspaceId)
        .eq('whatsapp_business_id', templateDto.waba_id)
        .eq('status', 'Active')
        .single();

      // Throw error if Meta credentials are not found or inactive
      if (!metaCredentials || credentialsError) {
        this.logger.error('Meta credentials not found or inactive', credentialsError);
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.META_CREDENTIALS_NOT_FOUND);
      }

      // Create template in Meta - REQUIRED
      let metaTemplateId: string;
      let metaTemplateStatus: string;
      let metaTemplateCategory: string;

      // Frontend already sends plain objects, use them directly

      try {

        // Use the components directly from frontend (Meta format)
        const metaTemplateData = {
          name: templateDto.name,
          category: templateDto.category,
          components: templateDto.components,
          language: templateDto.language 
        };
        console.log("metaTemplateData", JSON.stringify(metaTemplateData, null, 2));
        
        const metaResponse = await this.metaApiService.createTemplate(
          metaCredentials.whatsapp_business_id,
          metaCredentials.access_token,
          metaTemplateData
        );
        console.log("metaResponse", metaResponse);
        
        metaTemplateId = metaResponse.id;
        metaTemplateStatus = metaResponse.status as any;
        metaTemplateCategory = metaResponse.category;

        this.logger.log(`Template created in Meta with ID: ${metaTemplateId}`);
      } catch (metaError) {
        this.logger.error(`Failed to create template in Meta: ${metaError.message}`);
        throw new BadRequestException(metaError.message);
      }

      // Prepare payload for database storage (clean Meta format)
      const finalPayload = {
        name: templateDto.name,
        description: templateDto.description || '',
        language: templateDto.language,
        components: templateDto.components, // Use components directly from frontend
        meta_template_category: metaTemplateCategory,
        workspace_id: workspaceId,
        created_by: user.id,
        waba_id: templateDto.waba_id,
        meta_template_id: metaTemplateId,
        meta_template_status: metaTemplateStatus,
      };
      console.log("finalPayload", finalPayload);

      // Save template to database
      const { data: savedTemplate, error: saveError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .insert(finalPayload)
        .select()
        .single();

      if (saveError) {
        this.logger.error(`Failed to save template to database: ${saveError.message}`);
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_CREATION_FAILED);
      }

      this.logger.log(`Template created successfully with ID: ${savedTemplate.id}`);

      return TemplateResponseUtil.createSuccessResponse(
        { template: finalPayload },
        'Template created successfully',
        201
      );

    } catch (error) {
      this.logger.error(`Create Meta template failed: ${error.message}`);
      throw error;
    }
  }


  /**
   * Create draft template
   */
  async createDraftTemplate(templateDto: CreateTemplateDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting draft template creation process');

      const user = TemplateValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = TemplateValidationUtil.validateUserProfile(userProfile, userProfileError);

      // DTO validation is handled by class-validator decorators

      // Check for duplicate template
      const { data: existingTemplate, error: checkError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('id, name, language, waba_id')
        .eq('name', templateDto.name)
        .eq('language', templateDto.language)
        .eq('waba_id', templateDto.waba_id)
        .eq('workspace_id', workspaceId)
        .single();

      if (existingTemplate && !checkError) {
        return TemplateResponseUtil.createDuplicateErrorResponse(
          TEMPLATE_CONSTANTS.ERROR_MESSAGES.DUPLICATE_TEMPLATE,
          existingTemplate
        );
      }

      // Create draft template (no Meta submission)
      const finalPayload = {
        name: templateDto.name,
        description: templateDto.description || '',
        language: templateDto.language,
        components: templateDto.components,
        meta_template_id: null,
        meta_template_status: TEMPLATE_CONSTANTS.TEMPLATE_STATUSES.DRAFT,
        meta_template_category: templateDto.category,
        workspace_id: workspaceId,
        created_by: user.id,
        waba_id: templateDto.waba_id,
        is_active: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data: template, error } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .insert(finalPayload)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to create draft template', error);
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_CREATION_FAILED);
      }

      this.logger.log(`Draft template created successfully: ${template.id}`);

      const responseData = TemplateResponseUtil.createTemplateCreationData(template);
      return TemplateResponseUtil.createSuccessResponse(
        responseData,
        TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATE_CREATED_LOCAL_ONLY,
        TEMPLATE_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Create draft template failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Get templates by user
   */
  async getTemplates(req: any, query: TemplateQueryDto): Promise<any> {
    try {
      this.logger.log('Starting get templates by user process');

      const user = TemplateValidationUtil.validateUserContext(req);
      const { page, limit, skip } = TemplateValidationUtil.validatePaginationParams(query.page, query.limit);

      // Build query
      let supabaseQuery = this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('*')
        .eq('created_by', user.id);

      // Apply filters
      supabaseQuery = this.applyTemplateFilters(supabaseQuery, query);

      // Apply pagination and ordering
      const { data: templates, error } = await supabaseQuery
        .range(skip, skip + limit - 1)
        .order('created_at', { ascending: false });

      if (error) {
        this.logger.error('Failed to fetch user templates', error);
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_FETCH_FAILED);
      }

      // Get total count
      const { count, error: countError } = await this.getTemplateCount(user.id, query);

      if (countError) {
        this.logger.error('Failed to get user templates count', countError);
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_FETCH_FAILED);
      }

      this.logger.log(`User templates fetched successfully: ${count} total templates`);

      const pagination = TemplateResponseUtil.createPaginationMetadata(page, limit, count || 0);
      const responseData = TemplateResponseUtil.createTemplateListData(templates || [], pagination);

      return TemplateResponseUtil.createSuccessResponse(
        responseData,
        TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATES_FETCHED
      );

    } catch (error) {
      this.logger.error('Get templates by user failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Get templates by workspace
   */
  async getTemplatesByWorkspace(workspaceId: string, req: any, query: TemplateQueryDto): Promise<any> {
    try {
      this.logger.log(`Starting get templates for workspace: ${workspaceId}`);

      const user = TemplateValidationUtil.validateUserContext(req);
      const parsedWorkspaceId = TemplateValidationUtil.validateWorkspaceId(workspaceId);
      TemplateValidationUtil.validateWorkspaceAccess(await this.supabaseService.getWorkspaceMember(parsedWorkspaceId, user.id));

      const { page, limit, skip } = TemplateValidationUtil.validatePaginationParams(query.page, query.limit);

      // Build query
      let supabaseQuery = this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('*')
        .eq('workspace_id', parsedWorkspaceId);

      // Apply filters
      supabaseQuery = this.applyTemplateFilters(supabaseQuery, query);

      // Apply pagination and ordering
      const { data: templates, error } = await supabaseQuery
        .range(skip, skip + limit - 1)
        .order('created_at', { ascending: false });

      if (error) {
        this.logger.error('Failed to fetch workspace templates', error);
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_FETCH_FAILED);
      }

      // Get total count
      const { count, error: countError } = await this.getTemplateCount(parsedWorkspaceId, query, 'workspace_id');

      if (countError) {
        this.logger.error('Failed to get workspace templates count', countError);
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_FETCH_FAILED);
      }

      this.logger.log(`Workspace templates fetched successfully: ${count} total templates`);

      const pagination = TemplateResponseUtil.createPaginationMetadata(page, limit, count || 0);
      const responseData = TemplateResponseUtil.createTemplateListData(templates || [], pagination);

      return TemplateResponseUtil.createSuccessResponse(
        responseData,
        TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.WORKSPACE_TEMPLATES_FETCHED
      );

    } catch (error) {
      this.logger.error('Get templates by workspace failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Get template by ID
   */
  async getTemplateById(templateId: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get template process for ID: ${templateId}`);

      const user = TemplateValidationUtil.validateUserContext(req);

      const { data: template, error } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('*')
        .eq('id', templateId)
        .eq('created_by', user.id)
        .single();

      TemplateValidationUtil.validateTemplateExists(template, error);

      this.logger.log(`Template fetched successfully: ${templateId}`);

      const responseData = TemplateResponseUtil.createTemplateCreationData(template);
      return TemplateResponseUtil.createSuccessResponse(
        responseData,
        TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATE_FETCHED
      );

    } catch (error) {
      this.logger.error('Get template by ID failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Update template
   */
  async updateTemplate(templateId: string, updateDto: UpdateTemplateDto, req: any): Promise<any> {
    try {
      this.logger.log(`Starting update template process for ID: ${templateId}`);

      const user = TemplateValidationUtil.validateUserContext(req);

      // Check if template exists and belongs to user
      const { data: existingTemplate, error: fetchError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('*')
        .eq('id', templateId)
        .eq('created_by', user.id)
        .single();

      TemplateValidationUtil.validateTemplateExists(existingTemplate, fetchError);

      // Validate update data
      const updateData = TemplateValidationUtil.validateTemplateUpdateData(updateDto);

      let metaUpdateSuccess = false;
      let metaResponse: any = null;

      // Get user profile for Meta operations
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (userProfileError || !userProfile) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
      }

      try {
        // Get Meta credentials
        const { data: metaCredentials } = await this.supabaseService.getClient()
          .from(TEMPLATE_CONSTANTS.TABLES.META_CREDENTIALS)
          .select('*')
          .eq('created_by', user.id)
          .eq('status', 'Active')
          .eq('whatsapp_business_id', existingTemplate.waba_id)
          .single();

        if (metaCredentials) {
          // Use Meta format directly (no conversion needed)
          const metaTemplateData = {
            name: updateData.name || existingTemplate.name,
            category: updateData.meta_template_category || existingTemplate.meta_template_category,
            components: updateData.components || existingTemplate.components,
            language: updateData.language || existingTemplate.language
          };

          if (existingTemplate.meta_template_id) {
            // Update existing template in Meta
            await this.metaApiService.updateTemplate(
              existingTemplate.meta_template_id,
              metaCredentials.access_token,
              metaTemplateData
            );
            metaUpdateSuccess = true;
          } else {
            // Create new template in Meta
            metaResponse = await this.metaApiService.createTemplate(
              existingTemplate.waba_id,
              metaCredentials.access_token,
              metaTemplateData
            );
            metaUpdateSuccess = true;
          }
        }
      } catch (metaError) {
        this.logger.error('Failed to update/create template in Meta:', metaError.message);
        // Continue with local update even if Meta update fails
      }

      // Prepare final update payload
      const finalUpdateData = {
        ...updateData,
        updated_at: new Date().toISOString(),
      };

      // Add Meta-specific fields if we created a new template in Meta
      if (metaResponse) {
        finalUpdateData.meta_template_id = metaResponse.id;
        finalUpdateData.meta_template_status = metaResponse.status;
        finalUpdateData.meta_template_category = metaResponse.category;
      }

      const { data: updatedTemplate, error } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .update(finalUpdateData)
        .eq('id', templateId)
        .eq('created_by', user.id)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to update template', error);
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.TEMPLATE_UPDATE_FAILED);
      }

      this.logger.log(`Template updated successfully: ${templateId}`);

      const responseData = TemplateResponseUtil.createTemplateCreationData(updatedTemplate);
      const message = metaUpdateSuccess 
        ? TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATE_UPDATED_WITH_META
        : TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATE_UPDATED_LOCAL_ONLY;

      return TemplateResponseUtil.createSuccessResponse(
        responseData,
        message
      );

    } catch (error) {
      this.logger.error('Update template failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Delete template
   */
  async deleteTemplate(templateId: string, wabaId: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting delete template process for ID: ${templateId}`);

      const user = TemplateValidationUtil.validateUserContext(req);

      // Get user profile
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (userProfileError || !userProfile) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
      }

      // Fetch template
      const { data: existingTemplate, error: fetchError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('*')
        .eq('id', templateId)
        .eq('created_by', user.id)
        .eq('waba_id', wabaId)
        .single();

      TemplateValidationUtil.validateTemplateExists(existingTemplate, fetchError);

      // Fetch Meta credentials
      const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*')
        .eq('created_by', user.id)
        .eq('whatsapp_business_id', wabaId)
        .eq('status', 'Active')
        .single();

      if (credentialsError) {
        this.logger.error(`Failed to fetch Meta credentials for User ${user.id}`, credentialsError);
        throw new BadRequestException(`Failed to fetch Meta credentials: ${credentialsError.message}`);
      }

      // Delete from Meta if not DRAFT
      let deletedFromMeta = false;
      let metaErrorMessage: string | null = null;

      if (existingTemplate.meta_template_status !== TEMPLATE_CONSTANTS.TEMPLATE_STATUSES.DRAFT && existingTemplate.meta_template_id) {
        try {
          await this.metaApiService.deleteTemplate(existingTemplate.name, wabaId, metaCredentials.access_token);
          deletedFromMeta = true;
          this.logger.log(`Template deleted successfully on Meta: ${existingTemplate.name}`);
        } catch (metaError) {
          metaErrorMessage = metaError?.response?.data?.error?.message || metaError.message || 'Meta API error';
          this.logger.error(`Meta API delete failed for template ${existingTemplate.name}`, metaErrorMessage);
        }
      } else {
        this.logger.log(`Skipping Meta API call for DRAFT template: ${existingTemplate.name}`);
      }

      // Delete template from local DB
      const { error: deleteError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .delete()
        .eq('id', templateId)
        .eq('created_by', user.id);

      if (deleteError) {
        this.logger.error(`Failed to delete template from local DB: ${templateId}`, deleteError);
        throw new BadRequestException(`Failed to delete template in local DB: ${deleteError.message}`);
      }

      this.logger.log(`Template deleted successfully: ${templateId}`);

      // Handle Meta error
      if (metaErrorMessage) {
        return TemplateResponseUtil.createErrorResponse(
          `Template deleted locally but failed in Meta: ${metaErrorMessage}`,
          TEMPLATE_CONSTANTS.HTTP_STATUS.BAD_REQUEST,
          {
            templateId,
            template_name: existingTemplate.name,
            meta_template_status: existingTemplate.meta_template_status,
            deleted_from_meta: false,
          }
        );
      }

      const message = existingTemplate.meta_template_status === TEMPLATE_CONSTANTS.TEMPLATE_STATUSES.DRAFT
        ? TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATE_DELETED_LOCAL_ONLY
        : TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATE_DELETED_WITH_META;

      return TemplateResponseUtil.createSuccessResponse(
        {
          templateId: templateId,
          template_name: existingTemplate.name,
          meta_template_status: existingTemplate.meta_template_status,
          deleted_from_meta: deletedFromMeta,
        },
        message
      );

    } catch (error) {
      this.logger.error('Delete template failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Sync all templates with Meta
   */
  async syncAllWithMeta(wabaId: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting bulk sync with Meta for WABA: ${wabaId}`);

      const user = TemplateValidationUtil.validateUserContext(req);
      const validatedWabaId = TemplateValidationUtil.validateWabaId(wabaId);

      // Get user profile
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (userProfileError || !userProfile) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
      }

      // Get Meta credentials
      const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*')
        .eq('workspace_id', userProfile.workspace_id)
        .eq('status', 'Active')
        .eq('whatsapp_business_id', validatedWabaId)
        .single();

      if (credentialsError || !metaCredentials) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.META_CREDENTIALS_NOT_FOUND);
      }

      // Get all templates from Meta
      const metaTemplates = await this.metaApiService.getTemplates(
        metaCredentials.whatsapp_business_id,
        metaCredentials.access_token
      );

      // Get existing local templates for this waba_id
      const { data: existingLocalTemplates, error: fetchError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('meta_template_id')
        .eq('waba_id', validatedWabaId)
        .eq('workspace_id', userProfile.workspace_id);

      if (fetchError) {
        throw new BadRequestException(`Failed to fetch local templates: ${fetchError.message}`);
      }

      // Create a set of existing Meta template IDs for quick lookup
      const existingMetaTemplateIds = new Set(
        existingLocalTemplates
          .filter(t => t.meta_template_id)
          .map(t => t.meta_template_id)
      );

      const syncResults: any[] = [];
      const errors: any[] = [];

      // Filter Meta templates that don't exist locally
      const newMetaTemplates = metaTemplates.data.filter(
        metaTemplate => !existingMetaTemplateIds.has(metaTemplate.id)
      );

      for (const metaTemplate of newMetaTemplates) {
        try {
          // Use Meta template format directly (no conversion needed)
          
          // Save to local database
          const { data: savedTemplate, error: saveError } = await this.supabaseService.getClient()
            .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
            .insert({
              name: metaTemplate.name,
              description: '',
              components: metaTemplate.components || [], // Store Meta components directly
              language: metaTemplate.language,
              meta_template_id: metaTemplate.id,
              meta_template_status: metaTemplate.status,
              meta_template_category: metaTemplate.category,
              workspace_id: userProfile.workspace_id,
              created_by: user.id,
              is_active: metaTemplate.status === TEMPLATE_CONSTANTS.TEMPLATE_STATUSES.APPROVED,
              waba_id: validatedWabaId,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (saveError) {
            throw new Error(`Failed to save template: ${saveError.message}`);
          }

          syncResults.push({
            template_id: savedTemplate.id,
            template_name: metaTemplate.name,
            meta_template_id: metaTemplate.id,
            status: 'success'
          });
        } catch (error) {
          errors.push({
            template_name: metaTemplate.name,
            meta_template_id: metaTemplate.id,
            error: error.message
          });
        }
      }

      this.logger.log(`Bulk sync completed: ${syncResults.length} successful, ${errors.length} failed`);

      const responseData = TemplateResponseUtil.createBulkSyncData(syncResults, errors, metaTemplates.data);
      return TemplateResponseUtil.createSuccessResponse(
        responseData,
        TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATES_BULK_SYNCED
      );

    } catch (error) {
      this.logger.error('Sync all with Meta failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Sync single template with Meta
   */
  async syncWithMeta(templateId: string, req: any): Promise<any> {
    try {
      this.logger.log(`Starting sync template with Meta for ID: ${templateId}`);

      const user = TemplateValidationUtil.validateUserContext(req);

      // Check if template exists and belongs to user
      const { data: template, error: fetchError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .select('*')
        .eq('id', templateId)
        .eq('created_by', user.id)
        .single();

      TemplateValidationUtil.validateTemplateExists(template, fetchError);

      // Get user profile
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (userProfileError || !userProfile) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
      }

      // Get Meta credentials
      const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*')
        .eq('created_by', user.id)
        .eq('status', 'Active')
        .single();

      if (credentialsError || !metaCredentials) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.META_CREDENTIALS_NOT_FOUND);
      }

      // Use template components directly (already in Meta format)
      const metaTemplateData = {
        name: template.name,
        category: template.meta_template_category,
        components: template.components,
        language: template.language
      };

      // Create template in Meta
      const metaResponse = await this.metaApiService.createTemplate(
        metaCredentials.whatsapp_business_id,
        metaCredentials.access_token,
        metaTemplateData
      );

      // Update local template with Meta template ID
      const { data: updatedTemplate, error: updateError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .update({
          meta_template_id: metaResponse.id,
          meta_template_status: metaResponse.status,
          meta_template_category: template.meta_template_category,
          updated_at: new Date().toISOString()
        })
        .eq('id', templateId)
        .select()
        .single();

      if (updateError) {
        throw new BadRequestException(`Failed to update template: ${updateError.message}`);
      }

      this.logger.log(`Template synced with Meta successfully: ${templateId}`);

      const responseData = TemplateResponseUtil.createTemplateSyncData(updatedTemplate, metaResponse);
      return TemplateResponseUtil.createSuccessResponse(
        responseData,
        TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.TEMPLATE_SYNCED
      );

    } catch (error) {
      this.logger.error('Sync with Meta failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Get Meta templates
   */
  async getMetaTemplates(req: any): Promise<any> {
    try {
      this.logger.log('Starting get Meta templates process');

      const user = TemplateValidationUtil.validateUserContext(req);

      // Get user profile
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (userProfileError || !userProfile) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
      }

      // Get Meta credentials
      const { data: metaCredentials, error: credentialsError } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.META_CREDENTIALS)
        .select('*')
        .eq('workspace_id', userProfile.workspace_id)
        .eq('status', 'Active')
        .single();

      if (credentialsError || !metaCredentials) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.META_CREDENTIALS_NOT_FOUND);
      }

      const metaTemplates = await this.metaApiService.getTemplates(
        metaCredentials.whatsapp_business_id,
        metaCredentials.access_token
      );

      // Use Meta templates directly (no conversion needed)
      this.logger.log(`Meta templates fetched successfully: ${metaTemplates.data.length} templates`);

      return TemplateResponseUtil.createSuccessResponse(
        {
          templates: metaTemplates.data,
          total: metaTemplates.data.length,
          paging: metaTemplates.paging
        },
        TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.META_TEMPLATES_FETCHED
      );

    } catch (error) {
      this.logger.error('Get Meta templates failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Generate AI template
   */
  async generateAiTemplate(aiTemplateDto: CreateAiTemplateDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting AI template generation process');

      const user = TemplateValidationUtil.validateUserContext(req);

      // Get user profile
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (userProfileError || !userProfile) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
      }

      // Generate template using AI
      const generatedTemplate = await this.aiService.generateTemplate(aiTemplateDto);
      
      // Save as draft template (not submitted to Meta)
      const payload = {
        name: generatedTemplate.name,
        description: generatedTemplate.description,
        language: generatedTemplate.language || 'en',
        components: generatedTemplate.components || [], // Store AI generated components
        meta_template_id: null, // Not submitted to Meta yet
        meta_template_status: TEMPLATE_CONSTANTS.TEMPLATE_STATUSES.DRAFT,
        meta_template_category: generatedTemplate.meta_template_category,
        workspace_id: userProfile.workspace_id,
        created_by: user.id,
        is_active: false, // Draft templates are not active
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ai_generated: true,
        waba_id: aiTemplateDto.waba_id
      };

      const { data: template, error } = await this.supabaseService.getClient()
        .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
        .insert(payload)
        .select()
        .single();

      if (error) {
        this.logger.error('Failed to save AI-generated template', error);
        throw new BadRequestException(`Failed to save AI-generated template: ${error.message}`);
      }

      this.logger.log(`AI template generated and saved successfully: ${template.id}`);

      const responseData = TemplateResponseUtil.createTemplateCreationData(template);
      return TemplateResponseUtil.createSuccessResponse(
        responseData,
        TEMPLATE_CONSTANTS.SUCCESS_MESSAGES.AI_TEMPLATE_GENERATED,
        TEMPLATE_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Generate AI template failed:', error);
      this.handleTemplateError(error);
    }
  }

  /**
   * Generate template from voice
   */
  async generateTemplateFromVoice(file: any, body: any, req: any): Promise<any> {
    try {
      this.logger.log('Processing voice input for template generation...');
      
      TemplateValidationUtil.validateAudioFile(file);

      // Extract WABA ID from form data
      const wabaId = body?.waba_id;
      if (!wabaId) {
        throw new BadRequestException(TEMPLATE_CONSTANTS.ERROR_MESSAGES.WABA_ID_REQUIRED);
      }
      
      this.logger.log(`Extracted WABA ID from form data: ${wabaId}`);
      
      // Step 1: Transcribe audio to text
      const transcribedText = await this.aiService.transcribeAudio(file, file.mimetype);
      
      if (!transcribedText || transcribedText.trim() === '') {
        throw new BadRequestException('No text found in audio transcription');
      }

      this.logger.log(`Transcribed text: ${transcribedText}`);
      
      // Step 2: Generate template using the transcribed text and extracted WABA ID
      return await this.generateAiTemplate({ prompt: transcribedText, waba_id: wabaId }, req);
      
    } catch (error) {
      this.logger.error('Failed to generate template from voice:', error);
      this.handleTemplateError(error);
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Applies filters to template query
   */
  private applyTemplateFilters(query: any, filters: TemplateQueryDto): any {
    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    if (filters.category) {
      query = query.eq('category', filters.category);
    }

    if (filters.type) {
      query = query.eq('type', filters.type);
    }

    if (filters.language) {
      query = query.eq('language', filters.language);
    }

    if (filters.status) {
      query = query.eq('meta_template_status', filters.status);
    }

    if (filters.waba_id) {
      query = query.eq('waba_id', filters.waba_id);
    }

    return query;
  }

  /**
   * Gets template count with filters
   */
  private async getTemplateCount(userIdOrWorkspaceId: string | number, query: TemplateQueryDto, field: string = 'created_by'): Promise<{ count: number | null; error: any }> {
    let countQuery = this.supabaseService.getClient()
      .from(TEMPLATE_CONSTANTS.TABLES.TEMPLATES)
      .select('*', { count: 'exact', head: true })
      .eq(field, userIdOrWorkspaceId);

    // Apply same filters for count
    countQuery = this.applyTemplateFilters(countQuery, query);

    return await countQuery;
  }

  /**
   * Handles template-related errors
   */
  private handleTemplateError(error: any): void {
    if (error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException) {
      throw error;
    }

    this.logger.error('Unexpected template error:', error);
    throw new BadRequestException('Internal server error');
  }





}
