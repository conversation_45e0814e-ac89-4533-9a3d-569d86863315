/**
 * Utility class for validating upload-related data
 */
export class UploadValidationUtil {
  /**
   * Validates file type against allowed Meta formats
   */
  static validateFileType(fileType: string): boolean {
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'video/mp4'
    ];
    return allowedTypes.includes(fileType);
  }

  /**
   * Validates file size against Meta limits
   */
  static validateFileSize(fileSize: number): boolean {
    // Meta allows up to 100MB for resumable uploads
    const maxSize = 100 * 1024 * 1024; // 100MB in bytes
    return fileSize > 0 && fileSize <= maxSize;
  }

  /**
   * Validates file name format
   */
  static validateFileName(fileName: string): boolean {
    if (!fileName || fileName.trim().length === 0) {
      return false;
    }
    
    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
    return !invalidChars.test(fileName);
  }

  /**
   * Validates upload session ID format
   */
  static validateUploadSessionId(sessionId: string): boolean {
    if (!sessionId || sessionId.trim().length === 0) {
      return false;
    }
    
    // Meta upload session IDs start with "upload:"
    return sessionId.startsWith('upload:');
  }

  /**
   * Extracts session ID from full upload session ID
   */
  static extractSessionId(fullSessionId: string): string {
    if (fullSessionId.startsWith('upload:')) {
      return fullSessionId.substring(7); // Remove "upload:" prefix
    }
    return fullSessionId;
  }

  /**
   * Builds full upload session ID
   */
  static buildSessionId(sessionId: string): string {
    if (sessionId.startsWith('upload:')) {
      return sessionId;
    }
    return `upload:${sessionId}`;
  }

  /**
   * Gets MIME type from file extension
   */
  static getMimeTypeFromExtension(fileName: string): string {
    const extension = fileName.toLowerCase().split('.').pop();
    
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'mp4':
        return 'video/mp4';
      default:
        throw new Error(`Unsupported file extension: ${extension}`);
    }
  }

  /**
   * Validates file offset value
   */
  static validateFileOffset(offset: number): boolean {
    return offset >= 0 && Number.isInteger(offset);
  }
}
