import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { MessageStatusService } from '../../message-status.service';
import { Campaign, CampaignDocument } from '../../../schema/campaign.schema';
import { Types } from 'mongoose';

describe('MessageStatusService', () => {
  let service: MessageStatusService;
  let mockCampaignModel: any;

  beforeEach(async () => {
    const mockModel = {
      findById: jest.fn(),
      findByIdAndUpdate: jest.fn(),
      updateOne: jest.fn(),
      aggregate: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageStatusService,
        {
          provide: getModelToken(Campaign.name),
          useValue: mockModel,
        },
      ],
    }).compile();

    service = module.get<MessageStatusService>(MessageStatusService);
    mockCampaignModel = module.get(getModelToken(Campaign.name));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateMessageStatus', () => {
    it('should update message status for database contact', async () => {
      const campaignId = 'campaign-123';
      const contactId = 'contact-456';
      const status = 'SENT';
      const messageId = 'message-789';

      mockCampaignModel.updateOne.mockResolvedValue({ acknowledged: true });

      await service.updateMessageStatus(campaignId, contactId, status, messageId);

      expect(mockCampaignModel.updateOne).toHaveBeenCalledWith(
        { _id: campaignId, 'message_logs.contact_id': new Types.ObjectId(contactId) },
        {
          $inc: {
            'delivery_stats.sent': 1,
            pending_count: -1,
          },
          $set: {
            'message_logs.$[elem].status': 'SENT',
            'message_logs.$[elem].sent_at': expect.any(Date),
            'message_logs.$[elem].message_id': 'message-789',
          },
        },
        {
          arrayFilters: [{ 'elem.contact_id': new Types.ObjectId(contactId) }],
        }
      );
    });

    it('should update message status for CSV contact', async () => {
      const campaignId = 'campaign-123';
      const contactId = 'csv_8561038330';
      const status = 'SENT';
      const messageId = 'message-789';

      mockCampaignModel.updateOne.mockResolvedValue({ acknowledged: true });

      await service.updateMessageStatus(campaignId, contactId, status, messageId);

      expect(mockCampaignModel.updateOne).toHaveBeenCalledWith(
        { _id: campaignId, 'message_logs.phone': '8561038330' },
        {
          $inc: {
            'delivery_stats.sent': 1,
            pending_count: -1,
          },
          $set: {
            'message_logs.$[elem].status': 'SENT',
            'message_logs.$[elem].sent_at': expect.any(Date),
            'message_logs.$[elem].message_id': 'message-789',
          },
        },
        {
          arrayFilters: [{ 'elem.phone': '8561038330' }],
        }
      );
    });

    it('should update message status with error message', async () => {
      const campaignId = 'campaign-123';
      const contactId = 'contact-456';
      const status = 'FAILED';
      const errorMessage = 'Invalid phone number';

      mockCampaignModel.updateOne.mockResolvedValue({ acknowledged: true });

      await service.updateMessageStatus(campaignId, contactId, status, undefined, errorMessage);

      expect(mockCampaignModel.updateOne).toHaveBeenCalledWith(
        { _id: campaignId, 'message_logs.contact_id': new Types.ObjectId(contactId) },
        {
          $inc: {
            'delivery_stats.failed': 1,
            pending_count: -1,
          },
          $set: {
            'message_logs.$[elem].status': 'FAILED',
            'message_logs.$[elem].failed_at': expect.any(Date),
            'message_logs.$[elem].error_message': 'Invalid phone number',
          },
        },
        {
          arrayFilters: [{ 'elem.contact_id': new Types.ObjectId(contactId) }],
        }
      );
    });

    it('should update delivered status', async () => {
      const campaignId = 'campaign-123';
      const contactId = 'contact-456';
      const status = 'DELIVERED';
      const messageId = 'message-789';

      mockCampaignModel.updateOne.mockResolvedValue({ acknowledged: true });

      await service.updateMessageStatus(campaignId, contactId, status, messageId);

      expect(mockCampaignModel.updateOne).toHaveBeenCalledWith(
        { _id: campaignId, 'message_logs.contact_id': new Types.ObjectId(contactId) },
        {
          $inc: {
            'delivery_stats.delivered': 1,
          },
          $set: {
            'message_logs.$[elem].status': 'DELIVERED',
            'message_logs.$[elem].delivered_at': expect.any(Date),
          },
        },
        {
          arrayFilters: [{ 'elem.contact_id': new Types.ObjectId(contactId) }],
        }
      );
    });

    it('should update read status', async () => {
      const campaignId = 'campaign-123';
      const contactId = 'contact-456';
      const status = 'READ';
      const messageId = 'message-789';

      mockCampaignModel.updateOne.mockResolvedValue({ acknowledged: true });

      await service.updateMessageStatus(campaignId, contactId, status, messageId);

      expect(mockCampaignModel.updateOne).toHaveBeenCalledWith(
        { _id: campaignId, 'message_logs.contact_id': new Types.ObjectId(contactId) },
        {
          $inc: {
            'delivery_stats.read': 1,
          },
          $set: {
            'message_logs.$[elem].status': 'READ',
            'message_logs.$[elem].read_at': expect.any(Date),
          },
        },
        {
          arrayFilters: [{ 'elem.contact_id': new Types.ObjectId(contactId) }],
        }
      );
    });

    it('should handle database error gracefully', async () => {
      const campaignId = 'campaign-123';
      const contactId = 'contact-456';
      const status = 'SENT';
      const messageId = 'message-789';

      mockCampaignModel.updateOne.mockRejectedValue(new Error('Database error'));

      // Should not throw error
      await expect(service.updateMessageStatus(campaignId, contactId, status, messageId)).resolves.not.toThrow();
    });
  });

  describe('getCampaignMessageStats', () => {
    it('should return campaign message statistics', async () => {
      const campaignId = 'campaign-123';
      const mockStats = {
        total: 100,
        sent: 80,
        delivered: 75,
        read: 60,
        failed: 20,
        pending: 0,
      };

      mockCampaignModel.aggregate.mockResolvedValue([mockStats]);

      const result = await service.getCampaignMessageStats(campaignId);

      expect(mockCampaignModel.aggregate).toHaveBeenCalledWith([
        { $match: { _id: new Types.ObjectId(campaignId) } },
        {
          $project: {
            total: { $size: '$message_logs' },
            sent: {
              $size: {
                $filter: {
                  input: '$message_logs',
                  cond: { $eq: ['$$this.status', 'SENT'] },
                },
              },
            },
            delivered: {
              $size: {
                $filter: {
                  input: '$message_logs',
                  cond: { $eq: ['$$this.status', 'DELIVERED'] },
                },
              },
            },
            read: {
              $size: {
                $filter: {
                  input: '$message_logs',
                  cond: { $eq: ['$$this.status', 'READ'] },
                },
              },
            },
            failed: {
              $size: {
                $filter: {
                  input: '$message_logs',
                  cond: { $eq: ['$$this.status', 'FAILED'] },
                },
              },
            },
            pending: {
              $size: {
                $filter: {
                  input: '$message_logs',
                  cond: { $eq: ['$$this.status', 'PENDING'] },
                },
              },
            },
          },
        },
      ]);

      expect(result).toEqual(mockStats);
    });

    it('should return empty stats when campaign not found', async () => {
      const campaignId = 'campaign-123';

      mockCampaignModel.aggregate.mockResolvedValue([]);

      const result = await service.getCampaignMessageStats(campaignId);

      expect(result).toEqual({
        total: 0,
        sent: 0,
        delivered: 0,
        read: 0,
        failed: 0,
        pending: 0,
      });
    });
  });

  describe('getMessageLogs', () => {
    it('should return message logs for campaign', async () => {
      const campaignId = 'campaign-123';
      const mockCampaign = {
        _id: campaignId,
        message_logs: [
          {
            contact_id: 'contact-1',
            phone: '1234567890',
            status: 'SENT',
            sent_at: new Date(),
            message_id: 'message-1',
          },
          {
            contact_id: 'contact-2',
            phone: '0987654321',
            status: 'DELIVERED',
            sent_at: new Date(),
            delivered_at: new Date(),
            message_id: 'message-2',
          },
        ],
      };

      mockCampaignModel.findById.mockResolvedValue(mockCampaign);

      const result = await service.getMessageLogs(campaignId);

      expect(mockCampaignModel.findById).toHaveBeenCalledWith(campaignId);
      expect(result).toEqual(mockCampaign.message_logs);
    });

    it('should return empty array when campaign not found', async () => {
      const campaignId = 'campaign-123';

      mockCampaignModel.findById.mockResolvedValue(null);

      const result = await service.getMessageLogs(campaignId);

      expect(result).toEqual([]);
    });

    it('should filter message logs by status', async () => {
      const campaignId = 'campaign-123';
      const status = 'SENT';
      const mockCampaign = {
        _id: campaignId,
        message_logs: [
          {
            contact_id: 'contact-1',
            phone: '1234567890',
            status: 'SENT',
            sent_at: new Date(),
            message_id: 'message-1',
          },
          {
            contact_id: 'contact-2',
            phone: '0987654321',
            status: 'DELIVERED',
            sent_at: new Date(),
            delivered_at: new Date(),
            message_id: 'message-2',
          },
        ],
      };

      mockCampaignModel.findById.mockResolvedValue(mockCampaign);

      const result = await service.getMessageLogs(campaignId, status);

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('SENT');
    });
  });

  describe('getFailedMessages', () => {
    it('should return failed messages for campaign', async () => {
      const campaignId = 'campaign-123';
      const mockCampaign = {
        _id: campaignId,
        message_logs: [
          {
            contact_id: 'contact-1',
            phone: '1234567890',
            status: 'SENT',
            sent_at: new Date(),
            message_id: 'message-1',
          },
          {
            contact_id: 'contact-2',
            phone: '0987654321',
            status: 'FAILED',
            failed_at: new Date(),
            error_message: 'Invalid phone number',
          },
        ],
      };

      mockCampaignModel.findById.mockResolvedValue(mockCampaign);

      const result = await service.getFailedMessages(campaignId);

      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('FAILED');
      expect(result[0].error_message).toBe('Invalid phone number');
    });
  });

  describe('retryFailedMessages', () => {
    it('should retry failed messages', async () => {
      const campaignId = 'campaign-123';
      const mockCampaign = {
        _id: campaignId,
        message_logs: [
          {
            contact_id: 'contact-1',
            phone: '1234567890',
            status: 'FAILED',
            failed_at: new Date(),
            error_message: 'Temporary error',
            retry_count: 0,
          },
        ],
      };

      mockCampaignModel.findById.mockResolvedValue(mockCampaign);
      mockCampaignModel.updateOne.mockResolvedValue({ acknowledged: true });

      const result = await service.retryFailedMessages(campaignId);

      expect(mockCampaignModel.updateOne).toHaveBeenCalledWith(
        { _id: campaignId, 'message_logs.contact_id': new Types.ObjectId('contact-1') },
        {
          $set: {
            'message_logs.$[elem].status': 'PENDING',
            'message_logs.$[elem].retry_count': 1,
            'message_logs.$[elem].error_message': null,
          },
        },
        {
          arrayFilters: [{ 'elem.contact_id': new Types.ObjectId('contact-1') }],
        }
      );

      expect(result).toEqual({
        retried: 1,
        failed: 0,
      });
    });

    it('should not retry messages that have exceeded max retry count', async () => {
      const campaignId = 'campaign-123';
      const mockCampaign = {
        _id: campaignId,
        message_logs: [
          {
            contact_id: 'contact-1',
            phone: '1234567890',
            status: 'FAILED',
            failed_at: new Date(),
            error_message: 'Permanent error',
            retry_count: 3, // Max retry count exceeded
          },
        ],
      };

      mockCampaignModel.findById.mockResolvedValue(mockCampaign);

      const result = await service.retryFailedMessages(campaignId);

      expect(result).toEqual({
        retried: 0,
        failed: 1,
      });
    });
  });
});
