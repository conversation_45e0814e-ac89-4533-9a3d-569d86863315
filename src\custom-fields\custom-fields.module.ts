import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CustomField, CustomFieldSchema } from '../schema/custom-field.schema';
import { CustomFieldsController } from './custom-fields.controller';
import { CustomFieldsService } from './custom-fields.service';
import { SupabaseModule } from '../supabase/supabase.module';
import { AuthModule } from 'src/auth/auth.module';

@Module({
	imports: [
    SupabaseModule,
    AuthModule,
    MongooseModule.forFeature([{ name: CustomField.name, schema: CustomFieldSchema }])
	],
  controllers: [CustomFieldsController],
  providers: [CustomFieldsService],
  exports: [MongooseModule, CustomFieldsService],
})
export class CustomFieldsModule {}


