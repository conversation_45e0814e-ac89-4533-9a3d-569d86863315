import { UnauthorizedException, BadRequestException } from '@nestjs/common';
import { WORKSPACE_CONSTANTS } from './workspace-constants.util';

/**
 * Utility class for workspace-related validations
 */
export class WorkspaceValidationUtil {
  /**
   * Validates user context from request
   */
  static validateUserContext(req: any): { id: string; email?: string; workspace_id?: number } {
    const user = req.user;
    if (!user || !user.id) {
      throw new UnauthorizedException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
    }
    return user;
  }

  /**
   * Validates user profile exists and has workspace
   */
  static validateUserProfile(userProfile: any, error?: any): void {
    if (!userProfile || error) {
      throw new BadRequestException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.USER_PROFILE_NOT_FOUND);
    }
  }

  /**
   * Validates user doesn't already have a workspace
   */
  static validateUserHasNoWorkspace(existingWorkspace: any): void {
    if (existingWorkspace) {
      throw new BadRequestException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.USER_ALREADY_HAS_WORKSPACE);
    }
  }

  /**
   * Validates workspace exists
   */
  static validateWorkspaceExists(workspace: any, error?: any): void {
    if (!workspace || error) {
      throw new BadRequestException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.WORKSPACE_NOT_FOUND);
    }
  }

  /**
   * Validates user has workspace association
   */
  static validateUserWorkspaceAssociation(workspaceId: number | null): void {
    if (!workspaceId) {
      throw new BadRequestException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.NO_WORKSPACE_ASSOCIATED);
    }
  }

  /**
   * Validates user doesn't already exist
   */
  static validateUserDoesNotExist(existingUser: any): void {
    if (existingUser) {
      throw new BadRequestException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.USER_ALREADY_EXISTS);
    }
  }

  /**
   * Validates member doesn't already exist in workspace
   */
  static validateMemberDoesNotExist(existingMember: any, error?: any): void {
    if (existingMember && !error) {
      throw new BadRequestException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.MEMBER_ALREADY_EXISTS);
    }
  }

  /**
   * Validates role ID is provided when WABA access is enabled
   */
  static validateRoleIdForWabaAccess(wabaAccess: boolean, roleId?: string): void {
    if (wabaAccess && !roleId) {
      throw new BadRequestException(WORKSPACE_CONSTANTS.ERROR_MESSAGES.ROLE_ID_REQUIRED);
    }
  }

  /**
   * Validates pagination parameters
   */
  static validatePaginationParams(page?: string, limit?: string): { page: number; limit: number } {
    const pageNum = Math.max(1, parseInt(page || '1') || 1);
    const limitNum = Math.min(
      WORKSPACE_CONSTANTS.PAGINATION.MAX_LIMIT,
      Math.max(1, parseInt(limit || String(WORKSPACE_CONSTANTS.PAGINATION.DEFAULT_LIMIT)) || WORKSPACE_CONSTANTS.PAGINATION.DEFAULT_LIMIT)
    );
    
    return { page: pageNum, limit: limitNum };
  }

  /**
   * Validates sort parameters
   */
  static validateSortParams(sortBy?: string, sortOrder?: string): { sortBy: string; sortOrder: string } {
    const validSortFields = ['created_at', 'updated_at', 'first_name', 'last_name', 'email', 'role'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortByField = validSortFields.includes(sortBy || '') ? sortBy! : 'created_at';
    const sortOrderField = validSortOrders.includes(sortOrder || '') ? sortOrder! : 'desc';
    
    return { sortBy: sortByField, sortOrder: sortOrderField };
  }

  /**
   * Validates workspace creation data
   */
  static validateWorkspaceCreationData(createWorkspaceDto: any, userId: string): any {
    return {
      ...createWorkspaceDto,
      created_by: userId,
      status: WORKSPACE_CONSTANTS.DEFAULTS.STATUS,
      trial_expiry: new Date(Date.now() + WORKSPACE_CONSTANTS.DEFAULTS.TRIAL_DAYS * 24 * 60 * 60 * 1000),
      waba_access: true,
    };
  }

  /**
   * Validates member creation data
   */
  static validateMemberCreationData(memberData: any, workspaceId: number, userId: string): any {
    return {
      workspace_id: workspaceId,
      user_id: userId,
      role: memberData.role,
      status: WORKSPACE_CONSTANTS.MEMBER_STATUS.ACTIVE,
      reports_to: memberData.reports_to || null,
      waba_access: memberData.waba_access,
    };
  }

  /**
   * Validates user profile creation data
   */
  static validateUserProfileCreationData(memberData: any, userId: string, workspaceId: number): any {
    return {
      id: userId,
      email: memberData.email,
      first_name: memberData.first_name,
      last_name: memberData.last_name,
      phone: parseInt(memberData.phone),
      country: memberData.country,
      country_code: memberData.country_code,
      terms_conditions: true,
      workspace_id: workspaceId,
    };
  }
}

