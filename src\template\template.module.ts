import { Modu<PERSON> } from '@nestjs/common';
import { TemplateController } from './template.controller';
import { TemplateService } from './template.service';
import { SupabaseModule } from 'src/supabase/supabase.module';
import { AuthModule } from 'src/auth/auth.module';
import { MetaApiModule } from 'src/meta-api/meta-api.module';
import { ConfigModule } from '@nestjs/config';
import { AiModule } from 'src/ai/ai.module';

@Module({
  imports: [
    ConfigModule,
    AuthModule,
    SupabaseModule,
    MetaApiModule,
    AiModule
  ],
  controllers: [TemplateController],
  providers: [TemplateService],
  exports: [TemplateService],
})
export class TemplateModule {} 