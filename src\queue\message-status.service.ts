import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CampaignExecution, CampaignExecutionDocument } from '../schema/campaign-execution.schema';

@Injectable()
export class MessageStatusService {
  private readonly logger = new Logger(MessageStatusService.name);

  constructor(
    @InjectModel(CampaignExecution.name)
    private campaignExecutionModel: Model<CampaignExecutionDocument>,
  ) {}

  /**
   * Update message status in campaign execution
   */
  async updateMessageStatus(
    campaignId: string,
    contactId: string,
    status: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED',
    messageId?: string,
    errorMessage?: string,
    retryCount: number = 0,
  ): Promise<void> {
    try {
      this.logger.debug(`Updating message status for campaign ${campaignId}, contact ${contactId}, status: ${status}`);

      const updateData: any = {
        updated_at: new Date(),
      };

      // Update counters based on status
      if (status === 'SENT') {
        updateData.$inc = { sent_contacts: 1 };
      } else if (status === 'FAILED') {
        updateData.$inc = { failed_contacts: 1 };
      }

      // Add error log if there's an error message
      if (errorMessage) {
        updateData.$push = {
          error_logs: {
            timestamp: new Date(),
            level: 'ERROR',
            message: errorMessage,
            details: { contactId, messageId, retryCount }
          }
        };
      }

      const result = await this.campaignExecutionModel.updateOne(
        { campaign_id: campaignId },
        updateData,
        { upsert: true }
      );

      if (result.modifiedCount === 0 && result.upsertedCount === 0) {
        this.logger.warn(`No campaign execution found for campaign ${campaignId}`);
      }

      this.logger.debug(`Message status updated successfully for campaign ${campaignId}, contact ${contactId}`);
    } catch (error) {
      this.logger.error(`Failed to update message status for campaign ${campaignId}, contact ${contactId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get message status for a specific contact in a campaign
   */
  async getMessageStatus(campaignId: string, contactId: string): Promise<any> {
    try {
      const campaignExecution = await this.campaignExecutionModel.findOne({
        campaign_id: campaignId
      });

      if (!campaignExecution) {
        return null;
      }

      // Check error logs for this contact
      const errorLog = campaignExecution.error_logs?.find(log => 
        log.details?.contactId === contactId
      );

      return {
        contactId,
        status: errorLog ? 'FAILED' : 'SENT',
        errorMessage: errorLog?.message,
        timestamp: errorLog?.timestamp || campaignExecution.started_at
      };
    } catch (error) {
      this.logger.error(`Failed to get message status for campaign ${campaignId}, contact ${contactId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all message statuses for a campaign
   */
  async getCampaignMessageStatuses(campaignId: string): Promise<any> {
    try {
      const campaignExecution = await this.campaignExecutionModel.findOne({
        campaign_id: campaignId
      });

      if (!campaignExecution) {
        return null;
      }

      return {
        totalContacts: campaignExecution.total_contacts,
        sentContacts: campaignExecution.sent_contacts,
        failedContacts: campaignExecution.failed_contacts,
        processedContacts: campaignExecution.processed_contacts,
        errorLogs: campaignExecution.error_logs || []
      };
    } catch (error) {
      this.logger.error(`Failed to get campaign message statuses for campaign ${campaignId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStats(campaignId: string): Promise<any> {
    try {
      const campaignExecution = await this.campaignExecutionModel.findOne({
        campaign_id: campaignId
      });

      if (!campaignExecution) {
        return {
          total: 0,
          sent: 0,
          delivered: 0,
          read: 0,
          failed: 0,
        };
      }

      return {
        total: campaignExecution.total_contacts,
        sent: campaignExecution.sent_contacts,
        failed: campaignExecution.failed_contacts,
        processed: campaignExecution.processed_contacts,
        pending: campaignExecution.pending_contacts,
        status: campaignExecution.status,
        startTime: campaignExecution.started_at,
        endTime: campaignExecution.completed_at,
        duration: campaignExecution.execution_stats?.duration_ms
      };
    } catch (error) {
      this.logger.error(`Failed to get campaign stats for campaign ${campaignId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create or update campaign execution record
   */
  async createOrUpdateCampaignExecution(
    campaignId: string,
    workspaceId: number,
    userId: string,
    totalContacts: number,
  ): Promise<void> {
    try {
      await this.campaignExecutionModel.updateOne(
        { campaign_id: campaignId },
        {
          $set: {
            campaign_id: campaignId,
            execution_id: `exec_${campaignId}_${Date.now()}`,
            status: 'RUNNING',
            total_contacts: totalContacts,
            processed_contacts: 0,
            sent_contacts: 0,
            failed_contacts: 0,
            pending_contacts: totalContacts,
            started_at: new Date(),
            is_active: true,
            metadata: {
              workspace_id: workspaceId,
              user_id: userId,
            },
            execution_stats: {
              start_time: new Date(),
            },
            rate_limiting: {
              enabled: true,
              messages_per_second: 10,
              delay_between_batches_ms: 1000,
            },
            retry_config: {
              max_retries: 3,
              retry_delay_ms: 2000,
              exponential_backoff: true,
              retry_count: 0,
            },
          },
        },
        { upsert: true }
      );

      this.logger.debug(`Campaign execution record created/updated for campaign ${campaignId}`);
    } catch (error) {
      this.logger.error(`Failed to create/update campaign execution for campaign ${campaignId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark campaign as completed
   */
  async markCampaignCompleted(campaignId: string): Promise<void> {
    try {
      const endTime = new Date();
      
      await this.campaignExecutionModel.updateOne(
        { campaign_id: campaignId },
        {
          $set: {
            status: 'COMPLETED',
            completed_at: endTime,
            is_active: false,
            'execution_stats.end_time': endTime,
          },
        }
      );

      this.logger.debug(`Campaign marked as completed: ${campaignId}`);
    } catch (error) {
      this.logger.error(`Failed to mark campaign as completed: ${campaignId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark campaign as failed
   */
  async markCampaignFailed(campaignId: string, errorMessage: string): Promise<void> {
    try {
      const failedAt = new Date();
      
      await this.campaignExecutionModel.updateOne(
        { campaign_id: campaignId },
        {
          $set: {
            status: 'FAILED',
            is_active: false,
            'execution_stats.end_time': failedAt,
          },
          $push: {
            error_logs: {
              timestamp: failedAt,
              level: 'ERROR',
              message: errorMessage,
              details: { campaignId }
            }
          }
        }
      );

      this.logger.debug(`Campaign marked as failed: ${campaignId}`);
    } catch (error) {
      this.logger.error(`Failed to mark campaign as failed: ${campaignId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
