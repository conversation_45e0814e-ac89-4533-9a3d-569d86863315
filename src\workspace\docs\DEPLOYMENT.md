# Workspace Module Deployment Guide

## Overview

This guide covers deployment strategies, configuration, and best practices for the Workspace Module in production environments.

## Prerequisites

### System Requirements
- Node.js 18+ 
- PostgreSQL 13+ (via Supabase)
- <PERSON><PERSON> (optional, for caching)
- Docker (optional, for containerization)

### Environment Variables
```bash
# Database
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Application
NODE_ENV=production
PORT=3000
JWT_SECRET=your_jwt_secret

# Security
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100
```

## Deployment Options

### 1. Traditional Server Deployment

#### Setup Steps
```bash
# Clone repository
git clone <repository-url>
cd automate-whatsapp-backend

# Install dependencies
npm ci --production

# Build application
npm run build

# Start application
npm run start:prod
```

#### Process Management
```bash
# Using PM2
npm install -g pm2
pm2 start dist/main.js --name "workspace-api"
pm2 startup
pm2 save
```

### 2. Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "run", "start:prod"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  workspace-api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
```

### 3. Cloud Deployment

#### AWS ECS
```yaml
# task-definition.json
{
  "family": "workspace-api",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "workspace-api",
      "image": "your-account.dkr.ecr.region.amazonaws.com/workspace-api:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "SUPABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:supabase-url"
        }
      ]
    }
  ]
}
```

#### Google Cloud Run
```yaml
# cloudbuild.yaml
steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/workspace-api', '.']
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/workspace-api']
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'workspace-api',
      '--image', 'gcr.io/$PROJECT_ID/workspace-api',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated'
    ]
```

## Configuration

### Production Configuration
```typescript
// config/production.ts
export const productionConfig = {
  port: process.env.PORT || 3000,
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['https://yourdomain.com'],
    credentials: true
  },
  rateLimit: {
    ttl: parseInt(process.env.RATE_LIMIT_TTL) || 60,
    limit: parseInt(process.env.RATE_LIMIT_MAX) || 100
  },
  supabase: {
    url: process.env.SUPABASE_URL,
    anonKey: process.env.SUPABASE_ANON_KEY,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY
  }
};
```

### Health Checks
```typescript
// health.controller.ts
@Controller('health')
export class HealthController {
  @Get()
  check() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage()
    };
  }
}
```

## Monitoring

### Application Monitoring
```typescript
// monitoring.service.ts
@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);

  logMetrics(operation: string, duration: number, success: boolean) {
    this.logger.log({
      operation,
      duration,
      success,
      timestamp: new Date().toISOString()
    });
  }
}
```

### Error Tracking
```typescript
// error.interceptor.ts
@Injectable()
export class ErrorInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError(error => {
        // Log error to monitoring service
        this.monitoringService.logError(error);
        throw error;
      })
    );
  }
}
```

## Security

### SSL/TLS Configuration
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Security Headers
```typescript
// security.middleware.ts
export function securityMiddleware(req: Request, res: Response, next: NextFunction) {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  next();
}
```

## Performance Optimization

### Caching Strategy
```typescript
// cache.service.ts
@Injectable()
export class CacheService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async get<T>(key: string): Promise<T | undefined> {
    return this.cacheManager.get<T>(key);
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    await this.cacheManager.set(key, value, ttl);
  }
}
```

### Database Optimization
```sql
-- Indexes for performance
CREATE INDEX idx_workspace_members_workspace_id ON workspace_members(workspace_id);
CREATE INDEX idx_workspace_members_user_id ON workspace_members(user_id);
CREATE INDEX idx_user_profiles_workspace_id ON user_profiles(workspace_id);
```

## Backup & Recovery

### Database Backup
```bash
# Supabase backup (automated)
# Configure in Supabase dashboard

# Manual backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql
```

### Application Backup
```bash
# Code backup
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# Configuration backup
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/
```

## Scaling

### Horizontal Scaling
```yaml
# docker-compose.scale.yml
version: '3.8'
services:
  workspace-api:
    build: .
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
```

### Load Balancing
```nginx
# nginx load balancer
upstream workspace_api {
    server localhost:3000;
    server localhost:3001;
    server localhost:3002;
}

server {
    location / {
        proxy_pass http://workspace_api;
    }
}
```

## Troubleshooting

### Common Issues
1. **Database Connection**: Check Supabase credentials
2. **Memory Issues**: Monitor memory usage and scale accordingly
3. **Rate Limiting**: Adjust rate limits based on usage patterns
4. **CORS Issues**: Verify CORS configuration

### Log Analysis
```bash
# View application logs
pm2 logs workspace-api

# Monitor system resources
htop
iostat -x 1
```

## Maintenance

### Regular Tasks
- Update dependencies monthly
- Monitor security advisories
- Review and rotate secrets
- Backup verification
- Performance monitoring

### Update Process
```bash
# Update application
git pull origin main
npm ci
npm run build
pm2 restart workspace-api

# Verify deployment
curl -f http://localhost:3000/health
```


