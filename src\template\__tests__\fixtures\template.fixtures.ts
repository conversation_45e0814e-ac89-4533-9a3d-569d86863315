export const templateFixtures = {
  validTemplate: {
    name: 'welcome_message',
    description: 'Welcome message for new users',
    type: 'text',
    content: 'Welcome {{1}} to our platform!',
    language: 'en',
    category: 'UTILITY',
    waba_id: '123456789',
    buttons: [],
    sections: [],
    variables: { name: 'string' },
    headerText: 'Welcome',
    footer: 'Thank you for joining us!',
  },

  invalidTemplate: {
    name: '',
    description: '',
    type: 'invalid',
    content: '',
    language: 'invalid',
    category: 'INVALID',
    waba_id: '',
  },

  validUpdate: {
    name: 'updated_welcome_message',
    description: 'Updated welcome message',
    content: 'Updated welcome {{1}} to our platform!',
  },

  validQuery: {
    search: 'welcome',
    category: 'UTILITY',
    type: 'text',
    language: 'en',
    status: 'APPROVED',
    waba_id: '123456789',
    page: '1',
    limit: '10',
  },

  validAiTemplate: {
    prompt: 'Create a welcome message for new customers',
    waba_id: '123456789',
    language: 'en',
    category: 'MARKETING',
  },

  validVoiceTemplate: {
    audioFile: 'mock-audio-file',
    waba_id: '123456789',
    language: 'en',
    category: 'UTILITY',
  },

  mockRequest: {
    user: {
      id: 'user-123',
      email: '<EMAIL>',
      workspace_id: 'workspace-123',
    },
    headers: {
      authorization: 'Bearer mock-jwt-token',
    },
  },

  mockTemplate: {
    _id: 'template-123',
    name: 'welcome_message',
    description: 'Welcome message for new users',
    type: 'text',
    content: 'Welcome {{1}} to our platform!',
    language: 'en',
    category: 'UTILITY',
    waba_id: '123456789',
    user_id: 'user-123',
    workspace_id: 'workspace-123',
    meta_template_id: 'meta-template-123',
    meta_template_status: 'APPROVED',
    buttons: [],
    sections: [],
    variables: { name: 'string' },
    headerText: 'Welcome',
    footer: 'Thank you for joining us!',
    created_at: new Date('2024-01-01T00:00:00.000Z'),
    updated_at: new Date('2024-01-01T00:00:00.000Z'),
  },

  mockMetaTemplate: {
    id: 'meta-template-123',
    name: 'welcome_message',
    status: 'APPROVED',
    category: 'UTILITY',
    language: 'en',
    components: [
      {
        type: 'HEADER',
        format: 'TEXT',
        text: 'Welcome',
      },
      {
        type: 'BODY',
        text: 'Welcome {{1}} to our platform!',
      },
      {
        type: 'FOOTER',
        text: 'Thank you for joining us!',
      },
    ],
  },

  mockMetaResponse: {
    id: 'meta-template-123',
    status: 'PENDING',
    category: 'UTILITY',
    name: 'welcome_message',
    language: 'en',
    components: [
      {
        type: 'HEADER',
        format: 'TEXT',
        text: 'Welcome',
      },
      {
        type: 'BODY',
        text: 'Welcome {{1}} to our platform!',
      },
      {
        type: 'FOOTER',
        text: 'Thank you for joining us!',
      },
    ],
  },

  mockAiResponse: {
    name: 'ai_generated_welcome',
    content: 'Hello {{1}}, welcome to our amazing platform!',
    type: 'text',
    category: 'MARKETING',
    language: 'en',
    description: 'AI-generated welcome message',
    buttons: [],
    sections: [],
    variables: { name: 'string' },
  },

  mockTranscriptionResponse: {
    text: 'Hello, welcome to our platform. We are excited to have you here.',
    confidence: 0.95,
    language: 'en',
  },

  mockFileUpload: {
    fieldname: 'audioFile',
    originalname: 'welcome_audio.mp3',
    encoding: '7bit',
    mimetype: 'audio/mpeg',
    size: 1024000,
    buffer: Buffer.from('mock audio data'),
  },

  mockWorkspace: {
    _id: 'workspace-123',
    name: 'Test Workspace',
    description: 'Test workspace for templates',
    owner_id: 'user-123',
    members: ['user-123', 'user-456'],
    created_at: new Date('2024-01-01T00:00:00.000Z'),
    updated_at: new Date('2024-01-01T00:00:00.000Z'),
  },

  mockUser: {
    _id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    workspace_id: 'workspace-123',
    role: 'admin',
    created_at: new Date('2024-01-01T00:00:00.000Z'),
    updated_at: new Date('2024-01-01T00:00:00.000Z'),
  },

  mockMetaCredentials: {
    _id: 'credentials-123',
    user_id: 'user-123',
    workspace_id: 'workspace-123',
    access_token: 'mock-access-token',
    refresh_token: 'mock-refresh-token',
    token_expires_at: new Date('2024-12-31T23:59:59.000Z'),
    is_active: true,
    created_at: new Date('2024-01-01T00:00:00.000Z'),
    updated_at: new Date('2024-01-01T00:00:00.000Z'),
  },

  mockErrorResponse: {
    status: 'error',
    code: 400,
    message: 'Validation failed',
    timestamp: '2024-01-01T00:00:00.000Z',
  },

  mockSuccessResponse: {
    status: 'success',
    code: 200,
    message: 'Operation completed successfully',
    data: {},
    timestamp: '2024-01-01T00:00:00.000Z',
  },

  mockPagination: {
    page: 1,
    limit: 10,
    total: 100,
    totalPages: 10,
    hasNext: true,
    hasPrev: false,
  },

  mockTemplateList: [
    {
      _id: 'template-1',
      name: 'welcome_message',
      type: 'text',
      category: 'UTILITY',
      status: 'APPROVED',
      created_at: new Date('2024-01-01T00:00:00.000Z'),
    },
    {
      _id: 'template-2',
      name: 'order_confirmation',
      type: 'text',
      category: 'UTILITY',
      status: 'PENDING',
      created_at: new Date('2024-01-02T00:00:00.000Z'),
    },
    {
      _id: 'template-3',
      name: 'promotional_offer',
      type: 'interactive',
      category: 'MARKETING',
      status: 'APPROVED',
      created_at: new Date('2024-01-03T00:00:00.000Z'),
    },
  ],

  mockMetaTemplateList: [
    {
      id: 'meta-template-1',
      name: 'welcome_message',
      status: 'APPROVED',
      category: 'UTILITY',
      language: 'en',
    },
    {
      id: 'meta-template-2',
      name: 'order_confirmation',
      status: 'PENDING',
      category: 'UTILITY',
      language: 'en',
    },
    {
      id: 'meta-template-3',
      name: 'promotional_offer',
      status: 'APPROVED',
      category: 'MARKETING',
      language: 'en',
    },
  ],

  mockSyncResult: {
    synced: [
      {
        template_id: 'template-1',
        template_name: 'welcome_message',
        meta_template_id: 'meta-template-1',
        status: 'success',
      },
    ],
    errors: [],
    total_meta_templates: 3,
    new_templates_found: 1,
    successful_syncs: 1,
    failed_syncs: 0,
  },

  mockValidationError: {
    status: 'error',
    code: 400,
    message: 'Template name is required',
    timestamp: '2024-01-01T00:00:00.000Z',
  },

  mockNotFoundError: {
    status: 'error',
    code: 404,
    message: 'Template not found',
    timestamp: '2024-01-01T00:00:00.000Z',
  },

  mockConflictError: {
    status: 'error',
    code: 409,
    message: 'Template with this name already exists',
    timestamp: '2024-01-01T00:00:00.000Z',
  },

  mockUnauthorizedError: {
    status: 'error',
    code: 401,
    message: 'Unauthorized access',
    timestamp: '2024-01-01T00:00:00.000Z',
  },

  mockInternalServerError: {
    status: 'error',
    code: 500,
    message: 'Internal server error',
    timestamp: '2024-01-01T00:00:00.000Z',
  },
};
