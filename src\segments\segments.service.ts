import { Injectable, BadRequestException, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Segment } from 'src/schema/segment.schema';
import { Contact } from 'src/schema/contacts.schema';
import { SupabaseService } from 'src/supabase/supabase.service';
import { CreateSegmentDto, UpdateSegmentDto, SegmentQueryDto } from './dto';
import { SegmentsResponseUtil, SegmentsResponseData } from './utils/segments-response.util';
import { SegmentsValidationUtil } from './utils/segments-validation.util';
import { SEGMENTS_CONSTANTS } from './utils/segments-constants.util';

/**
 * Refactored SegmentsService with improved structure and consistent response handling
 */
@Injectable()
export class SegmentsService {
  private readonly logger = new Logger(SegmentsService.name);
  
  constructor(
    @InjectModel(Segment.name) private segmentModel: Model<Segment>,
    @InjectModel(Contact.name) private contactModel: Model<Contact>,
    private readonly supabaseService: SupabaseService,
  ) {}

  // ==================== PUBLIC METHODS ====================

  /**
   * Create segment
   */
  async createSegment(createDto: CreateSegmentDto, req: any): Promise<any> {
    try {
      this.logger.log('Starting segment creation process');

      const user = SegmentsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = SegmentsValidationUtil.validateUserProfile(userProfile, userProfileError);

      // Validate and prepare data
      const payload = SegmentsValidationUtil.validateSegmentCreationData(createDto, user.id, workspaceId);

      // Check for duplicate name in workspace
      const existing = await this.segmentModel.findOne({ workspaceId, name: payload.name });
      if (existing) {
        return SegmentsResponseUtil.createDuplicateErrorResponse(
          SEGMENTS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_SEGMENT_NAME
        );
      }

      // Normalize rules for database storage
      if (payload.rules) {
        payload.rules = this.normalizeRulesForStorage(payload.rules);
      }

      // Normalize condition for database storage
      if (createDto.condition) {
        payload.condition = this.normalizeConditionForStorage(createDto.condition);
      }

      const segment = new this.segmentModel(payload);
      
      let savedSegment;
      try {
        savedSegment = await segment.save();
      } catch (error: any) {
        // Handle duplicate key error from Mongo unique index
        if (error?.code === 11000) {
          return SegmentsResponseUtil.createDuplicateErrorResponse(
            SEGMENTS_CONSTANTS.ERROR_MESSAGES.DUPLICATE_SEGMENT_NAME
          );
        }
        this.logger.error('Failed to create segment', error);
        throw new BadRequestException(SEGMENTS_CONSTANTS.ERROR_MESSAGES.SEGMENT_CREATION_FAILED);
      }

      this.logger.log(`Segment created successfully: ${savedSegment._id}`);

      const responseData = SegmentsResponseUtil.createSegmentCreationData(savedSegment);
      return SegmentsResponseUtil.createSuccessResponse(
        responseData,
        SEGMENTS_CONSTANTS.SUCCESS_MESSAGES.SEGMENT_CREATED,
        SEGMENTS_CONSTANTS.HTTP_STATUS.CREATED
      );

    } catch (error) {
      this.logger.error('Create segment failed:', error);
      this.handleSegmentsError(error);
    }
  }

  /**
   * Get all segments for workspace
   */
  async getSegmentsForWorkspace(req: any): Promise<any> {
    try {
      this.logger.log('Starting get segments for workspace process');

      const user = SegmentsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = SegmentsValidationUtil.validateUserProfile(userProfile, userProfileError);

      const segments = await this.segmentModel
        .find({ workspaceId })
        .sort({ createdAt: -1 })
        .lean();

      this.logger.log(`Segments fetched successfully: ${segments.length} total segments`);

      const responseData = SegmentsResponseUtil.createSegmentsListData(segments);
      return SegmentsResponseUtil.createSuccessResponse(
        responseData,
        SEGMENTS_CONSTANTS.SUCCESS_MESSAGES.SEGMENTS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get segments for workspace failed:', error);
      this.handleSegmentsError(error);
    }
  }

  private buildQueryFromRules(rules: any[], combine: 'all' | 'any', workspaceId: number): any {
    const parts: any[] = [];
    for (const r of rules) {
      const field = String(r.field || '').trim();
      const op = String(r.operator || '').trim();
      const val = r.value;

      if (!field || !op) continue;
      let clause: any = undefined;

      const toRegex = (v: any, flags = 'i') => new RegExp(String(v).replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), flags);

      switch (op) {
        case 'equals':
          clause = { [field]: val };
          break;
        case 'notEquals':
          clause = { [field]: { $ne: val } };
          break;
        case 'contains':
          clause = { [field]: { $regex: toRegex(val) } };
          break;
        case 'notContains':
          clause = { [field]: { $not: toRegex(val) } };
          break;
        case 'startsWith':
          clause = { [field]: { $regex: new RegExp('^' + String(val), 'i') } };
          break;
        case 'endsWith':
          clause = { [field]: { $regex: new RegExp(String(val) + '$', 'i') } };
          break;
        case 'regex':
          clause = { [field]: { $regex: new RegExp(String(val)) } };
          break;
        case 'exists':
          clause = { [field]: { $exists: !!val } };
          break;
        case 'in':
          clause = { [field]: { $in: Array.isArray(val) ? val : [val] } };
          break;
        case 'hasAnyTag':
          clause = { tagsId: { $in: Array.isArray(val) ? val : [val] } };
          break;
        case 'hasAllTags':
          clause = { tagsId: { $all: Array.isArray(val) ? val : [val] } };
          break;
        default:
          break;
      }
      if (clause) parts.push(clause);
    }

    const base = { workspaceId } as any;
    if (parts.length === 0) return base;
    if (combine === 'any') return { ...base, $or: parts };
    return { ...base, $and: parts };
  }

  /**
   * Get contacts by segment
   */
  async getContactsBySegment(segmentId: string, queryDto: SegmentQueryDto, req: any): Promise<any> {
    try {
      this.logger.log(`Starting get contacts by segment process for ID: ${segmentId}`);

      const user = SegmentsValidationUtil.validateUserContext(req);
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      const workspaceId = SegmentsValidationUtil.validateUserProfile(userProfile, userProfileError);

      const segment = await this.segmentModel.findOne({ _id: segmentId, workspaceId }).lean();
      SegmentsValidationUtil.validateSegmentExists(segment, segmentId);

      // Build contact query from rules or legacy condition
      const query = this.buildContactQuery(segment, workspaceId);

      const { page, limit } = SegmentsValidationUtil.validatePaginationParams(
        queryDto.page,
        queryDto.limit
      );
      const skip = (page - 1) * limit;

      const [contacts, total] = await Promise.all([
        this.contactModel.find(query).skip(skip).limit(limit).sort({ createdAt: -1 }).lean(),
        this.contactModel.countDocuments(query),
      ]);

      this.logger.log(`Contacts fetched successfully: ${total} total contacts for segment ${segmentId}`);

      const pagination = SegmentsResponseUtil.createPaginationMetadata(page, limit, total);
      const responseData = SegmentsResponseUtil.createContactsListData(contacts, pagination);

      return SegmentsResponseUtil.createSuccessResponse(
        responseData,
        SEGMENTS_CONSTANTS.SUCCESS_MESSAGES.CONTACTS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get contacts by segment failed:', error);
      this.handleSegmentsError(error);
    }
  }

  /**
   * Get segment operators
   */
  async getSegmentOperators(): Promise<any> {
    try {
      this.logger.log('Starting get segment operators process');

      const operators = [
        { key: 'equals', label: 'Equals', valueType: 'any', description: 'Field value equals the given value' },
        { key: 'notEquals', label: 'Not equals', valueType: 'any', description: 'Field value is not equal to the given value' },
        { key: 'contains', label: 'Contains', valueType: 'string', description: 'Field contains the given substring (case-insensitive)', applicableFields: ['firstName','lastName','chatName','email'] },
        { key: 'notContains', label: 'Does not contain', valueType: 'string', description: 'Field does not contain the given substring (case-insensitive)', applicableFields: ['firstName','lastName','chatName','email'] },
        { key: 'startsWith', label: 'Starts with', valueType: 'string', description: 'Field starts with the given prefix (case-insensitive)' },
        { key: 'endsWith', label: 'Ends with', valueType: 'string', description: 'Field ends with the given suffix (case-insensitive)' },
        { key: 'regex', label: 'Regex', valueType: 'string', description: 'Field matches the provided regular expression' },
        { key: 'exists', label: 'Exists', valueType: 'boolean', description: 'Checks whether the field exists' },
        { key: 'in', label: 'In list', valueType: 'array', description: 'Field value is one of the provided values' },
        { key: 'hasAnyTag', label: 'Has any tag', valueType: 'array', description: 'Contact has any of the provided tag IDs', applicableFields: ['tagsId'] },
        { key: 'hasAllTags', label: 'Has all tags', valueType: 'array', description: 'Contact has all of the provided tag IDs', applicableFields: ['tagsId'] },
      ];

      const responseData = SegmentsResponseUtil.createOperatorsListData(operators);
      return SegmentsResponseUtil.createSuccessResponse(
        responseData,
        SEGMENTS_CONSTANTS.SUCCESS_MESSAGES.OPERATORS_FETCHED
      );

    } catch (error) {
      this.logger.error('Get segment operators failed:', error);
      this.handleSegmentsError(error);
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Normalizes rules for database storage
   */
  private normalizeRulesForStorage(rules: any[]): any[] {
    return rules.map((r: any) => {
      const rule = { field: String(r.field || ''), operator: String(r.operator || ''), value: r.value } as any;
      
      if ((rule.operator === 'in' || rule.operator === 'hasAnyTag' || rule.operator === 'hasAllTags') && Array.isArray(rule.value)) {
        rule.value = rule.value.map((v: any) => v);
      }
      
      if ((rule.field === 'tagsId') && (rule.operator === 'hasAnyTag' || rule.operator === 'hasAllTags' || rule.operator === 'in')) {
        if (Array.isArray(rule.value)) {
          rule.value = rule.value.map((id: string) => new Types.ObjectId(id));
        } else if (rule.value) {
          rule.value = [new Types.ObjectId(rule.value)];
        }
      }
      
      return rule;
    });
  }

  /**
   * Normalizes condition for database storage
   */
  private normalizeConditionForStorage(condition: any): any {
    const normalized = { ...condition };
    if (Array.isArray(normalized.tagIds)) {
      normalized.tagIds = normalized.tagIds
        .filter((id: string) => !!id)
        .map((id: string) => new Types.ObjectId(id));
    }
    return normalized;
  }

  /**
   * Builds contact query from segment rules or condition
   */
  private buildContactQuery(segment: any, workspaceId: number): any {
    let query: any = { workspaceId };
    
    if (Array.isArray(segment.rules) && segment.rules.length > 0) {
      query = this.buildQueryFromRules(segment.rules, segment.match || 'all', workspaceId);
    } else if (segment.condition) {
      const condition: any = segment.condition || {};
      if (Array.isArray(condition.tagIds) && condition.tagIds.length > 0) {
        query.tagsId = { $in: condition.tagIds };
      }
      if (condition.subscribed !== undefined) {
        query.subscribed = !!condition.subscribed;
      }
      if (condition.nameContains && String(condition.nameContains).trim()) {
        const regex = new RegExp(String(condition.nameContains).trim(), 'i');
        query.$or = [
          { firstName: regex },
          { lastName: regex },
          { chatName: regex },
        ];
      }
      if (condition.emailContains && String(condition.emailContains).trim()) {
        const regex = new RegExp(String(condition.emailContains).trim(), 'i');
        query.email = { $regex: regex };
      }
    }
    
    return query;
  }

  /**
   * Handles segments-related errors
   */
  private handleSegmentsError(error: any): void {
    if (error instanceof BadRequestException || 
        error instanceof NotFoundException || 
        error instanceof ConflictException) {
      throw error;
    }
    
    this.logger.error('Unexpected segments error:', error);
    throw new BadRequestException('Internal server error');
  }
}


