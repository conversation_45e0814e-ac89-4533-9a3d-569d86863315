import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

import { SupabaseService } from '../supabase/supabase.service';

@Injectable()
export class StorageService {
    private readonly logger = new Logger(StorageService.name);
    private readonly s3: S3Client;
    private readonly bucket: string;
    private readonly endpoint: string;
    private readonly publicBaseUrl: string;

    constructor(private readonly supabaseService: SupabaseService) {
        this.endpoint ='s3.ap-southeast-1.wasabisys.com';
        this.bucket =  'automate-chat';
        const region = 'ap-southeast-1';
        const accessKeyId ='ZQ7NANJ7Z8PEN2V1ZIL2';
        const secretAccessKey ='2NPZ2JikK49d6dZmCjf6JpeeENBluFNvhQdAbz0B';

        if (!this.bucket || !accessKeyId || !secretAccessKey) {
            this.logger.warn('Wasabi S3 credentials or bucket not fully configured');
        }
      

       

        this.s3 = new S3Client({
            region,
            endpoint: `https://${this.endpoint}`,
            forcePathStyle: true,
            credentials: { accessKeyId, secretAccessKey },
        });

		// Public URL base: https://{bucket}.{endpoint}
		this.publicBaseUrl = `https://${this.bucket}.${this.endpoint}`;
    }

    async uploadForWorkspace(userId: string, file: any) {
        if (!file || !file.buffer || !file.originalname) {
            throw new BadRequestException('File is required');
        }

        const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(userId);
        if (userProfileError || !userProfile?.workspace_id) {
            throw new BadRequestException('User workspace not found');
        }
        const workspaceId = userProfile.workspace_id;

        const ext = (file.originalname.split('.').pop() || 'bin').toLowerCase();
        const timestamp = Date.now();
        const key = `workspaces/${workspaceId}/${timestamp}-${Math.random().toString(36).slice(2)}.${ext}`;

        const contentType = file.mimetype || 'application/octet-stream';

		try {
			await this.s3.send(
				new PutObjectCommand({
					Bucket: this.bucket,
					Key: key,
					Body: file.buffer,
					ContentType: contentType,
				}),
			);
		} catch (e) {
            this.logger.error('Failed to upload to Wasabi', e);
            throw new BadRequestException('Upload failed');
        }

		// Generate a short-lived pre-signed URL so the private object can be accessed
		const defaultExpirySeconds = 60 * 60; // 1 hour
		const presignedUrl = await this.getPresignedUrlForKey(key, defaultExpirySeconds);

		const publicUrl = `${this.publicBaseUrl}/${encodeURI(key)}`;
		return { key, publicUrl, presignedUrl, expiresIn: defaultExpirySeconds };
    }

	async getPresignedUrlForKey(objectKey: string, expiresIn: number) {
		try {
			const command = new GetObjectCommand({
				Bucket: this.bucket,
				Key: objectKey,
			});
			const url = await getSignedUrl(this.s3, command, { expiresIn });
			return url;
		} catch (err) {
			this.logger.error('Error generating pre-signed URL', err as any);
			throw new BadRequestException('Failed to create pre-signed URL');
		}
	}
}


