import { HttpStatus } from '@nestjs/common';

export interface StandardMetaOnboardingResponse {
  status: 'success' | 'error';
  code: number;
  message: string;
  data?: any;
  error?: any;
  timestamp: string;
}

export interface MetaOnboardingResponseData {
  credentials?: any;
  credentialsList?: any[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class MetaOnboardingResponseUtil {
  static createSuccessResponse(
    data: MetaOnboardingResponseData | any,
    message: string = 'Operation completed successfully',
    code: number = HttpStatus.OK,
  ): StandardMetaOnboardingResponse {
    return {
      status: 'success',
      code,
      message,
      data,
      timestamp: new Date().toISOString(),
    };
  }

  static createErrorResponse(
    message: string,
    code: number = HttpStatus.BAD_REQUEST,
    error?: any,
  ): StandardMetaOnboardingResponse {
    return {
      status: 'error',
      code,
      message,
      error: error?.message || error,
      timestamp: new Date().toISOString(),
    };
  }

  static createDuplicateErrorResponse(message: string): StandardMetaOnboardingResponse {
    return MetaOnboardingResponseUtil.createErrorResponse(message, HttpStatus.CONFLICT);
  }

  static createValidationErrorResponse(message: string, details?: any): StandardMetaOnboardingResponse {
    return MetaOnboardingResponseUtil.createErrorResponse(message, HttpStatus.BAD_REQUEST, details);
  }

  static createNotFoundErrorResponse(message: string): StandardMetaOnboardingResponse {
    return MetaOnboardingResponseUtil.createErrorResponse(message, HttpStatus.NOT_FOUND);
  }

  static createUnauthorizedErrorResponse(message: string): StandardMetaOnboardingResponse {
    return MetaOnboardingResponseUtil.createErrorResponse(message, HttpStatus.UNAUTHORIZED);
  }

  static createForbiddenErrorResponse(message: string): StandardMetaOnboardingResponse {
    return MetaOnboardingResponseUtil.createErrorResponse(message, HttpStatus.FORBIDDEN);
  }

  // Credentials-specific response data creators
  static createCredentialsCreationData(credentials: any): MetaOnboardingResponseData {
    return { credentials };
  }

  static createCredentialsListData(credentialsList: any[], pagination?: any): MetaOnboardingResponseData {
    return { credentialsList, pagination };
  }

  // Pagination metadata creator
  static createPaginationMetadata(page: number, limit: number, total: number) {
    const totalPages = Math.ceil(total / limit);
    return { page, limit, total, totalPages };
  }
}
