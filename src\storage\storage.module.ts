import { Module } from '@nestjs/common';
import { StorageService } from './storage.service';
import { StorageController } from './storage.controller';
import { SupabaseModule } from '../supabase/supabase.module';
import { AuthModule } from 'src/auth/auth.module';
@Module({
    imports: [SupabaseModule,AuthModule],
    providers: [StorageService],
    controllers: [StorageController],
    exports: [StorageService],
})
export class StorageModule {}


