import { IsNotEmpty, IsOptional, IsString, IsUUID, IsNumber, IsBoolean, IsArray, IsDateString } from 'class-validator';

export class CreateWorkspaceDto {
  @IsString({ message: 'Workspace name must be a string' })
  @IsNotEmpty({ message: 'Workspace name is required' })
  name: string;
  
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  
  @IsString({ message: 'Industry must be a string' })
  industry?: string;


  @IsString({ message: 'Team size must be a string' })
  team_size?: string;

  @IsOptional()
  @IsString({ message: 'Referral code must be a string' })
  referral_code?: string;

  @IsOptional()
  @IsString()
  designation?: string;

} 