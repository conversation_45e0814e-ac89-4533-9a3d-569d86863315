# Queue Module Documentation

## Overview

The Queue Module provides a comprehensive message queuing system using Bull MQ (Redis-based) for handling WhatsApp campaign messages, scheduled messages, and retry logic. This module replaces the previous Kafka implementation with a more lightweight and cost-effective solution.

## Table of Contents

1. [Architecture](ARCHITECTURE.md)
2. [API Endpoints](API_ENDPOINTS.md)
3. [Security](SECURITY.md)
4. [Testing](TESTING.md)
5. [Deployment](DEPLOYMENT.md)

## Features

### Core Functionality
- ✅ **Campaign Message Queuing**: Process immediate campaign messages
- ✅ **Scheduled Message Queuing**: Handle delayed/scheduled messages
- ✅ **Retry Message Queuing**: Manage failed message retries
- ✅ **Queue Management**: Pause, resume, and clear queues
- ✅ **Performance Monitoring**: Queue statistics and metrics
- ✅ **Error Handling**: Comprehensive retry logic with exponential backoff
- ✅ **Job Prioritization**: Support for HIGH, NORMAL, and LOW priority messages
- ✅ **Automatic Cleanup**: Remove completed and failed jobs automatically
- ✅ **Message Status Tracking**: Track delivery status and campaign analytics
- ✅ **Campaign Execution Management**: Monitor campaign progress and statistics

### Advanced Features
- **Redis-based Queues**: Lightweight and fast message processing
- **Bull Dashboard Integration**: Built-in monitoring and management
- **Concurrent Processing**: Multiple workers for parallel message processing
- **Dead Letter Queue**: Handle permanently failed messages
- **Job Persistence**: Redis persistence for job data
- **Queue Statistics**: Real-time queue performance metrics

## Quick Start

### Installation

```bash
# Install dependencies (already included in package.json)
npm install

# Start Redis
npm run redis:up

# Test queue connection
npm run test:queue

# Start development server
npm run start:dev
```

### Basic Usage

#### Send Campaign Message

```typescript
import { QueueService } from './queue.service';

const queueService = new QueueService();

const message = await queueService.sendCampaignMessage({
  campaignId: 'campaign-123',
  contactId: 'contact-456',
  phoneNumber: '+1234567890',
  templateId: 'template-789',
  // ... other fields
}, req);
```

#### Schedule Message

```typescript
const scheduledMessage = await queueService.scheduleCampaignMessage(
  message,
  60000 // 1 minute delay
);
```

#### Retry Failed Message

```typescript
const retryMessage = await queueService.retryCampaignMessage(
  message,
  5000 // 5 second delay
);
```

## API Endpoints

### Queue Management
- `GET /queue/stats` - Get queue statistics
- `POST /queue/pause/:queueName` - Pause specific queue
- `POST /queue/resume/:queueName` - Resume specific queue
- `POST /queue/clear/:queueName` - Clear specific queue

## Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Queue Configuration
QUEUE_CONCURRENCY=5
QUEUE_MAX_ATTEMPTS=3
QUEUE_BACKOFF_DELAY=2000
```

### Queue Types

- `campaign-messages` - Immediate campaign message processing
- `scheduled-campaign-messages` - Delayed/scheduled message processing
- `campaign-retry-messages` - Failed message retry processing

### Message Priorities

- `HIGH` - Priority 10 (processed first)
- `NORMAL` - Priority 5 (default)
- `LOW` - Priority 1 (processed last)

## Examples

### Creating a Campaign Message

```json
{
  "campaignId": "campaign-123",
  "contactId": "contact-456",
  "phoneNumber": "+1234567890",
  "countryCode": "+1",
  "templateId": "template-789",
  "phoneNumberId": "phone-id-123",
  "variableMapping": {
    "body": { "1": "Hello World!" },
    "header": { "1": "Welcome" }
  },
  "workspaceId": 1,
  "userId": "user-123",
  "retryCount": 0,
  "priority": "NORMAL"
}
```

### Queue Statistics Response

```json
{
  "status": "success",
  "code": 200,
  "message": "Queue statistics retrieved successfully",
  "data": {
    "campaignMessages": {
      "waiting": 10,
      "active": 2,
      "completed": 100,
      "failed": 5
    },
    "scheduledMessages": {
      "waiting": 5,
      "active": 1,
      "completed": 50,
      "failed": 2
    },
    "retryMessages": {
      "waiting": 3,
      "active": 0,
      "completed": 20,
      "failed": 1
    },
    "total": {
      "waiting": 18,
      "active": 3,
      "completed": 170,
      "failed": 8
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Error Handling

The module provides comprehensive error handling with standardized error responses:

```json
{
  "status": "error",
  "code": 500,
  "message": "Failed to process queue message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Common Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `404` - Not Found (queue not found)
- `500` - Internal Server Error

## Testing

```bash
# Run all tests
npm test

# Run unit tests
npm run test:unit

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e

# Run with coverage
npm run test:coverage

# Test queue connection
npm run test:queue
```

## Documentation

- [Complete API Documentation](API_ENDPOINTS.md)
- [Architecture Overview](ARCHITECTURE.md)
- [Security Guidelines](SECURITY.md)
- [Testing Documentation](TESTING.md)
- [Deployment Guide](DEPLOYMENT.md)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Contact the development team

## Changelog

### v1.0.0
- Initial release
- Bull MQ implementation
- Campaign message queuing
- Scheduled message queuing
- Retry message queuing
- Queue management APIs
- Comprehensive documentation
- Full test coverage
