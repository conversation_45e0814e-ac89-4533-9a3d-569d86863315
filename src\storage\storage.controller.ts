import { Controller, Post, UploadedFile, UseGuards, UseInterceptors, Req, BadRequestException, Body } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { StorageService } from './storage.service';
import { AuthGuard } from '../auth/auth.guard';

@Controller('storage')
export class StorageController {
    constructor(private readonly storageService: StorageService) {}

    @Post('upload')
    @UseGuards(AuthGuard)
    @UseInterceptors(FileInterceptor('file'))
    async upload(@UploadedFile() file: any, @Req() req: any) {
        if (!req?.user?.id) throw new BadRequestException('User not found');
        const result = await this.storageService.uploadForWorkspace(req.user.id, file);
        return {
            status: 'success',
            code: 200,
            message: 'File uploaded',
            data: result,
            timestamp: new Date().toISOString(),
        };
    }

    @Post('presign')
    async presign(@Body() body: any) {
        const { key, expiresIn } = body || {};
        if (!key) throw new BadRequestException('key is required');
        const ttl = typeof expiresIn === 'number' && expiresIn > 0 ? expiresIn : 60 * 60; // default 1h
        const url = await this.storageService.getPresignedUrlForKey(key, ttl);
        return {
            status: 'success',
            code: 200,
            message: 'Pre-signed URL generated',
            data: { key, presignedUrl: url, expiresIn: ttl },
            timestamp: new Date().toISOString(),
        };
    }
}


