# Custom Fields Module Architecture

## Overview

The Custom Fields module follows the same architectural patterns as the Auth module, ensuring consistency across the application. It implements a clean, layered architecture with proper separation of concerns.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    Custom Fields Module                     │
├─────────────────────────────────────────────────────────────┤
│  Controller Layer                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              CustomFieldsController                     │ │
│  │  • HTTP endpoint handling                              │ │
│  │  • Request/Response transformation                     │ │
│  │  • Authentication guard integration                    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              CustomFieldsService                        │ │
│  │  • Business logic implementation                       │ │
│  │  • Data validation and processing                      │ │
│  │  • Database operations                                 │ │
│  │  • Error handling                                      │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Utility Layer                                              │
│  ┌─────────────────┬─────────────────┬─────────────────────┐ │
│  │   Constants     │   Validation    │     Response        │ │
│  │   Utility       │   Utility       │     Utility         │ │
│  └─────────────────┴─────────────────┴─────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  DTOs (Data Transfer Objects)                          │ │
│  │  • CreateCustomFieldDto                                │ │
│  │  • UpdateCustomFieldDto                                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Design Patterns

### 1. Layered Architecture

The module follows a three-layer architecture:

- **Controller Layer**: Handles HTTP requests and responses
- **Service Layer**: Contains business logic and data processing
- **Utility Layer**: Provides reusable validation, constants, and response utilities

### 2. Dependency Injection

Uses NestJS dependency injection for:
- Service dependencies
- Database models
- External services (Supabase)

### 3. Repository Pattern

MongoDB operations are abstracted through Mongoose models, providing:
- Data persistence abstraction
- Query optimization
- Schema validation

### 4. Utility Pattern

Reusable utility classes for:
- **Constants**: Centralized configuration and messages
- **Validation**: Input validation and business rules
- **Response**: Consistent API response formatting

## Component Details

### Controller Layer

**CustomFieldsController**
- Handles HTTP requests for custom field operations
- Integrates with AuthGuard for authentication
- Uses proper HTTP status codes
- Follows RESTful API conventions

**Key Responsibilities:**
- Request validation through DTOs
- Authentication and authorization
- Response formatting
- Error handling delegation to service layer

### Service Layer

**CustomFieldsService**
- Implements all business logic for custom field operations
- Handles data validation and processing
- Manages database operations
- Provides comprehensive error handling

**Key Methods:**
- `createCustomField()` - Creates new custom fields
- `getCustomFieldsForWorkspace()` - Retrieves workspace custom fields
- `getCustomFieldById()` - Retrieves specific custom field
- `updateCustomField()` - Updates existing custom fields
- `deleteCustomField()` - Removes custom fields

### Utility Layer

**CustomFieldsConstantsUtil**
- Centralized constants for error messages, success messages, and configuration
- Type-safe constant definitions
- Easy maintenance and updates

**CustomFieldsValidationUtil**
- Comprehensive input validation
- Business rule enforcement
- Reusable validation methods
- Type-specific validation logic

**CustomFieldsResponseUtil**
- Consistent API response formatting
- Standardized error responses
- Success response templates
- Response data transformation

### Data Layer

**DTOs (Data Transfer Objects)**
- `CreateCustomFieldDto` - Input validation for creation
- `UpdateCustomFieldDto` - Input validation for updates
- Class-validator decorators for automatic validation

## Data Flow

### 1. Create Custom Field Flow

```
Client Request → Controller → Service → Validation → Database → Response
     ↓              ↓          ↓          ↓           ↓          ↓
   HTTP POST    Auth Check  Business   Input      MongoDB    Success
   with DTO     + Guard     Logic     Validation   Save      Response
```

### 2. Read Custom Fields Flow

```
Client Request → Controller → Service → Database → Response
     ↓              ↓          ↓          ↓          ↓
   HTTP GET     Auth Check  Business   MongoDB    Formatted
   with Auth    + Guard     Logic     Query      Response
```

### 3. Update Custom Field Flow

```
Client Request → Controller → Service → Validation → Database → Response
     ↓              ↓          ↓          ↓           ↓          ↓
   HTTP PUT     Auth Check  Business   Input      MongoDB    Success
   with DTO     + Guard     Logic     Validation   Update    Response
```

### 4. Delete Custom Field Flow

```
Client Request → Controller → Service → Database → Response
     ↓              ↓          ↓          ↓          ↓
   HTTP DELETE  Auth Check  Business   MongoDB    Success
   with Auth    + Guard     Logic     Delete     Response
```

## Security Architecture

### Authentication & Authorization

- **AuthGuard Integration**: All endpoints protected by authentication
- **Workspace Scoping**: Users can only access custom fields from their workspace
- **User Context Validation**: Validates user identity and workspace membership

### Input Validation

- **DTO Validation**: Class-validator decorators for automatic validation
- **Business Rule Validation**: Custom validation utilities for complex rules
- **Type Safety**: TypeScript interfaces for compile-time type checking

### Data Protection

- **MongoDB Injection Prevention**: Mongoose ODM prevents NoSQL injection
- **Input Sanitization**: Validation utilities sanitize and validate inputs
- **Error Information**: Controlled error messages to prevent information leakage

## Error Handling Architecture

### Error Classification

1. **Validation Errors** (400) - Input validation failures
2. **Authentication Errors** (401) - Missing or invalid authentication
3. **Not Found Errors** (404) - Resource not found
4. **Conflict Errors** (409) - Duplicate resources
5. **Server Errors** (500) - Internal server errors

### Error Handling Flow

```
Error Occurrence → Service Layer → Error Classification → Response Utility → Client
       ↓                ↓                ↓                    ↓              ↓
   Exception        Log Error        Determine Type      Format Error    HTTP Response
   Thrown          + Context         + Status Code       + Message       with Status
```

## Performance Considerations

### Database Optimization

- **Indexes**: Unique index on `workspaceId + label` for fast lookups
- **Lean Queries**: Uses `.lean()` for read operations to reduce memory usage
- **Efficient Filtering**: Workspace-scoped queries for data isolation

### Caching Strategy

- **Response Caching**: Can be implemented at controller level
- **Database Caching**: MongoDB query result caching
- **Application Caching**: In-memory caching for frequently accessed data

### Scalability

- **Horizontal Scaling**: Stateless service design supports load balancing
- **Database Sharding**: Workspace-based data partitioning
- **Microservice Ready**: Modular design supports service extraction

## Testing Architecture

### Test Layers

1. **Unit Tests** - Individual component testing
2. **Integration Tests** - Service layer with database
3. **E2E Tests** - Complete API workflow testing

### Test Utilities

- **Mock Services** - Supabase service mocking
- **Test Data** - Factory functions for test data creation
- **Assertion Helpers** - Custom assertion utilities

## Monitoring & Observability

### Logging Strategy

- **Structured Logging** - JSON format for easy parsing
- **Log Levels** - Debug, Info, Warn, Error levels
- **Context Information** - User ID, workspace ID, operation type

### Metrics Collection

- **Performance Metrics** - Response times, throughput
- **Business Metrics** - Custom field creation rates, usage patterns
- **Error Metrics** - Error rates, error types

### Health Checks

- **Database Connectivity** - MongoDB connection status
- **Service Dependencies** - Supabase service availability
- **Resource Usage** - Memory, CPU utilization

## Future Enhancements

### Planned Features

1. **Bulk Operations** - Batch create/update/delete
2. **Field Templates** - Predefined field configurations
3. **Field Dependencies** - Conditional field display
4. **Audit Trail** - Change tracking and history
5. **API Versioning** - Backward compatibility support

### Architecture Evolution

1. **Event-Driven Architecture** - Custom field change events
2. **CQRS Pattern** - Separate read/write models
3. **GraphQL Support** - Flexible query interface
4. **Real-time Updates** - WebSocket integration
