import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type CampaignDocument = Campaign & Document;

@Schema({ timestamps: true })
export class Campaign {
  @Prop({ required: true })
  name: string;

  @Prop()
  description: string;

  @Prop({ required: true, type: String })
  created_by: string;

  @Prop({ required: true, type: Number })
  workspace_id: number;

  @Prop({ required: true, type: String })
  template_id: string;

  // Contact Selection
  @Prop({ 
    required: true, 
    enum: ['all_contacts', 'segmented', 'csv_contacts'],
    default: 'all_contacts'
  })
  contact_selection_type: string;

  @Prop({ type: [String] })
  segments: string[];

  @Prop({ type: Object })
  contact_filters: {
    include: {
      tags: string[];
      custom_fields: Record<string, any>;
      conditions: Array<{
        field: string;
        operator: string;
        value: any;
      }>;
    };
    exclude: {
      tags: string[];
      custom_fields: Record<string, any>;
      conditions: Array<{
        field: string;
        operator: string;
        value: any;
      }>;
    };
  };

  @Prop({ type: String })
  phone_number_id: string;

  @Prop({ type: String })
  csv_contacts_string: string; // Store CSV string for reference

  @Prop({ type: Object })
  csv_mapping: {
    phone_column: number;
    name_column?: number;
    email_column?: number;
    country_code_number:number;
    custom_field_mapping?: Record<string, number>;
  };

  // Template Variables Mapping
  @Prop({ type: Object })
  variable_mapping: {
    header: Record<string, string>;
    body: Record<string, string>;
    buttons: Record<string, string>;
    footer: Record<string, string>;
  };

  // Campaign Settings
  @Prop({ 
    required: true, 
    enum: ['DRAFT', 'SCHEDULED', 'ACTIVE', 'PAUSED', 'COMPLETED', 'FAILED', 'CANCELLED'],
    default: 'DRAFT'
  })
  status: string;

  @Prop({ 
    required: true, 
    enum: ['IMMEDIATE', 'SCHEDULED', 'RECURRING'],
    default: 'IMMEDIATE'
  })
  send_type: string;

  @Prop()
  scheduled_at: Date;

  @Prop({ type: Object })
  recurring_settings: {
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
    interval: number;
    days_of_week?: number[]; // 0-6 (Sunday-Saturday)
    day_of_month?: number; // 1-31
    end_date?: Date;
    max_occurrences?: number;
  };

  // Campaign Statistics
  @Prop({ default: 0 })
  total_contacts: number;

  @Prop({ default: 0 })
  sent_count: number;

  @Prop({ default: 0 })
  delivered_count: number;

  @Prop({ default: 0 })
  read_count: number;

  @Prop({ default: 0 })
  failed_count: number;

  @Prop({ default: 0 })
  pending_count: number;

  @Prop({ type: Object })
  delivery_stats: {
    sent: number;
    delivered: number;
    read: number;
    failed: number;
    pending: number;
    total: number;
  };

  @Prop({ type: [Object] })
  message_logs: Array<{
    contact_id?: Types.ObjectId | null; // ObjectId for database contacts, null for CSV contacts
    phone: string;
    status: 'PENDING' | 'SENT' | 'DELIVERED' | 'READ' | 'FAILED';
    sent_at?: Date;
    delivered_at?: Date;
    read_at?: Date;
    error_message?: string;
    message_id?: string;
    retry_count: number;
  }>;

  // Test Message Settings
  @Prop({ type: [String] })
  test_contacts: string[];

  @Prop({ default: false })
  test_sent: boolean;

  @Prop()
  test_sent_at: Date;

  // Campaign Settings
  @Prop({ default: 100 })
  batch_size: number;

  @Prop({ default: 1000 })
  rate_limit_per_hour: number;

  @Prop({ default: 3 })
  max_retries: number;

  @Prop({ default: 300000 }) // 5 minutes
  retry_delay_ms: number;

  // Timestamps
  @Prop()
  started_at: Date;

  @Prop()
  completed_at: Date;

  @Prop()
  paused_at: Date;

  @Prop()
  resumed_at: Date;

  @Prop({ default: false })
  is_active: boolean;

  @Prop({ default: false })
  is_deleted: boolean;
}

export const CampaignSchema = SchemaFactory.createForClass(Campaign);

// Indexes for better performance
CampaignSchema.index({ workspace_id: 1, status: 1 });
CampaignSchema.index({ created_by: 1, status: 1 });
CampaignSchema.index({ scheduled_at: 1, status: 1 });
CampaignSchema.index({ 'message_logs.contact_id': 1 });
CampaignSchema.index({ 'message_logs.phone': 1 });
