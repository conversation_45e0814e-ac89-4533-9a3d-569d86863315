import { Injectable, Logger } from '@nestjs/common';
import { CreateWorkspaceDto } from 'src/dto/create-workspace.dto';
import { CreateMemberdto } from 'src/dto/workspaceMemeber.dto';
import { CreateCompanyProfileDto, UpdateCompanyProfileDto } from 'src/dto/company-profile.dto';
import { CreateUserProfileCardDto, UpdateUserProfileCardDto } from 'src/dto/user-profile-card.dto';
import { UpdateMemberAccessDto } from 'src/dto/update-member-access.dto';
import { SupabaseService } from 'src/supabase/supabase.service';

@Injectable()
export class AutomateCardService {
    private readonly logger = new Logger(AutomateCardService.name);
    constructor(
        private readonly supabaseService: SupabaseService
    ) {}
    async createCardWorkspace(createWorkspaceDto: CreateWorkspaceDto, req: any, res: any): Promise<Response> {
        try {
          const user = req.user;
    
          // Check if user exists in user_profile table
          const {data:userProfile,error:userProfileError} = await this.supabaseService.getUserProfile(user.id);
          if (!userProfile || userProfileError) {
            return res.status(400).json({
              status: 'error',
              code: 400,
              message: 'User profile not found',
              timestamp: new Date().toISOString()
            });
          }
    
          // Check if user already has a workspace (due to unique constraint)
          const existingWorkspace = await this.supabaseService.getWorkspaceByCreatedBy(user.id);
          console.log("existingWorkspace", existingWorkspace);
          if (existingWorkspace) {
            return res.status(400).json({
              status: 'error',
              code: 400,
              message: 'User already has a workspace',
              timestamp: new Date().toISOString()
            });
          }
    
          // Prepare workspace data with user ID from request
          const workspaceData = {
            ...createWorkspaceDto,
            created_by: user.id,
            status: 'active',
            trial_expiry:   new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
            cards_access: true,
          };
          const result = await this.supabaseService.insertWorkspace(workspaceData);
          console.log("result", result)
          if (result.error) {
            this.logger.error('Failed to create workspace:', result.error);
            return res.status(400).json({
              status: 'error',
              code: 400,
              message: 'Failed to create workspace',
              timestamp: new Date().toISOString(),
              request_id: req.headers['x-request-id'] || 'unknown'
            });
          }
    
          // Update user profile with workspace ID
          const updateUserProfile = await this.supabaseService.updateUserProfile(user.id, {
            workspace_id: result.data.id,
            updated_at: new Date().toISOString()
          });
    
          if (updateUserProfile.error) {
            this.logger.error('Failed to update user profile:', updateUserProfile.error);
            
            // Rollback: Delete the workspace since user profile update failed
            const deleteWorkspace = await this.supabaseService.deleteWorkspace(result.data.id);
            
            if (deleteWorkspace.error) {
              this.logger.error('Failed to rollback workspace deletion:', deleteWorkspace.error);
              // Critical: Both workspace creation and rollback failed
              return res.status(500).json({
                status: 'error',
                code: 500,
                message: 'Critical error: Workspace created but profile update failed and rollback also failed. Please contact support immediately.',
                data: {
                  workspace_id: result.data.id,
                  user_id: user.id,
                  error_details: 'Both workspace creation and rollback failed'
                },
                timestamp: new Date().toISOString(),
                request_id: req.headers['x-request-id'] || 'unknown'
              });
            }
            
            // Rollback successful
            return res.status(500).json({
              status: 'error',
              code: 500,
              message: 'Failed to create workspace due to user profile update error. Please try again.',
              data: {
                user_id: user.id,
                error_details: 'User profile update failed, workspace rolled back'
              },
              timestamp: new Date().toISOString(),
              request_id: req.headers['x-request-id'] || 'unknown'
            });
          }
    
          // Add user as workspace member (owner/admin role)
          const insertWorkspaceMember = await this.supabaseService.insertWorkspaceMember({
            workspace_id: result.data.id,
            user_id: user.id,
            role: 'Admin', // or 'admin' based on your requirements
            status: 'active',
            cards_access: true
          });
    
          if (insertWorkspaceMember.error) {
            this.logger.error('Failed to add user as workspace member:', insertWorkspaceMember.error);
            
            // Rollback: Delete workspace and revert user profile
            const deleteWorkspace = await this.supabaseService.deleteWorkspace(result.data.id);
            const revertUserProfile = await this.supabaseService.updateUserProfile(user.id, {
              workspace_id: null,
              updated_at: new Date().toISOString()
            });
            
            return res.status(500).json({
              status: 'error',
              code: 500,
              message: 'Failed to create workspace due to member creation error. Please try again.',
              data: {
                user_id: user.id,
                error_details: 'Workspace member creation failed, workspace rolled back'
              },
              timestamp: new Date().toISOString(),
              request_id: req.headers['x-request-id'] || 'unknown'
            });
          }      
          return res.status(201).json({
            status: 'success',
            code: 201,
            message: 'Workspace created successfully with user as admin',
            data: {
              workspace: result.data,
              created_at: new Date().toISOString(),
              user_id: user.id,
              user_profile_updated: true,
              user_profile: updateUserProfile.data,
              workspace_member: insertWorkspaceMember.data,
            },
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.error('Create workspace failed:', error);
          
          return res.status(500).json({
            status: 'error',
            code: 500,
            message: 'Internal server error',
            timestamp: new Date().toISOString(),
            request_id: req.headers['x-request-id'] || 'unknown'
          });
        }
      }

    async addMemberToWorkspace(memberData: CreateMemberdto, req: any, res: any): Promise<Response> {
            try {
              const user = req.user;
            
              // Validate that the requesting user has a profile and get their workspace
              const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
              console.log("userProfile", userProfile);
              if (!userProfile || userProfileError) {
                return res.status(404).json({
                  status: 'error',
                  code: 404,
                  message: 'User profile not found',
                  timestamp: new Date().toISOString(),
                });
              }
        
              // Get the workspace_id from the requesting user's profile
              const workspaceId = userProfile.workspace_id;
              if (!workspaceId) {
                return res.status(400).json({
                  status: 'error',
                  code: 400,
                  message: 'You are not associated with any workspace',
                  timestamp: new Date().toISOString(),
                });
              }
        
        
        
              // Validate that the workspace exists
              const { data: workspace, error: workspaceError } = await this.supabaseService.getWorkspaceById(workspaceId);
              if (!workspace || workspaceError) {
                return res.status(404).json({
                  status: 'error',
                  code: 404,
                  message: 'Workspace not found',
                  timestamp: new Date().toISOString(),
                });
              }
        
            // Check if email already exists
              const existingUserByEmail = await this.supabaseService.getUserProfileByEmail(memberData.email);
              if (existingUserByEmail) {
                return res.status(400).json({
                  status: 'error',
                  code: 400,
                  message: 'User with this email already exists',
                  timestamp: new Date().toISOString(),
                });
              }
        
              // Create new user in Supabase Auth
              const signUpResult = await this.supabaseService.signUp(
                memberData.email,
                memberData.password,
                {
                  first_name: memberData.first_name,
                  last_name: memberData.last_name,
                  phoneNumber: memberData.phone,
                  countrycode: memberData.country_code,
                  country: memberData.country,
                }
              );
        
              if (signUpResult.error) {
                this.logger.error('Failed to create user in auth:', signUpResult.error);
                return res.status(500).json({
                  status: 'error',
                  code: 500,
                  message: 'Failed to create user account',
                  timestamp: new Date().toISOString(),
                });
              }
        
              const targetUserId = signUpResult.data.user?.id || null;
              if (!targetUserId) {
                return res.status(500).json({
                  status: 'error',
                  code: 500,
                  message: 'Failed to get user ID from auth creation',
                  timestamp: new Date().toISOString(),
                });
              }
        
              // Insert into user_profile
              const profileData = {
                id: targetUserId,
                email: memberData.email,
                first_name: memberData.first_name,
                last_name: memberData.last_name,
                phone: parseInt(memberData.phone),
                country: memberData.country,
                country_code: memberData.country_code,
                terms_conditions: true,
                workspace_id: workspaceId,
              };
        
              const { data: newProfile, error: profileError } = await this.supabaseService.insertUserProfile(profileData);
            
              if (profileError) {
                this.logger.error('Failed to create user profile:', profileError);
                return res.status(500).json({
                  status: 'error',
                  code: 500,
                  message: 'Failed to create user profile: ' + profileError.message,
                  timestamp: new Date().toISOString(),
                });
              }
        
              const targetUserProfile = newProfile;
        
                    // Check if the target user is already a member of this workspace
              const { data: existingMember, error: existingMemberError } = await this.supabaseService.getWorkspaceMember(
                workspaceId, 
                targetUserId
              );
            
              if (existingMember && !existingMemberError) {
                return res.status(400).json({
                  status: 'error',
                  code: 400,
                  message: 'User is already a member of this workspace',
                  timestamp: new Date().toISOString(),
                });
              }
        
                    // Insert into workspace_members
              const insertMemberData = {
                workspace_id: workspaceId,
                user_id: targetUserId,
                role: memberData.role,
                status: 'Active',
                reports_to: memberData.reports_to || null,
                cards_access: memberData.cards_access,
              };
        
              const { data: newMember, error: insertError } = await this.supabaseService.insertWorkspaceMember(insertMemberData);
            
              if (insertError) {
                this.logger.error('Failed to add workspace member:', insertError);
                return res.status(500).json({
                  status: 'error',
                  code: 500,
                  message: 'Failed to add member to workspace',
                  timestamp: new Date().toISOString(),
                });
              }

              // Create user profile cards for all company profiles in the workspace
              const profileCardResult = await this.createUserProfileCardsForWorkspace(
                targetUserId,
                workspaceId,
                {
                  first_name: memberData.first_name,
                  last_name: memberData.last_name,
                  email: memberData.email,
                  phone: memberData.phone,
                  role: memberData.role
                }
              );

              return res.status(201).json({
                status: 'success',
                code: 201,
                message: 'User registered and added to workspace successfully',
                data: {
                  user_id: targetUserId,
                  workspace_id: workspaceId,
                  member_role: memberData.role,
                  user_profile_cards: {
                    created: profileCardResult.successCount,
                    failed: profileCardResult.failedCount,
                    total_attempted: profileCardResult.successCount + profileCardResult.failedCount
                  }
                },
                timestamp: new Date().toISOString()
              });
        
            } catch (error) {
              this.logger.error('Add member to workspace failed:', error);
              return res.status(500).json({
                status: 'error',
                code: 500,
                message: 'Internal server error',
                timestamp: new Date().toISOString()
              });
            }
          }

  // Company Profile Methods
  async createCompanyProfile(createCompanyProfileDto: CreateCompanyProfileDto, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace to create company profile',
          timestamp: new Date().toISOString()
        });
      }

      // Check if company profile with same name already exists for this workspace
      const existingCompanyProfiles = await this.supabaseService.getCompanyProfileByWorkspaceId(userProfile.workspace_id);
      if (existingCompanyProfiles.data && existingCompanyProfiles.data.length > 0) {
        const existingProfile = existingCompanyProfiles.data.find(profile => 
          profile.company_name === createCompanyProfileDto.company_name
        );
        if (existingProfile) {
          return res.status(400).json({
            status: 'error',
            code: 400,
            message: 'Company profile with this name already exists for this workspace',
            timestamp: new Date().toISOString()
          });
        }
      }

      // Create company profile
      const companyProfileData = {
        ...createCompanyProfileDto,
        workspace_id: userProfile.workspace_id,
        created_by: user.id
      };
console.log("companyProfileData",companyProfileData)
      const { data: companyProfile, error: companyProfileError } = await this.supabaseService.insertCompanyProfile(companyProfileData);
      console.log("companyProfile",companyProfile)
      if (companyProfileError) {
        console.log("companyProfileError",companyProfileError)
        this.logger.error('Failed to create company profile:', companyProfileError);
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Failed to create company profile',
          timestamp: new Date().toISOString()
        });
      }

      // Get all workspace members
      const { data: workspaceMembers, error: membersError } = await this.supabaseService.getWorkspaceMembers(userProfile.workspace_id);
      
      if (membersError) {
        this.logger.error('Failed to get workspace members:', membersError);
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Failed to get workspace members',
          timestamp: new Date().toISOString()
        });
      }
console.log("workspaceMembers",workspaceMembers)
      // Create user profile cards for all workspace members
      const userProfilePromises = workspaceMembers.map(async (member) => {
        const userProfileCardData = {
          company_profile_id: companyProfile.id,
          user_id: member.user_id,
          full_name: member.user_profile?.first_name + ' ' + member.user_profile?.last_name,
          email: member.user_profile?.email,
          phone_number: member.user_profile?.phone?.toString(),
          designation: member.role || 'Member'
        };

        return this.supabaseService.insertUserProfileCard(userProfileCardData);
      });

      const userProfileResults = await Promise.all(userProfilePromises);
      const failedProfiles = userProfileResults.filter(result => result.error);
      const successCount = userProfileResults.length - failedProfiles.length;

      if (failedProfiles.length > 0) {
        this.logger.error('Some user profile cards failed to create:', failedProfiles);
        // Continue with success response but log the failures
      }

      return res.status(201).json({
        status: 'success',
        code: 201,
        message: 'Company profile created successfully with user profiles',
        data: {
          company_profile: companyProfile,
          user_profiles_created: successCount,
          failed_profiles: failedProfiles.length,
          total_workspace_members: workspaceMembers.length
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Create company profile failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async getCompanyProfile(req: any, res: any): Promise<Response> {
    try {
      const user = req.user;
      
      // Extract query parameters for search and pagination
      const { 
        search = '', 
        page = 1, 
        limit = 10, 
        sortBy = 'created_at', 
        sortOrder = 'desc' 
      } = req.query;

      // Validate pagination parameters
      const pageNum = Math.max(1, parseInt(page as string) || 1);
      const limitNum = Math.min(100, Math.max(1, parseInt(limit as string) || 10));
      const offset = (pageNum - 1) * limitNum;

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Get company profiles with search and pagination
      const { data: companyProfiles, error: companyProfileError, count } = await this.supabaseService.getCompanyProfileByWorkspaceIdWithSearch(
        userProfile.workspace_id,
        search as string,
        limitNum,
        offset,
        sortBy as string,
        sortOrder as string
      );
      
      if (companyProfileError) {
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to fetch company profiles',
          timestamp: new Date().toISOString()
        });
      }

      // Calculate pagination metadata
      const totalPages = Math.ceil((count || 0) / limitNum);
      const hasNextPage = pageNum < totalPages;
      const hasPrevPage = pageNum > 1;

      return res.status(200).json({
        status: 'success',
        code: 200,
        data: companyProfiles || [],
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: count || 0,
          itemsPerPage: limitNum,
          hasNextPage,
          hasPrevPage,
          nextPage: hasNextPage ? pageNum + 1 : null,
          prevPage: hasPrevPage ? pageNum - 1 : null
        },
        filters: {
          search: search as string,
          sortBy: sortBy as string,
          sortOrder: sortOrder as string
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Get company profile failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async getCompanyProfileById(id: string, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Get the specific company profile by ID
      const { data: companyProfile, error: companyProfileError } = await this.supabaseService.getCompanyProfileById(id);
      
      if (companyProfileError || !companyProfile) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Company profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if the company profile belongs to the user's workspace
      if (companyProfile.workspace_id !== userProfile.workspace_id) {
        return res.status(403).json({
          status: 'error',
          code: 403,
          message: 'Company profile does not belong to your workspace',
          timestamp: new Date().toISOString()
        });
      }

      return res.status(200).json({
        status: 'success',
        code: 200,
        data: companyProfile,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Get company profile by ID failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async updateCompanyProfile(id: string, updateCompanyProfileDto: any, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Get the specific company profile by ID
      const { data: companyProfile, error: companyProfileError } = await this.supabaseService.getCompanyProfileById(id);
      
      if (companyProfileError || !companyProfile) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Company profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if the company profile belongs to the user's workspace
      if (companyProfile.workspace_id !== userProfile.workspace_id) {
        return res.status(403).json({
          status: 'error',
          code: 403,
          message: 'Company profile does not belong to your workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user is the creator of the company profile
      if (companyProfile.created_by !== user.id) {
        return res.status(403).json({
          status: 'error',
          code: 403,
          message: 'Only the creator can update company profile',
          timestamp: new Date().toISOString()
        });
      }

      // Update company profile
      const { data: updatedProfile, error: updateError } = await this.supabaseService.updateCompanyProfile(companyProfile.id, updateCompanyProfileDto);
      
      if (updateError) {
        this.logger.error('Failed to update company profile:', updateError);
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Failed to update company profile',
          timestamp: new Date().toISOString()
        });
      }

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Company profile updated successfully',
        data: updatedProfile,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Update company profile failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async deleteCompanyProfile(id: string, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Get the specific company profile by ID
      const { data: companyProfile, error: companyProfileError } = await this.supabaseService.getCompanyProfileById(id);
      
      if (companyProfileError || !companyProfile) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Company profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if the company profile belongs to the user's workspace
      if (companyProfile.workspace_id !== userProfile.workspace_id) {
        return res.status(403).json({
          status: 'error',
          code: 403,
          message: 'Company profile does not belong to your workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user is the creator of the company profile
      if (companyProfile.created_by !== user.id) {
        return res.status(403).json({
          status: 'error',
          code: 403,
          message: 'Only the creator can delete company profile',
          timestamp: new Date().toISOString()
        });
      }

      // Delete company profile
      const { error: deleteError } = await this.supabaseService.deleteCompanyProfile(id);
      
      if (deleteError) {
        this.logger.error('Failed to delete company profile:', deleteError);
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Failed to delete company profile',
          timestamp: new Date().toISOString()
        });
      }

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Company profile deleted successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Delete company profile failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async getUsermyCard(req: any, res: any): Promise<Response> {
    try {
      const user = req.user;
      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }

      const { data: userProfileCards, error: userProfileCardsError } = await this.supabaseService.getClient().from('automate_card_user_profile').select(`
        *,
        company_profile:automate_card_company_profile!company_profile_id(
          id,
          company_name,
          company_logo,
          address,
          phone_number,
          country_code,
          email,
          website,
          industry_type
        )
      `).eq('user_id', user.id).order('created_at', { ascending: false });
    
      if (userProfileCardsError) {
        return res.status(500).json({
          status: 'error',
          code: 500,
          message: 'Failed to fetch user profile cards',
          timestamp: new Date().toISOString()
        });
      }

      if (!userProfileCards || userProfileCards.length === 0) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'No user profile cards found for this company',
          timestamp: new Date().toISOString()
        });
      }

   

      return res.status(200).json({
        status: 'success',
        code: 200,
        data: userProfileCards,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Get user profile card failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async updateUserProfileCard(id: string, updateUserProfileCardDto: any, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;
      const profile_id = id; 

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }
        // Update user profile card
        const { data: updatedProfileCard, error: updateError } = await this.supabaseService.updateUserProfileCard(profile_id, updateUserProfileCardDto);
        
        if (updateError) {
          this.logger.error('Failed to update user profile card:', updateError);
          return res.status(400).json({
            status: 'error',
            code: 400,
            message: 'Failed to update user profile card',
            timestamp: new Date().toISOString()
          });
        }

        return res.status(200).json({
          status: 'success',
          code: 200,
          message: 'User profile card updated successfully',
          data: updatedProfileCard,
          timestamp: new Date().toISOString()
        });
    } catch (error) {
      this.logger.error('Update user profile card failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async getAllUserProfileCards(id: string, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;
      const companyProfileId = id; 

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }

      // If company_profile_id is provided, get all user profile cards for that specific company
      if (companyProfileId) {
        // Verify the company profile belongs to user's workspace
        const { data: companyProfile, error: companyProfileError } = await this.supabaseService.getCompanyProfileById(companyProfileId);
        
        if (companyProfileError || !companyProfile) {
          return res.status(404).json({
            status: 'error',
            code: 404,
            message: 'Company profile not found',
            timestamp: new Date().toISOString()
          });
        }

        if (companyProfile.workspace_id !== userProfile.workspace_id) {
          return res.status(403).json({
            status: 'error',
            code: 403,
            message: 'Company profile does not belong to your workspace',
            timestamp: new Date().toISOString()
          });
        }

        // Get all user profile cards for the specific company with search and pagination
        const { 
          search = '', 
          page = 1, 
          limit = 10, 
          sortBy = 'created_at', 
          sortOrder = 'desc' 
        } = req.query;

        const pageNum = Math.max(1, parseInt(page as string) || 1);
        const limitNum = Math.min(100, Math.max(1, parseInt(limit as string) || 10));
        const offset = (pageNum - 1) * limitNum;

        const { data: userProfileCards, error: userProfileCardsError, count } = await this.supabaseService.getUserProfileCardsByCompanyWithSearch(
          companyProfileId,
          search as string,
          limitNum,
          offset,
          sortBy as string,
          sortOrder as string
        );
        
        if (userProfileCardsError) {
          this.logger.error('Failed to get user profile cards:', userProfileCardsError);
          return res.status(400).json({
            status: 'error',
            code: 400,
            message: 'Failed to get user profile cards',
            timestamp: new Date().toISOString()
          });
        }

        // Calculate pagination metadata
        const totalPages = Math.ceil((count || 0) / limitNum);
        const hasNextPage = pageNum < totalPages;
        const hasPrevPage = pageNum > 1;

        return res.status(200).json({
          status: 'success',
          code: 200,
          data: userProfileCards || [],
          pagination: {
            currentPage: pageNum,
            totalPages,
            totalItems: count || 0,
            itemsPerPage: limitNum,
            hasNextPage,
            hasPrevPage,
            nextPage: hasNextPage ? pageNum + 1 : null,
            prevPage: hasPrevPage ? pageNum - 1 : null
          },
          filters: {
            search: search as string,
            sortBy: sortBy as string,
            sortOrder: sortOrder as string
          },
          timestamp: new Date().toISOString()
        });
      }

      return res.status(400).json({
        status: 'error',
        code: 400,
        message: 'Company profile ID is required',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Get all user profile cards failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async getPublicCompanyProfileById(id: string, req: any, res: any): Promise<Response> {
    try {
      const { data: companyProfile, error: companyProfileError } = await this.supabaseService.getCompanyProfileById(id);
      
      if (companyProfileError || !companyProfile) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Company profile not found',
          timestamp: new Date().toISOString()
        });
      }

      return res.status(200).json({
        status: 'success',
        code: 200,
        data: companyProfile,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Get company profile by ID failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }
  async publicUserCard(id: string, req: any, res: any): Promise<Response> {
    try {
      const userProfileCardId = id;
      const { data: userProfileCard, error: userProfileCardError } = await this.supabaseService.getUserProfileCardById(userProfileCardId);
      if(userProfileCardError || !userProfileCard) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'User profile card not found',
          timestamp: new Date().toISOString()
        });
      }

      return res.status(200).json({
        status: 'success',
        code: 200,
        data: userProfileCard,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Get user profile card failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async getSpecificUserCard(id: string, req: any, res: any): Promise<Response> {
    try {
      const userProfileCardId = id;
      const { data: userProfileCard, error: userProfileCardError } = await this.supabaseService.getUserProfileCardById(userProfileCardId);
      if(userProfileCardError || !userProfileCard) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'User profile card not found',
          timestamp: new Date().toISOString()
        });
      }
      return res.status(200).json({
        status: 'success',
        code: 200,
        data: userProfileCard,
        timestamp: new Date().toISOString()
      });
    }
    catch (error) {
      this.logger.error('Get user profile card failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  async updateMemberCardAccess(updateMemberAccessDto: UpdateMemberAccessDto, req: any, res: any): Promise<Response> {
    try {
      const user = req.user;

      // Check if user exists in user_profile table
      const { data: userProfile, error: userProfileError } = await this.supabaseService.getUserProfile(user.id);
      if (!userProfile || userProfileError) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User profile not found',
          timestamp: new Date().toISOString()
        });
      }

      // Check if user has a workspace
      if (!userProfile.workspace_id) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'User must have a workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Verify the target user exists in the workspace
      const { data: targetMember, error: targetMemberError } = await this.supabaseService.getWorkspaceMember(
        userProfile.workspace_id,
        updateMemberAccessDto.user_id
      );

      if (targetMemberError || !targetMember) {
        return res.status(404).json({
          status: 'error',
          code: 404,
          message: 'Member not found in workspace',
          timestamp: new Date().toISOString()
        });
      }

      // Check if the requesting user has permission to update member access
      // Only Admin role can update member access
      const { data: requestingMember, error: requestingMemberError } = await this.supabaseService.getWorkspaceMember(
        userProfile.workspace_id,
        user.id
      );

      if (requestingMemberError || !requestingMember) {
        return res.status(403).json({
          status: 'error',
          code: 403,
          message: 'You are not a member of this workspace',
          timestamp: new Date().toISOString()
        });
      }

      if (requestingMember.role !== 'Admin') {
        return res.status(403).json({
          status: 'error',
          code: 403,
          message: 'Only Admin role can update member access permissions',
          timestamp: new Date().toISOString()
        });
      }

      // Prevent admin from removing their own access
      if (updateMemberAccessDto.user_id === user.id && !updateMemberAccessDto.cards_access) {
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Admin cannot remove their own card access',
          timestamp: new Date().toISOString()
        });
      }

      // Update member card access
      const { data: updatedMember, error: updateError } = await this.supabaseService.updateWorkspaceMember(
        userProfile.workspace_id,
        updateMemberAccessDto.user_id,
        { cards_access: updateMemberAccessDto.cards_access }
      );

      if (updateError) {
        this.logger.error('Failed to update member card access:', updateError);
        return res.status(400).json({
          status: 'error',
          code: 400,
          message: 'Failed to update member card access',
          timestamp: new Date().toISOString()
        });
      }

      return res.status(200).json({
        status: 'success',
        code: 200,
        message: 'Member card access updated successfully',
        data: {
          member: updatedMember,
          updated_by: user.id,
          updated_at: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('Update member card access failed:', error);
      return res.status(500).json({
        status: 'error',
        code: 500,
        message: 'Internal server error',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Creates user profile cards for a user across all company profiles in a workspace
   * @param userId - The user ID to create profile cards for
   * @param workspaceId - The workspace ID to get company profiles from
   * @param userData - User data for the profile cards
   * @returns Promise with creation results
   */
  private async createUserProfileCardsForWorkspace(
    userId: string, 
    workspaceId: number, 
    userData: {
      first_name: string;
      last_name: string;
      email: string;
      phone?: string;
      role?: string;
    }
  ): Promise<{ successCount: number; failedCount: number; errors: any[] }> {
    try {
      // Get all company profiles for the workspace
      const { data: companyProfiles, error: companyProfilesError } = await this.supabaseService.getCompanyProfileByWorkspaceId(workspaceId);
      
      if (companyProfilesError) {
        this.logger.error('Failed to get company profiles:', companyProfilesError);
        return { successCount: 0, failedCount: 0, errors: [companyProfilesError] };
      }

      if (!companyProfiles || companyProfiles.length === 0) {
        this.logger.log('No company profiles found for workspace, skipping user profile card creation');
        return { successCount: 0, failedCount: 0, errors: [] };
      }

      // Create user profile cards for all company profiles
      const userProfileCardPromises = companyProfiles.map(async (companyProfile) => {
        const userProfileCardData = {
          company_profile_id: companyProfile.id,
          user_id: userId,
          full_name: `${userData.first_name} ${userData.last_name}`,
          email: userData.email,
          phone_number: userData.phone?.toString(),
          designation: userData.role || 'Member'
        };

        return this.supabaseService.insertUserProfileCard(userProfileCardData);
      });

      const userProfileCardResults = await Promise.all(userProfileCardPromises);
      const failedProfileCards = userProfileCardResults.filter(result => result.error);
      const successCount = userProfileCardResults.length - failedProfileCards.length;

      if (failedProfileCards.length > 0) {
        this.logger.error('Some user profile cards failed to create:', failedProfileCards);
      }

      this.logger.log(`Created ${successCount} user profile cards for user ${userId} across ${companyProfiles.length} company profiles`);

      return {
        successCount,
        failedCount: failedProfileCards.length,
        errors: failedProfileCards.map(result => result.error)
      };

    } catch (error) {
      this.logger.error('Error creating user profile cards for workspace:', error);
      return { successCount: 0, failedCount: 0, errors: [error] };
    }
  }
  
}
