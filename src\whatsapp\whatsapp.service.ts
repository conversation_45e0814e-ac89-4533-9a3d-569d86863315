import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

interface MessageStatus {
  messageId: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: string;
  to: string;
}

interface TemplateMessageRequest {
  templateId: string;
  to: string;
  variables: Record<string, any>;
  language?: string;
}

@Injectable()
export class WhatsAppService {
  private readonly logger = new Logger(WhatsAppService.name);
  private readonly apiUrl = 'https://graph.facebook.com/v18.0';
  private readonly accessToken: string;
  private readonly phoneNumberId: string;
  
  // In-memory storage for message status (in production, use a database)
  private messageStatuses: Map<string, MessageStatus> = new Map();

  constructor(private readonly configService: ConfigService) {
    this.accessToken = this.configService.get<string>('WHATSAPP_ACCESS_TOKEN') || '';
    this.phoneNumberId = this.configService.get<string>('WHATSAPP_PHONE_NUMBER_ID') || '';
  }

  async sendTextMessage(to: string, message: string): Promise<{ success: boolean; messageId?: string }> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'text',
          text: {
            preview_url: false,
            body: message,
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const messageId = response.data?.messages?.[0]?.id;
      this.logger.log(`Message sent successfully to ${to} with ID: ${messageId}`);
      
      // Store initial status
      if (messageId) {
        this.messageStatuses.set(messageId, {
          messageId,
          status: 'sent',
          timestamp: new Date().toISOString(),
          to
        });
      }
      
      return { success: true, messageId };
    } catch (error) {
      this.logger.error(`Failed to send message to ${to}:`, error.response?.data || error.message);
      return { success: false };
    }
  }

  async sendInteractiveMessage(to: string, message: string, buttons: Array<{ id: string; title: string }>): Promise<{ success: boolean; messageId?: string }> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'interactive',
          interactive: {
            type: 'button',
            body: {
              text: message,
            },
            action: {
              buttons: buttons.map(button => ({
                type: 'reply',
                reply: {
                  id: button.id,
                  title: button.title,
                },
              })),
            },
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const messageId = response.data?.messages?.[0]?.id;
      this.logger.log(`Interactive message sent successfully to ${to} with ID: ${messageId}`);
      return { success: true, messageId };
    } catch (error) {
      this.logger.error(`Failed to send interactive message to ${to}:`, error.response?.data || error.message);
      return { success: false };
    }
  }

  async sendListMessage(to: string, message: string, sections: Array<{ title: string; rows: Array<{ id: string; title: string; description?: string }> }>): Promise<{ success: boolean; messageId?: string }> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'interactive',
          interactive: {
            type: 'list',
            body: {
              text: message,
            },
            action: {
              button: 'Choose an option',
              sections: sections,
            },
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const messageId = response.data?.messages?.[0]?.id;
      this.logger.log(`List message sent successfully to ${to} with ID: ${messageId}`);
      return { success: true, messageId };
    } catch (error) {
      this.logger.error(`Failed to send list message to ${to}:`, error.response?.data || error.message);
      return { success: false };
    }
  }

  async sendImageMessage(to: string, imageUrl: string, caption?: string): Promise<{ success: boolean; messageId?: string }> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'image',
          image: {
            link: imageUrl,
            caption: caption,
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );
      console.log("response", response);
      const messageId = response.data?.messages?.[0]?.id;
      this.logger.log(`Image message sent successfully to ${to} with ID: ${messageId}`);
      return { success: true, messageId };
    } catch (error) {
      this.logger.error(`Failed to send image message to ${to}:`, error.response?.data || error.message);
      return { success: false };
    }
  }

  /**
   * Send video message
   */
  async sendVideoMessage(to: string, videoUrl: string, caption?: string): Promise<{ success: boolean; messageId?: string }> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'video',
          video: {
            link: videoUrl,
            caption: caption,
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const messageId = response.data?.messages?.[0]?.id;
      this.logger.log(`Video message sent successfully to ${to} with ID: ${messageId}`);
      return { success: true, messageId };
    } catch (error) {
      this.logger.error(`Failed to send video message to ${to}:`, error.response?.data || error.message);
      return { success: false };
    }
  }

  /**
   * Send document message
   */
  async sendDocumentMessage(to: string, documentUrl: string, caption?: string): Promise<{ success: boolean; messageId?: string }> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'document',
          document: {
            link: documentUrl,
            caption: caption,
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const messageId = response.data?.messages?.[0]?.id;
      this.logger.log(`Document message sent successfully to ${to} with ID: ${messageId}`);
      return { success: true, messageId };
    } catch (error) {
      this.logger.error(`Failed to send document message to ${to}:`, error.response?.data || error.message);
      return { success: false };
    }
  }

  /**
   * Send audio message
   */
  async sendAudioMessage(to: string, audioUrl: string): Promise<{ success: boolean; messageId?: string }> {
    try {
      const response = await axios.post(
        `${this.apiUrl}/${this.phoneNumberId}/messages`,
        {
          messaging_product: 'whatsapp',
          recipient_type: 'individual',
          to: to,
          type: 'audio',
          audio: {
            link: audioUrl,
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const messageId = response.data?.messages?.[0]?.id;
      this.logger.log(`Audio message sent successfully to ${to} with ID: ${messageId}`);
      return { success: true, messageId };
    } catch (error) {
      this.logger.error(`Failed to send audio message to ${to}:`, error.response?.data || error.message);
      return { success: false };
    }
  }

  async getMessageStatus(messageId: string): Promise<any> {
    // Check if status is already stored
    const storedStatus = this.messageStatuses.get(messageId);
    if (storedStatus) {
      this.logger.log(`Returning stored status for message ID: ${messageId}`);
      return {
        messaging_product: 'whatsapp',
        status: storedStatus.status,
        id: storedStatus.messageId,
        timestamp: storedStatus.timestamp,
        to: storedStatus.to
      };
    }

    // If not found, return a default response
    this.logger.warn(`Message status not found for ID: ${messageId}`);
    return {
      messaging_product: 'whatsapp',
      status: 'unknown',
      id: messageId,
      message: 'Message status not available. Status updates come via webhooks.'
    };
  }

  // Method to update message status (called by webhooks)
  updateMessageStatus(messageId: string, status: 'sent' | 'delivered' | 'read' | 'failed', to?: string): void {
    const existingStatus = this.messageStatuses.get(messageId);
    if (existingStatus) {
      existingStatus.status = status;
      existingStatus.timestamp = new Date().toISOString();
      this.messageStatuses.set(messageId, existingStatus);
      this.logger.log(`Updated status for message ${messageId} to ${status}`);
    } else if (to) {
      // Create new status entry if it doesn't exist
      this.messageStatuses.set(messageId, {
        messageId,
        status,
        timestamp: new Date().toISOString(),
        to
      });
      this.logger.log(`Created new status entry for message ${messageId} with status ${status}`);
    }
  }

  // Method to get all message statuses (for debugging)
  getAllMessageStatuses(): MessageStatus[] {
    return Array.from(this.messageStatuses.values());
  }
} 