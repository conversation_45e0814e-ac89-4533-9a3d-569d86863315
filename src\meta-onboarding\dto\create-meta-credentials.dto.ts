import { Is<PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

/**
 * DTO for creating meta credentials
 */
export class CreateMetaCredentialsDto {
  @IsString({ message: 'WhatsApp Business ID must be a string' })
  @IsNotEmpty({ message: 'WhatsApp Business ID is required' })
  @MinLength(1, { message: 'WhatsApp Business ID must be at least 1 character long' })
  @MaxLength(50, { message: 'WhatsApp Business ID must not exceed 50 characters' })
  whatsapp_business_id: string;

  @IsString({ message: 'Phone Number ID must be a string' })
  @IsNotEmpty({ message: 'Phone Number ID is required' })
  @MinLength(1, { message: 'Phone Number ID must be at least 1 character long' })
  @MaxLength(50, { message: 'Phone Number ID must not exceed 50 characters' })
  phone_number_id: string;

  @IsString({ message: 'Access token must be a string' })
  @IsNotEmpty({ message: 'Access token is required' })
  @MinLength(10, { message: 'Access token must be at least 10 characters long' })
  @MaxLength(1000, { message: 'Access token must not exceed 1000 characters' })
  access_token: string;

  @IsOptional()
  @IsEnum(['Active', 'Inactive'], { message: 'Status must be either "Active" or "Inactive"' })
  status?: 'Active' | 'Inactive';
}
