import { Response } from 'express';

/**
 * Standard response interface for consistent API responses
 */
export interface StandardSegmentsResponse {
  status: 'success' | 'error';
  code: number;
  message: string;
  data?: any;
  error?: any;
  timestamp: string;
}

/**
 * Segments-specific response data interfaces
 */
export interface SegmentsResponseData {
  segment?: any;
  segments?: any[];
  contacts?: any[];
  operators?: any[];
  pagination?: PaginationMetadata;
}

export interface PaginationMetadata {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Utility class for creating consistent segments responses
 */
export class SegmentsResponseUtil {
  /**
   * Creates a success response with segments data
   */
  static createSuccessResponse(
    data: SegmentsResponseData | any,
    message: string = 'Operation completed successfully',
    code: number = 200
  ): StandardSegmentsResponse {
    return {
      status: 'success',
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates an error response
   */
  static createErrorResponse(
    message: string,
    code: number = 400,
    error?: any
  ): StandardSegmentsResponse {
    return {
      status: 'error',
      code,
      message,
      error: error?.message || error,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates a duplicate error response
   */
  static createDuplicateErrorResponse(
    message: string,
    code: number = 409
  ): StandardSegmentsResponse {
    return {
      status: 'error',
      code,
      message,
      error: 'Duplicate entry',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Creates a validation error response
   */
  static createValidationErrorResponse(
    message: string,
    details?: any,
    code: number = 400
  ): StandardSegmentsResponse {
    return {
      status: 'error',
      code,
      message,
      error: details,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Sends response to client
   */
  static sendResponse(res: Response, response: StandardSegmentsResponse): void {
    res.status(response.code).json(response);
  }

  /**
   * Creates segment creation data
   */
  static createSegmentCreationData(segment: any): SegmentsResponseData {
    return {
      segment: {
        id: segment._id,
        name: segment.name,
        description: segment.description,
        rules: segment.rules,
        match: segment.match,
        condition: segment.condition,
        workspaceId: segment.workspaceId,
        createdBy: segment.createdBy,
        createdAt: segment.createdAt,
        updatedAt: segment.updatedAt
      }
    };
  }

  /**
   * Creates segments list data
   */
  static createSegmentsListData(segments: any[]): SegmentsResponseData {
    return {
      segments: segments.map(segment => ({
        id: segment._id,
        name: segment.name,
        description: segment.description,
        rules: segment.rules,
        match: segment.match,
        condition: segment.condition,
        workspaceId: segment.workspaceId,
        createdBy: segment.createdBy,
        createdAt: segment.createdAt,
        updatedAt: segment.updatedAt
      }))
    };
  }

  /**
   * Creates contacts list data with pagination
   */
  static createContactsListData(contacts: any[], pagination: PaginationMetadata): SegmentsResponseData {
    return {
      contacts: contacts.map(contact => ({
        id: contact._id,
        firstName: contact.firstName,
        lastName: contact.lastName,
        chatName: contact.chatName,
        email: contact.email,
        phoneNumber: contact.phoneNumber,
        countryCode: contact.countryCode,
        source: contact.source,
        subscribed: contact.subscribed,
        tagsId: contact.tagsId,
        customFields: contact.customFields,
        workspaceId: contact.workspaceId,
        createdBy: contact.createdBy,
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt
      })),
      pagination
    };
  }

  /**
   * Creates operators list data
   */
  static createOperatorsListData(operators: any[]): SegmentsResponseData {
    return {
      operators: operators.map(operator => ({
        key: operator.key,
        label: operator.label,
        valueType: operator.valueType,
        description: operator.description,
        applicableFields: operator.applicableFields
      }))
    };
  }

  /**
   * Creates pagination metadata
   */
  static createPaginationMetadata(page: number, limit: number, total: number): PaginationMetadata {
    const totalPages = Math.ceil(total / limit);
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };
  }
}
