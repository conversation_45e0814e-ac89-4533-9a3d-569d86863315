import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

export interface MetaTemplateRequest {
  name: string;
  category: string;
  components: MetaTemplateComponent[];
  language: string;
}

export interface MetaTemplateComponent {
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';
  format?: 'TEXT' | 'IMAGE' | 'VIDEO' | 'DOCUMENT' | 'LOCATION';
  text?: string;
  add_security_recommendation?: boolean;
  code_expiration_minutes?: number;
  example?: {
    header_text?: string[];
    header_handle?: string[];
    body_text?: string[][];  // Only body_text with nested array format
  };
  buttons?: MetaTemplateButton[];
}

export interface MetaTemplateButton {
  type: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER' | 'COPY_CODE' | 'OTP' | 'CATALOG' | 'MPM';
  text: string;
  url?: string;
  phone_number?: string;
  otp_type?: 'COPY_CODE' | 'ONE_TAP';
  autofill_text?: string;
  package_name?: string;
  signature_hash?: string;
}

export interface MetaTemplateResponse {
  id: string;
  status: string;
  category: string;
  components: MetaTemplateComponent[];
  language: string;
  name: string;
  quality_rating?: string;
  quality_score?: number;
}

export interface MetaTemplateListResponse {
  data: MetaTemplateResponse[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
  };
}

@Injectable()
export class MetaApiService {
  private readonly logger = new Logger(MetaApiService.name);
  private readonly axiosInstance: AxiosInstance;
  private readonly baseUrl = 'https://graph.facebook.com/v19.0';

  constructor(private configService: ConfigService) {
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
    });
  }

  /**
   * Create a WhatsApp template using Meta's API
   */
  async createTemplate(
    whatsappBusinessId: string,
    accessToken: string,
    templateData: MetaTemplateRequest
  ): Promise<MetaTemplateResponse> {
    try {
      // Meta API endpoint for creating templates - uses WhatsApp Business Account ID
      const url = `/${whatsappBusinessId}/message_templates`;
      
      // Prepare the request payload according to Meta's API specification
      const payload = {
        name: templateData.name,
        category: templateData.category,
        components: templateData.components,
        language: templateData.language
      };
      this.logger.log(`Creating template in Meta: ${JSON.stringify(payload)}`);
      console.log("=== META API REQUEST ===");
      console.log("URL:", url);
      console.log("Payload:", JSON.stringify(payload, null, 2));
      console.log("Components:", JSON.stringify(payload.components, null, 2));
      
      const response = await this.axiosInstance.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      this.logger.log(`Template created successfully in Meta: ${response.data.id}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to create Meta template:', error.response?.data || error.message);
      
      // Parse Meta API error response for better user messages
      let errorMessage = 'Failed to create template in Meta';
      
      if (error.response?.data?.error) {
        const metaError = error.response.data.error;
        
        // Handle specific Meta API error codes
        switch (metaError.code) {
          case 190: // OAuth token expired
            errorMessage = 'Your Meta access token has expired. Please reconnect your WhatsApp Business Account to refresh the token.';
            break;
          case 100: // Invalid parameter
            errorMessage = `Invalid template data: ${metaError.message}`;
            break;
          case 368: // Temporary API issue
            errorMessage = 'Meta API is temporarily unavailable. Please try again in a few minutes.';
            break;
          case 200: // Permission denied
            errorMessage = 'You do not have permission to create templates. Please check your Meta Business account permissions.';
            break;
          case 131026: // Template name already exists
            errorMessage = 'A template with this name already exists. Please use a different name.';
            break;
          case 131021: // Invalid template format
            errorMessage = 'Template format is invalid. Please check your template structure and try again.';
            break;
          default:
            errorMessage = metaError.message || `Meta API error (Code: ${metaError.code})`;
        }
      } else if (error.response?.status === 401) {
        errorMessage = 'Your Meta access token is invalid or expired. Please reconnect your WhatsApp Business Account.';
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to create templates. Please check your Meta Business account permissions.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Meta API is temporarily unavailable. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      throw new BadRequestException(errorMessage);
    }
  }

  /**
   * Get all templates for a WhatsApp Business Account
   */
  async getTemplates(
    whatsappBusinessId: string,
    accessToken: string
  ): Promise<MetaTemplateListResponse> {
    try {
      const url = `/${whatsappBusinessId}/message_templates`;
      
      const response = await this.axiosInstance.get(url, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        }
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to get Meta templates:', error.response?.data || error.message);
      
      // Parse Meta API error response for better user messages
      let errorMessage = 'Failed to fetch templates from Meta';
      
      if (error.response?.data?.error) {
        const metaError = error.response.data.error;
        
        // Handle specific Meta API error codes
        switch (metaError.code) {
          case 190: // OAuth token expired
            errorMessage = 'Your Meta access token has expired. Please reconnect your WhatsApp Business Account to refresh the token.';
            break;
          case 200: // Permission denied
            errorMessage = 'You do not have permission to access templates. Please check your Meta Business account permissions.';
            break;
          case 368: // Temporary API issue
            errorMessage = 'Meta API is temporarily unavailable. Please try again in a few minutes.';
            break;
          default:
            errorMessage = metaError.message || `Meta API error (Code: ${metaError.code})`;
        }
      } else if (error.response?.status === 401) {
        errorMessage = 'Your Meta access token is invalid or expired. Please reconnect your WhatsApp Business Account.';
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to access templates. Please check your Meta Business account permissions.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Meta API is temporarily unavailable. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      throw new BadRequestException(errorMessage);
    }
  }

  /**
   * Get a specific template by ID
   */
  async getTemplate(
    templateId: string,
    accessToken: string
  ): Promise<MetaTemplateResponse> {
    try {
      const url = `/${templateId}`;
      
      const response = await this.axiosInstance.get(url, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to get Meta template:', error.response?.data || error.message);
      throw new BadRequestException(`Meta API error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Update a template
   */
  async updateTemplate(
    templateId: string,
    accessToken: string,
    templateData: Partial<MetaTemplateRequest>
  ): Promise<MetaTemplateResponse> {
    try {
      const url = `/${templateId}`;
      
      const response = await this.axiosInstance.post(url, templateData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      this.logger.log(`Template updated successfully: ${templateId}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to update Meta template:', error.response?.data || error.message);
      throw new BadRequestException(`Meta API error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Delete a template
   */
  async deleteTemplate(
    templateName: string,
    whatsappBusinessId: string,
    accessToken: string
  ): Promise<void> {
    try {
      const url = `/${whatsappBusinessId}/message_templates?name=${templateName}`;
      await this.axiosInstance.delete(url, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
    } catch (error) {
      // Throw error to propagate to controller
      throw error;
    }
  }
  
  
  

  /**
   * Get template categories
   */
  async getTemplateCategories(accessToken: string): Promise<any> {
    try {
      const url = '/template_categories';
      
      const response = await this.axiosInstance.get(url, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      return response.data;
    } catch (error) {
      this.logger.error('Failed to get template categories:', error.response?.data || error.message);
      throw new BadRequestException(`Meta API error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  /**
   * Determine media type from URL or template type
   */
  private determineMediaType(url: string, templateType?: string): 'image' | 'video' | 'document' {
    // If template type is specified, use it
    if (templateType) {
      if (templateType === 'image' || templateType === 'video' || templateType === 'document') {
        return templateType;
      }
    }
    
    // Determine from file extension
    const extension = url.split('.').pop()?.toLowerCase();
    
    // Image extensions
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(extension || '')) {
      return 'image';
    }
    
    // Video extensions
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension || '')) {
      return 'video';
    }
    
    // Document extensions (default for other files)
    return 'document';
  }

  /**
   * Send a template message via WhatsApp Business API
   */
  async sendTemplateMessage(
    phoneNumberId: string,
    templateName: string,
    recipientPhoneNumber: string,
    countryCode: string,
    variables: Record<string, any> = {},
    language: string = 'en',
    accessToken: string,
    templateInfo?: { type?: string; [key: string]: any }
  ): Promise<any> {
    try {
      const url = `/${phoneNumberId}/messages`;
      
      // Prepare the message payload according to Meta API specification
      const payload: any = {
        messaging_product: 'whatsapp',
        recipient_type: 'individual',
        to: countryCode ? countryCode + recipientPhoneNumber: recipientPhoneNumber,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: language
          },
          components: []
        }
      };
      console.log("payload",payload)
              // Add variables if provided
      if (Object.keys(variables).length > 0) {
        const components: any[] = [];
        
        // Add body variables
        if (variables.body) {
          const bodyParameters: any[] = [];
          
          // Convert object entries to array and sort by key to maintain order
          const sortedEntries = Object.entries(variables.body).sort(([a], [b]) => {
            // Extract numbers from keys like "{{1}}", "{{2}}" etc.
            const numA = parseInt(a.replace(/\D/g, ''));
            const numB = parseInt(b.replace(/\D/g, ''));
            return numA - numB;
          });
          
          for (const [key, value] of sortedEntries) {
            if (value !== null && typeof value === 'string') {
              bodyParameters.push({
                type: 'text',
                text: value
              });
            } else if (value !== null && typeof value === 'object' && value !== null && 'type' in value) {
              // Handle complex parameter types (currency, date_time, etc.)
              bodyParameters.push(value);
            }
          }
          
          if (bodyParameters.length > 0) {
            components.push({
              type: 'body',
              parameters: bodyParameters
            });
          }
        }

        // Add header variables
        if (variables.header) {
          const headerParameters: any[] = [];
          
          // Handle special header keys like link, header_handle, etc.
          const headerLink = variables.header.link || variables.header.header_handle;
          if (headerLink) {
            const mediaType = this.determineMediaType(headerLink, templateInfo?.type);
            
            if (mediaType === 'image') {
              headerParameters.push({
                type: 'image',
                image: {
                  link: headerLink
                }
              });
            } else if (mediaType === 'video') {
              headerParameters.push({
                type: 'video',
                video: {
                  link: headerLink
                }
              });
            } else if (mediaType === 'document') {
              headerParameters.push({
                type: 'document',
                document: {
                  link: headerLink
                }
              });
            }
          }
          
          // Handle numbered placeholder keys like "{{1}}", "{{2}}" etc.
          const numberedEntries = Object.entries(variables.header)
            .filter(([key]) => key.startsWith('{{') && key.endsWith('}}'))
            .sort(([a], [b]) => {
              // Extract numbers from keys like "{{1}}", "{{2}}" etc.
              const numA = parseInt(a.replace(/\D/g, ''));
              const numB = parseInt(b.replace(/\D/g, ''));
              return numA - numB;
            });
          
          for (const [key, value] of numberedEntries) {
            if (value !== null && typeof value === 'string') {
              headerParameters.push({
                type: 'text',
                text: value
              });
            } else if (value !== null && typeof value === 'object' && value !== null && 'type' in value) {
              // Handle complex parameter types (currency, date_time, etc.)
              headerParameters.push(value);
            }
          }
          
          if (headerParameters.length > 0) {
            components.push({
              type: 'header',
              parameters: headerParameters
            });
          }
        }

        // Add button variables (support both 'button' and 'buttons' keys)
        const buttonVariables = variables.button || variables.buttons;
        if (buttonVariables) {
          const buttonParameters: any[] = [];
          
          // Handle numbered placeholder keys like "{{1}}", "{{2}}" etc.
          const numberedEntries = Object.entries(buttonVariables)
            .filter(([key]) => key.startsWith('{{') && key.endsWith('}}'))
            .sort(([a], [b]) => {
              // Extract numbers from keys like "{{1}}", "{{2}}" etc.
              const numA = parseInt(a.replace(/\D/g, ''));
              const numB = parseInt(b.replace(/\D/g, ''));
              return numA - numB;
            });
          
          for (const [key, value] of numberedEntries) {
            if (value !== null && typeof value === 'string') {
              buttonParameters.push({
                type: 'text',
                text: value
              });
            } else if (value !== null && typeof value === 'object' && value !== null && 'type' in value) {
              buttonParameters.push(value);
            }
          }
          
          if (buttonParameters.length > 0) {
            components.push({
              type: 'button',
              sub_type: 'quick_reply',
              index: 0,
              parameters: buttonParameters
            });
          }
        }

        // Add footer variables
        if (variables.footer) {
          const footerParameters: any[] = [];
          
          // Handle numbered placeholder keys like "{{1}}", "{{2}}" etc.
          const numberedEntries = Object.entries(variables.footer)
            .filter(([key]) => key.startsWith('{{') && key.endsWith('}}'))
            .sort(([a], [b]) => {
              // Extract numbers from keys like "{{1}}", "{{2}}" etc.
              const numA = parseInt(a.replace(/\D/g, ''));
              const numB = parseInt(b.replace(/\D/g, ''));
              return numA - numB;
            });
          
          for (const [key, value] of numberedEntries) {
            if (value !== null && typeof value === 'string') {
              footerParameters.push({
                type: 'text',
                text: value
              });
            } else if (value !== null && typeof value === 'object' && value !== null && 'type' in value) {
              // Handle complex parameter types (currency, date_time, etc.)
              footerParameters.push(value);
            }
          }
          
          if (footerParameters.length > 0) {
            components.push({
              type: 'footer',
              parameters: footerParameters
            });
          }
        }

        payload.template.components = components;
      }

      // Make the actual API call to Meta
      this.logger.log(`Sending template message to ${recipientPhoneNumber} with template ${templateName}`);
      this.logger.log('Payload:', JSON.stringify(payload, null, 2));
      const response = await this.axiosInstance.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
// let response = {
//   data: {
//     messages: [{
//       id: '1234567890'
//     }]
//   }
// }
      this.logger.log(`Template message sent successfully: ${response.data.messages?.[0]?.id}`);
      return {
        success: true,
        message_id: response.data.messages?.[0]?.id,
        payload:payload,
        response:response.data
        
      };

    } catch (error) {
      this.logger.error('Failed to send template message:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Convert our template format to Meta's format
   * Follows Meta's exact requirements for template creation
   */
  convertToMetaFormat(template: any): MetaTemplateRequest {
    this.logger.log(`Converting template to Meta format: ${JSON.stringify(template, null, 2)}`);
    console.log("=== CONVERT TO META FORMAT ===");
    console.log("Template received:", JSON.stringify(template, null, 2));
    console.log("Variables:", template.variables);
    
    const components: MetaTemplateComponent[] = [];

    // 1. HEADER COMPONENT
    if (template.headerText || template.type === 'image' || template.type === 'video' || template.type === 'document' || template.type === 'location') {
      const headerComponent = this.createHeaderComponent(template);
      if (headerComponent) {
        components.push(headerComponent);
      }
    }

    // 2. BODY COMPONENT (Required)
    if (template.content) {
      const bodyComponent = this.createBodyComponent(template);
      components.push(bodyComponent);
    }

    // 3. FOOTER COMPONENT (Optional)
    if (template.footer) {
      const footerComponent = this.createFooterComponent(template);
      components.push(footerComponent);
    }

    // 4. BUTTONS COMPONENT (Optional)
    if (template.buttons && template.buttons.length > 0) {
      const buttonsComponent = this.createButtonsComponent(template);
      components.push(buttonsComponent);
    }

    const result: MetaTemplateRequest = {
      name: template.name,
      category: template.meta_template_category || 'UTILITY',
      components,
      language: template.language === 'en' ? 'en_US' : template.language || 'en_US'  // Use en_US instead of en
    };
    
    this.logger.log(`Final Meta template format: ${JSON.stringify(result, null, 2)}`);
    return result;
  }

  /**
   * Normalize text to use sequential variable numbering starting from {{1}}
   * Meta API requires independent numbering per component
   */
  private normalizeVariableNumbering(text: string): string {
    if (!text.includes('{{')) {
      return text;
    }

    // Find all variables and replace with sequential numbering
    const variableMatches = text.match(/\{\{(\d+)\}\}/g);
    if (!variableMatches) {
      return text;
    }

    let normalizedText = text;
    const uniqueVariables = [...new Set(variableMatches)]; // Remove duplicates
    
    // Replace each variable with sequential numbering starting from {{1}}
    uniqueVariables.forEach((variable, index) => {
      const newVariable = `{{${index + 1}}}`;
      normalizedText = normalizedText.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), newVariable);
    });

    this.logger.log(`Normalized text: "${text}" -> "${normalizedText}"`);
    return normalizedText;
  }

  /**
   * Create HEADER component following Meta's rules
   */
  private createHeaderComponent(template: any): MetaTemplateComponent | null {
    // Text header
    if (template.headerText) {
      const normalizedHeaderText = this.normalizeVariableNumbering(template.headerText);
      const headerComponent: MetaTemplateComponent = {
        type: 'HEADER',
        format: 'TEXT',
        text: normalizedHeaderText
      };

      // Add example only if header contains variables (Meta rule: static headers don't need examples)
      if (normalizedHeaderText.includes('{{')) {
        const headerExamples = this.extractVariableExamples(normalizedHeaderText, template.variables);
        if (headerExamples.length > 0) {
          headerComponent.example = {
            header_text: headerExamples  // Meta rule: HEADER uses header_text
          };
        }
      }
      // If header is static (no {{variables}}), no example object is added

      return headerComponent;
    }

    // Media headers
    if (template.type === 'image' && template.imageUrl) {
      return {
        type: 'HEADER',
        format: 'IMAGE',
        example: {
          header_handle: [template.imageUrl]
        }
      };
    }

    if (template.type === 'video' && template.imageUrl) {
      return {
        type: 'HEADER',
        format: 'VIDEO',
        example: {
          header_handle: [template.imageUrl]
        }
      };
    }

    if (template.type === 'document' && template.imageUrl) {
      return {
        type: 'HEADER',
        format: 'DOCUMENT',
        example: {
          header_handle: [template.imageUrl]
        }
      };
    }

    if (template.type === 'location') {
      return {
        type: 'HEADER',
        format: 'LOCATION'
      };
    }

    return null;
  }

  /**
   * Create BODY component following Meta's rules
   */
  private createBodyComponent(template: any): MetaTemplateComponent {
    const normalizedBodyText = this.normalizeVariableNumbering(template.content);
    const bodyComponent: MetaTemplateComponent = {
      type: 'BODY',
      text: normalizedBodyText
    };

    // Add security recommendation for authentication templates
    if (template.meta_template_category === 'AUTHENTICATION') {
      bodyComponent.add_security_recommendation = true;
    }

    // Add example only if body contains variables
    if (normalizedBodyText.includes('{{')) {
      const bodyExamples = this.extractVariableExamples(normalizedBodyText, template.variables);
      if (bodyExamples.length > 0) {
        // Meta documentation: BODY uses body_text with nested array format
        bodyComponent.example = {
          body_text: [bodyExamples]  // Nested array: [["Rahul"]]
        };
        console.log("Body examples added:", bodyExamples);
        console.log("Body component example:", bodyComponent.example);
      } else {
        // If no examples found, don't add example object at all
        console.log("No body examples found, skipping example object");
      }
    }

    return bodyComponent;
  }

  /**
   * Create FOOTER component following Meta's rules
   */
  private createFooterComponent(template: any): MetaTemplateComponent {
    const footerComponent: MetaTemplateComponent = {
      type: 'FOOTER',
      text: template.footer
    };

    // Add code expiration for authentication templates
    if (template.meta_template_category === 'AUTHENTICATION' && template.codeExpirationMinutes) {
      footerComponent.code_expiration_minutes = template.codeExpirationMinutes;
    }

    return footerComponent;
  }

  /**
   * Create BUTTONS component following Meta's rules
   */
  private createButtonsComponent(template: any): MetaTemplateComponent {
    const buttons: MetaTemplateButton[] = template.buttons.map((btn: any) => {
      const button: MetaTemplateButton = {
        type: 'QUICK_REPLY',
        text: btn.title || btn.text
      };

      // Handle different button types
      if (btn.type === 'URL' || btn.url) {
        button.type = 'URL';
        button.url = btn.url;
      } else if (btn.type === 'PHONE_NUMBER' || btn.phone_number) {
        button.type = 'PHONE_NUMBER';
        button.phone_number = btn.phone_number;
      } else if (btn.type === 'OTP') {
        button.type = 'OTP';
        button.otp_type = btn.otp_type || 'COPY_CODE';
        if (btn.otp_type === 'ONE_TAP') {
          button.autofill_text = btn.autofill_text;
          button.package_name = btn.package_name;
          button.signature_hash = btn.signature_hash;
        }
      } else if (btn.type === 'CATALOG') {
        button.type = 'CATALOG';
      } else if (btn.type === 'MPM') {
        button.type = 'MPM';
      }

      return button;
    });

    return {
      type: 'BUTTONS',
      buttons
    };
  }

  /**
   * Extract variable examples from text content
   * Each component (HEADER/BODY) has independent variable numbering
   * Maps user's combined variables to component-specific variables
   */
  private extractVariableExamples(text: string, variables: any): string[] {
    const examples: string[] = [];
    
    if (!variables) {
      return examples;
    }

    // Find all variables in text ({{1}}, {{2}}, etc.)
    const variableMatches = text.match(/\{\{(\d+)\}\}/g);
    if (!variableMatches) {
      return examples;
    }

    // Extract variable numbers and sort them
    const variableNumbers = variableMatches.map(match => {
      const number = match.match(/\{(\d+)\}/)?.[1];
      return number ? parseInt(number) : 0;
    }).sort((a, b) => a - b);

    // Get example values for each variable
    // Meta rule: Variable numbering is independent per component
    for (let i = 0; i < variableNumbers.length; i++) {
      const varNumber = variableNumbers[i];
      let exampleValue = 'Example'; // Default value
      
      // For independent numbering, map component variables to user variables
      // If user has {{1}}, {{2}}, {{3}} and component has {{1}}, {{2}}
      // Map component {{1}} -> user {{1}}, component {{2}} -> user {{2}}
      
      // Try different key patterns to find the example value
      const possibleKeys = [
        `{{${varNumber}}}`,        // Direct format: {{1}}, {{2}}
        `variable${varNumber}`,    // variable1, variable2
        `${varNumber}`,            // 1, 2
        `header_variable${varNumber}`, // header_variable1
        `body_variable${varNumber}`,   // body_variable1
        `header${varNumber}`,      // header1
        `body${varNumber}`         // body1
      ];
      
      for (const key of possibleKeys) {
        if (variables[key]) {
          exampleValue = variables[key];
          this.logger.log(`Found example for component {{${varNumber}}}: ${exampleValue} (key: ${key})`);
          break;
        }
      }
      
      examples.push(exampleValue);
      this.logger.log(`Added example for component {{${varNumber}}}: ${exampleValue}`);
    }

    return examples;
  }

  /**
   * Convert Meta's format to our format
   * Follows Meta's exact structure for template conversion
   */
  convertFromMetaFormat(metaTemplate: MetaTemplateResponse): any {
    this.logger.log(`Converting Meta template to our format: ${JSON.stringify(metaTemplate, null, 2)}`);
    
    const template: any = {
      id: metaTemplate.id,
      name: metaTemplate.name,
      category: metaTemplate.category,
      language: metaTemplate.language,
      status: metaTemplate.status,
      quality_rating: metaTemplate.quality_rating,
      quality_score: metaTemplate.quality_score,
      components: metaTemplate.components
    };

    // Process each component
    for (const component of metaTemplate.components) {
      this.processMetaComponent(component, template);
    }

    this.logger.log(`Converted template: ${JSON.stringify(template, null, 2)}`);
    return template;
  }

  /**
   * Process individual Meta component and extract data to our format
   */
  private processMetaComponent(component: MetaTemplateComponent, template: any): void {
    switch (component.type) {
      case 'HEADER':
        this.processHeaderComponent(component, template);
        break;
      case 'BODY':
        this.processBodyComponent(component, template);
        break;
      case 'FOOTER':
        this.processFooterComponent(component, template);
        break;
      case 'BUTTONS':
        this.processButtonsComponent(component, template);
        break;
    }
  }

  /**
   * Process HEADER component
   */
  private processHeaderComponent(component: MetaTemplateComponent, template: any): void {
    if (component.format === 'TEXT') {
      // Text header
      template.type = 'text';
      if (component.text) {
        if (component.text.includes('{{')) {
          // Header has variables, use example text if available
          template.headerText = component.example?.header_text?.[0] || component.text;
        } else {
          // Header has static text
          template.headerText = component.text;
        }
      }
    } else if (component.format === 'IMAGE') {
      template.type = 'image';
      if (component.example?.header_handle?.[0]) {
        template.imageUrl = component.example.header_handle[0];
      }
    } else if (component.format === 'VIDEO') {
      template.type = 'video';
      if (component.example?.header_handle?.[0]) {
        template.imageUrl = component.example.header_handle[0];
      }
    } else if (component.format === 'DOCUMENT') {
      template.type = 'document';
      if (component.example?.header_handle?.[0]) {
        template.imageUrl = component.example.header_handle[0];
      }
    } else if (component.format === 'LOCATION') {
      template.type = 'location';
    }
  }

  /**
   * Process BODY component
   */
  private processBodyComponent(component: MetaTemplateComponent, template: any): void {
    if (component.text) {
      template.content = component.text;
    }
  }

  /**
   * Process FOOTER component
   */
  private processFooterComponent(component: MetaTemplateComponent, template: any): void {
    if (component.text) {
      template.footer = component.text;
    }
  }

  /**
   * Process BUTTONS component
   */
  private processButtonsComponent(component: MetaTemplateComponent, template: any): void {
    if (component.buttons && component.buttons.length > 0) {
      template.buttons = component.buttons.map(btn => ({
        id: btn.text.toLowerCase().replace(/\s+/g, '_'),
        title: btn.text,
        type: btn.type.toLowerCase(),
        url: btn.url,
        phone_number: btn.phone_number,
        otp_type: btn.otp_type,
        autofill_text: btn.autofill_text,
        package_name: btn.package_name,
        signature_hash: btn.signature_hash
      }));
    }
  }

  /**
   * Send a regular message (text, image, video, etc.) to a contact
   */
  async sendMessage(
    phoneNumberId: string,
    contactPhone: string,
    type: 'text' | 'image' | 'video' | 'audio' | 'document',
    content: {
      text?: string;
      media_url?: string;
      caption?: string;
      filename?: string;
    },
    countryCode: string = '+1',
    accessToken: string,
  ): Promise<{ success: boolean; message_id?: string; error?: string }> {
    try {
      const url = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;
      
      let messageData: any = {
        messaging_product: 'whatsapp',
        to: `${countryCode}${contactPhone}`,
        type: type,
      };

      switch (type) {
        case 'text':
          messageData.text = {
            body: content.text || '',
          };
          break;

        case 'image':
          messageData.image = {
            link: content.media_url,
            caption: content.caption || '',
          };
          break;

        case 'video':
          messageData.video = {
            link: content.media_url,
            caption: content.caption || '',
          };
          break;

        case 'audio':
          messageData.audio = {
            link: content.media_url,
          };
          break;

        case 'document':
          messageData.document = {
            link: content.media_url,
            filename: content.filename || 'document',
            caption: content.caption || '',
          };
          break;

        default:
          throw new BadRequestException(`Unsupported message type: ${type}`);
      }

      const response = await this.axiosInstance.post(url, messageData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data && response.data.messages && response.data.messages[0]) {
        return {
          success: true,
          message_id: response.data.messages[0].id,
        };
      } else {
        return {
          success: false,
          error: 'Invalid response from Meta API',
        };
      }
    } catch (error) {
      this.logger.error('Failed to send message via Meta API:', error);
      
      let errorMessage = 'Failed to send message';
      if (error.response?.data?.error?.message) {
        errorMessage = error.response.data.error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }
} 