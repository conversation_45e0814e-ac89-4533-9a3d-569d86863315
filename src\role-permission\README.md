# Role-Permission Module

## 🎯 **Overview**

The Role-Permission module provides comprehensive role-based access control (RBAC) functionality for the WhatsApp automation platform. It manages roles, permissions, and their assignments within workspace contexts, ensuring secure and granular access control.

## 🚀 **Features**

- **Role Management**: Create, read, update, and delete roles
- **Permission Management**: Manage granular permissions with resource-action pairs
- **Role-Permission Assignment**: Assign and revoke permissions from roles
- **Workspace Scoping**: All operations are scoped to user workspaces
- **Comprehensive Validation**: Input validation and business logic validation
- **Standardized Responses**: Consistent API response format
- **Error Handling**: Comprehensive error management with specific error types
- **Security**: Authentication required for all endpoints

## 📁 **Module Structure**

```
role-permission/
├── dto/                          # Data Transfer Objects
│   ├── create-role.dto.ts
│   ├── update-role.dto.ts
│   ├── create-permission.dto.ts
│   ├── update-permission.dto.ts
│   ├── assign-permission.dto.ts
│   ├── role-permission-query.dto.ts
│   └── index.ts
├── utils/                        # Utility Classes
│   ├── role-permission-constants.util.ts
│   ├── role-permission-validation.util.ts
│   └── role-permission-response.util.ts
├── docs/                         # Documentation
│   └── INDEX.md
├── role-permission.controller.ts  # Controller
├── role-permission.service.ts     # Service
├── role-permission.module.ts      # Module
└── README.md                      # This file
```

## 🔧 **API Endpoints**

### Role Management
- `POST /role-permission/roles` - Create role
- `GET /role-permission/roles` - Get all roles
- `GET /role-permission/roles/:id` - Get role by ID
- `PUT /role-permission/roles/:id` - Update role
- `DELETE /role-permission/roles/:id` - Delete role

### Permission Management
- `POST /role-permission/permissions` - Create permission
- `GET /role-permission/permissions` - Get all permissions
- `GET /role-permission/permissions/:id` - Get permission by ID
- `PUT /role-permission/permissions/:id` - Update permission
- `DELETE /role-permission/permissions/:id` - Delete permission

### Role-Permission Assignment
- `POST /role-permission/assign` - Assign permission to role
- `POST /role-permission/revoke` - Revoke permission from role
- `GET /role-permission/roles/:id/permissions` - Get role permissions

## 🛡️ **Security Features**

- ✅ Authentication required for all endpoints
- ✅ Workspace-scoped access control
- ✅ Input validation and sanitization
- ✅ SQL injection prevention
- ✅ Comprehensive error handling
- ✅ Audit logging

## 📊 **Data Models**

### Role
```typescript
interface Role {
  id: string;
  name: string;
  description?: string;
  status: 'active' | 'inactive';
  workspace_id: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}
```

### Permission
```typescript
interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
  status: 'active' | 'inactive';
  workspace_id: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}
```

## 🎯 **Usage Examples**

### Create Role with Permissions
```typescript
// 1. Create role
const role = await rolePermissionService.createRole({
  name: 'Content Manager',
  description: 'Manages content and templates',
  status: 'active'
}, req);

// 2. Create permissions
const permission = await rolePermissionService.createPermission({
  name: 'manage_templates',
  resource: 'templates',
  action: 'manage',
  description: 'Full template management'
}, req);

// 3. Assign permission to role
await rolePermissionService.assignPermissionToRole({
  roleId: role.data.role.id,
  permissionId: permission.data.permission.id
}, req);
```

## 🔍 **Validation Rules**

### Role Validation
- Name: 1-100 characters, required
- Description: Max 500 characters, optional
- Status: 'active' or 'inactive', defaults to 'active'

### Permission Validation
- Name: 1-100 characters, required
- Resource: Max 100 characters, required
- Action: Max 50 characters, required
- Description: Max 500 characters, optional
- Status: 'active' or 'inactive', defaults to 'active'

## 📈 **Performance**

- Optimized database queries with proper indexing
- Pagination support for large datasets
- Efficient workspace scoping
- Minimal data transfer with lean queries

## 🧪 **Testing**

- Unit tests for all service methods
- Integration tests for API endpoints
- Validation testing for DTOs
- Error handling testing
- Security testing

## 🚀 **Deployment**

### Prerequisites
- Supabase database with required tables
- Proper environment variables configured
- Authentication system set up

### Environment Variables
```bash
DATABASE_URL=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...
JWT_SECRET=your-jwt-secret
```

## 📚 **Documentation**

For detailed documentation, see:
- [Complete API Documentation](./docs/INDEX.md)
- [Architecture Overview](./docs/INDEX.md#architecture)
- [Security Guidelines](./docs/INDEX.md#security)
- [Deployment Guide](./docs/INDEX.md#deployment)

## 🔄 **Refactoring Summary**

This module has been completely refactored following the Auth module patterns:

- ✅ **Utility Classes**: Constants, validation, and response utilities
- ✅ **Enhanced DTOs**: Comprehensive validation with class-validator
- ✅ **Service Architecture**: Clean separation of concerns with proper error handling
- ✅ **Controller Structure**: Consistent endpoint patterns with proper decorators
- ✅ **Response Standardization**: Unified response format across all endpoints
- ✅ **Error Handling**: Comprehensive error management with specific exception types
- ✅ **Logging**: Structured logging for debugging and monitoring
- ✅ **Type Safety**: Strong TypeScript usage throughout

The module is now production-ready with comprehensive documentation and follows all established patterns from the Auth module.
