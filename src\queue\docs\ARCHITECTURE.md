# Queue Module Architecture

## Overview

This document outlines the architecture of the Queue Module, including its components, data flow, and integration patterns using Bull MQ and Redis.

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Queue         │
│   Application   │◄──►│   (NestJS)      │◄──►│   Module        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Auth Module   │    │   Redis         │
                       │   (JWT)         │    │   (Bull MQ)     │
                       └─────────────────┘    └─────────────────┘
                                                        │
                                ┌───────────────────────┼───────────────────────┐
                                ▼                       ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
                       │   Campaign      │    │   Template      │    │   Meta API      │
                       │   Messages      │    │   Module        │    │   Service       │
                       │   Queue         │    │                 │    │                 │
                       └─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   WhatsApp      │
                       │   Business API  │
                       └─────────────────┘
```

## Module Components

### 1. Queue Service
- **Purpose**: Core queue management and message operations
- **Responsibilities**:
  - Message queuing and scheduling
  - Queue statistics and monitoring
  - Queue management (pause, resume, clear)
  - Job prioritization and retry logic

### 2. Queue Processors
- **Purpose**: Process queued messages
- **Responsibilities**:
  - Campaign message processing
  - Scheduled message processing
  - Retry message processing
  - Error handling and logging

### 3. Message Status Service
- **Purpose**: Track and manage message delivery status
- **Responsibilities**:
  - Update message status (SENT, DELIVERED, READ, FAILED)
  - Track campaign execution statistics
  - Log error messages and retry attempts
  - Provide campaign analytics and reporting

### 4. Queue Controller
- **Purpose**: HTTP API endpoints for queue management
- **Responsibilities**:
  - Queue statistics API
  - Queue management operations
  - Authentication and authorization
  - Response formatting

### 5. Queue Module
- **Purpose**: Module configuration and dependency injection
- **Responsibilities**:
  - Bull MQ configuration
  - Redis connection setup
  - Queue registration
  - Service provider configuration

## Data Flow

### Message Processing Flow

```
1. Campaign Creation → Queue Service
2. Queue Service → Redis (Bull MQ)
3. Redis → Queue Processor
4. Queue Processor → Template Service
5. Queue Processor → Meta API Service
6. Meta API Service → WhatsApp Business API
7. Queue Processor → Message Status Update
8. Queue Processor → Job Completion
```

### Queue Management Flow

```
1. API Request → Queue Controller
2. Queue Controller → Queue Service
3. Queue Service → Redis (Bull MQ)
4. Redis → Queue Statistics
5. Queue Service → Response Formatting
6. Queue Controller → HTTP Response
```

## Redis/Bull MQ Architecture

### Queue Structure

```
Redis Keys:
├── bull:campaign-messages:waiting
├── bull:campaign-messages:active
├── bull:campaign-messages:completed
├── bull:campaign-messages:failed
├── bull:scheduled-campaign-messages:waiting
├── bull:scheduled-campaign-messages:active
├── bull:scheduled-campaign-messages:completed
├── bull:scheduled-campaign-messages:failed
├── bull:campaign-retry-messages:waiting
├── bull:campaign-retry-messages:active
├── bull:campaign-retry-messages:completed
└── bull:campaign-retry-messages:failed
```

### Job Data Structure

```typescript
interface CampaignMessage {
  campaignId: string;
  contactId: string;
  phoneNumber: string;
  countryCode: string;
  templateId: string;
  phoneNumberId: string;
  variableMapping: Record<string, any>;
  workspaceId: number;
  userId: string;
  retryCount: number;
  priority: 'HIGH' | 'NORMAL' | 'LOW';
  scheduledAt?: Date;
}
```

### Job Options

```typescript
interface JobOptions {
  priority: number;           // 1-10 (10 = highest)
  attempts: number;           // Max retry attempts
  backoff: {
    type: 'exponential';
    delay: number;            // Initial delay in ms
  };
  removeOnComplete: number;   // Keep N completed jobs
  removeOnFail: number;       // Keep N failed jobs
  delay?: number;             // Delay before processing
}
```

## Integration Patterns

### 1. Campaign Module Integration

```typescript
// Campaign Service
@Injectable()
export class CampaignService {
  constructor(
    private queueService: QueueService,
    private messageStatusService: MessageStatusService,
    // ... other dependencies
  ) {}

  async startCampaign(campaignId: string) {
    // Create campaign execution record
    await this.messageStatusService.createOrUpdateCampaignExecution(
      campaignId,
      workspaceId,
      userId,
      totalContacts
    );
    
    // Queue campaign messages
    await this.queueService.sendCampaignBatch(batchMessage);
  }
}
```

### 2. Template Module Integration

```typescript
// Queue Processor
@Processor('campaign-messages')
export class CampaignMessageProcessor {
  constructor(
    private templateService: TemplateService,
    private messageStatusService: MessageStatusService,
    // ... other dependencies
  ) {}

  @Process('process-campaign-message')
  async processCampaignMessage(job: Job<CampaignMessage>) {
    const template = await this.templateService.getTemplateById(
      job.data.templateId
    );
    
    try {
      // Process message with template
      const result = await this.sendMessage(job.data, template);
      
      // Update message status
      await this.messageStatusService.updateMessageStatus(
        job.data.campaignId,
        job.data.contactId,
        'SENT',
        result.messageId
      );
    } catch (error) {
      // Update failed status
      await this.messageStatusService.updateMessageStatus(
        job.data.campaignId,
        job.data.contactId,
        'FAILED',
        undefined,
        error.message,
        job.data.retryCount
      );
    }
  }
}
```

### 3. Meta API Integration

```typescript
// Queue Processor
@Processor('campaign-messages')
export class CampaignMessageProcessor {
  constructor(
    private metaApiService: MetaApiService,
    // ... other dependencies
  ) {}

  @Process('process-campaign-message')
  async processCampaignMessage(job: Job<CampaignMessage>) {
    const result = await this.metaApiService.sendTemplateMessage(
      job.data.phoneNumberId,
      template.name,
      job.data.phoneNumber,
      job.data.countryCode,
      job.data.variableMapping,
      template.language,
      credentials.access_token
    );
  }
}
```

## Performance Considerations

### Redis Configuration

```yaml
# docker-compose.redis.yml
redis:
  image: redis:7-alpine
  command: >
    redis-server
    --maxmemory 256mb
    --maxmemory-policy allkeys-lru
    --save 900 1
    --save 300 10
    --save 60 10000
```

### Queue Configuration

```typescript
// Queue Module
BullModule.forRootAsync({
  useFactory: async (configService: ConfigService) => ({
    redis: {
      host: configService.get('REDIS_HOST'),
      port: configService.get('REDIS_PORT'),
      password: configService.get('REDIS_PASSWORD'),
      db: configService.get('REDIS_DB'),
    },
    defaultJobOptions: {
      removeOnComplete: 100,
      removeOnFail: 50,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
  }),
})
```

### Concurrency Settings

```typescript
// Queue Processors
@Processor('campaign-messages')
export class CampaignMessageProcessor {
  @Process('process-campaign-message', { concurrency: 5 })
  async processCampaignMessage(job: Job<CampaignMessage>) {
    // Process up to 5 messages concurrently
  }
}
```

## Monitoring and Logging

### Queue Statistics

```typescript
// Queue Service
async getQueueStats(): Promise<any> {
  const [campaignStats, scheduledStats, retryStats] = await Promise.all([
    this.campaignQueue.getJobCounts(),
    this.scheduledQueue.getJobCounts(),
    this.retryQueue.getJobCounts(),
  ]);

  return {
    campaignMessages: campaignStats,
    scheduledMessages: scheduledStats,
    retryMessages: retryStats,
    total: {
      waiting: campaignStats.waiting + scheduledStats.waiting + retryStats.waiting,
      active: campaignStats.active + scheduledStats.active + retryStats.active,
      completed: campaignStats.completed + scheduledStats.completed + retryStats.completed,
      failed: campaignStats.failed + scheduledStats.failed + retryStats.failed,
    },
  };
}
```

### Event Logging

```typescript
// Queue Processors
@OnQueueActive()
onActive(job: Job<CampaignMessage>) {
  this.logger.debug(`Processing campaign message job ${job.id}`);
}

@OnQueueCompleted()
onCompleted(job: Job<CampaignMessage>, result: any) {
  this.logger.debug(`Campaign message job ${job.id} completed`);
}

@OnQueueFailed()
onFailed(job: Job<CampaignMessage>, err: Error) {
  this.logger.error(`Campaign message job ${job.id} failed: ${err.message}`);
}
```

## Error Handling Strategy

### Retry Logic

```typescript
// Queue Configuration
const jobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential' as const,
    delay: 2000,
  },
  removeOnComplete: 100,
  removeOnFail: 50,
};
```

### Error Classification

1. **Temporary Errors**: Network issues, API rate limits
2. **Permanent Errors**: Invalid phone numbers, template errors
3. **System Errors**: Redis connection issues, service unavailability

### Error Recovery

```typescript
// Queue Processor
@Process('process-campaign-message')
async processCampaignMessage(job: Job<CampaignMessage>) {
  try {
    // Process message
  } catch (error) {
    if (error.type === 'TEMPORARY') {
      // Will be retried automatically
      throw error;
    } else if (error.type === 'PERMANENT') {
      // Move to dead letter queue
      await this.moveToDeadLetterQueue(job, error);
    }
  }
}
```

## Scalability Considerations

### Horizontal Scaling

- **Multiple Redis Instances**: Redis Cluster for high availability
- **Multiple Queue Processors**: Scale processors across multiple servers
- **Load Balancing**: Distribute queue processing load

### Vertical Scaling

- **Redis Memory**: Increase Redis memory for larger queues
- **Processor Concurrency**: Increase concurrent job processing
- **Connection Pooling**: Optimize Redis connection pooling

## Security Architecture

### Authentication

```typescript
// Queue Controller
@Controller('queue')
@UseGuards(AuthGuard)
export class QueueController {
  // All endpoints require authentication
}
```

### Authorization

```typescript
// Queue Service
async getQueueStats(req: any): Promise<any> {
  // Validate user permissions
  await this.validateUserPermissions(req.user);
  // Return queue statistics
}
```

### Data Protection

- **Redis Security**: Password protection, network isolation
- **Job Data Encryption**: Sensitive data encryption in Redis
- **Access Control**: Role-based access to queue operations

## Deployment Architecture

### Docker Configuration

```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["node", "dist/main"]
```

### Kubernetes Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: queue-module
spec:
  replicas: 3
  selector:
    matchLabels:
      app: queue-module
  template:
    spec:
      containers:
      - name: queue-module
        image: queue-module:latest
        env:
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PORT
          value: "6379"
```

## Future Enhancements

### Planned Features

1. **Queue Dashboard**: Web-based queue monitoring
2. **Dead Letter Queue**: Handle permanently failed messages
3. **Queue Analytics**: Advanced performance metrics
4. **Auto-scaling**: Dynamic queue processor scaling
5. **Message Encryption**: End-to-end message encryption

### Technical Improvements

1. **Redis Cluster**: High availability Redis setup
2. **Message Compression**: Reduce Redis memory usage
3. **Batch Processing**: Process multiple messages in batches
4. **Queue Partitioning**: Partition queues by workspace
5. **Real-time Monitoring**: WebSocket-based queue monitoring
