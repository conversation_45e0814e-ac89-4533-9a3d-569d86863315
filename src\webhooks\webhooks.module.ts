import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { WebhooksController } from './webhooks.controller';
import { WebhooksService } from './webhooks.service';
import { WhatsAppMessage, WhatsAppMessageSchema } from '../schema/whatsapp-message.schema';
import { WhatsAppStatus, WhatsAppStatusSchema } from '../schema/whatsapp-status.schema';
import { WhatsAppModule } from '../whatsapp/whatsapp.module';
import { SupabaseModule } from '../supabase/supabase.module';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: WhatsAppMessage.name, schema: WhatsAppMessageSchema },
      { name: WhatsAppStatus.name, schema: WhatsAppStatusSchema },
    ]),
    WhatsAppModule,
    SupabaseModule,
  ],
  controllers: [WebhooksController],
  providers: [WebhooksService],
  exports: [WebhooksService],
})
export class WebhooksModule {} 