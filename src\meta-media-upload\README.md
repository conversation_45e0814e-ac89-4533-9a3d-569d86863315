# 📁 Meta Media Upload Module

## 📋 Overview
This module provides functionality for uploading large files to Meta's social graph using the Resumable Upload API. It supports file uploads with resume capability for interrupted sessions, allowing users to upload files up to 100MB in size.

## 🎯 Features

### **Core Functionality**
- ✅ Start upload sessions with Meta's Resumable Upload API
- ✅ Upload file data in chunks with support for resuming
- ✅ Resume interrupted upload sessions
- ✅ Support for multiple file formats (PDF, JPEG, JPG, PNG, MP4)
- ✅ File size validation (up to 100MB)
- ✅ Comprehensive error handling and validation
- ✅ Authentication integration with existing auth system

### **Supported File Types**
- **Documents**: PDF
- **Images**: JPEG, JPG, PNG  
- **Videos**: MP4

## 🏗️ Module Structure

```
src/meta-media-upload/
├── dto/                              # Data Transfer Objects
│   ├── upload-session.dto.ts         # Start upload session DTOs
│   ├── upload-file.dto.ts            # Upload file DTOs
│   ├── resume-upload.dto.ts          # Resume upload DTOs
│   └── index.ts                      # Centralized DTO exports
├── utils/                            # Utility classes
│   ├── upload-response.util.ts       # Response formatting utilities
│   ├── upload-validation.util.ts     # Validation utilities
│   └── upload-constants.util.ts      # Constants and configuration
├── meta-media-upload.controller.ts   # API endpoints controller
├── meta-media-upload.service.ts      # Business logic service
├── meta-media-upload.module.ts       # Module configuration
└── README.md                         # This documentation
```

## 🔧 Component Details

### **1. MetaMediaUploadController**
**Purpose**: HTTP request/response handling for upload operations
**Responsibilities**:
- Handle upload session creation requests
- Process file upload requests with multipart form data
- Manage upload session resume requests
- Validate authentication and authorization
- Format API responses

**Key Endpoints**:
```typescript
@Post('start-session')     // Start upload session
@Post('upload')            // Upload file data
@Get('resume/:sessionId')  // Resume interrupted upload
@Get('health')             // Health check
```

### **2. MetaMediaUploadService**
**Purpose**: Business logic implementation for Meta API integration
**Responsibilities**:
- Integrate with Meta's Resumable Upload API
- Handle file upload session management
- Process file data uploads
- Manage upload session resumption
- Error handling and validation

**Key Methods**:
```typescript
async startUploadSession()  // Create upload session
async uploadFile()          // Upload file data
async resumeUpload()        // Resume interrupted session
```

### **3. Utility Classes**
- **UploadResponseUtil**: Standardized response formatting
- **UploadValidationUtil**: Input validation and file type checking
- **UploadConstantsUtil**: Configuration constants and error messages

## 📡 API Endpoints

### **1. Start Upload Session**
```http
POST /meta-media-upload/start-session
Content-Type: application/json
Authorization: Bearer <access_token>
x-meta-app-id: <meta_app_id>
x-meta-access-token: <meta_access_token>

{
  "fileName": "document.pdf",
  "fileLength": 1024000,
  "fileType": "application/pdf"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Upload session created successfully",
  "data": {
    "id": "upload:abc123",
    "success": true,
    "message": "Upload session created successfully"
  },
  "status_code": 201,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### **2. Upload File**
```http
POST /meta-media-upload/upload
Content-Type: multipart/form-data
Authorization: Bearer <access_token>
x-meta-access-token: <meta_access_token>

Form Data:
- file: <binary_file_data>
- uploadSessionId: "upload:abc123"
- fileOffset: 0 (optional)
```

**Response**:
```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "handleId": "2:c2FtcGxl...",
    "success": true,
    "message": "File uploaded successfully",
    "uploadSessionId": "upload:abc123"
  },
  "status_code": 200,
  "timestamp": "2024-01-15T10:31:00.000Z"
}
```

### **3. Resume Upload Session**
```http
GET /meta-media-upload/resume/upload:abc123
Authorization: Bearer <access_token>
x-meta-access-token: <meta_access_token>
```

**Response**:
```json
{
  "success": true,
  "message": "Upload session status retrieved successfully",
  "data": {
    "id": "upload:abc123",
    "fileOffset": 512000,
    "success": true,
    "message": "Upload session status retrieved successfully"
  },
  "status_code": 200,
  "timestamp": "2024-01-15T10:32:00.000Z"
}
```

## 🔐 Authentication & Authorization

### **Required Headers**
- `Authorization: Bearer <access_token>` - User authentication token
- `x-meta-app-id: <meta_app_id>` - Meta App ID (for start session)

**Note**: Meta access token is automatically retrieved from user's stored credentials in the workspace.

### **Security Features**
- JWT token validation through AuthGuard
- User context extraction from authenticated requests
- Input validation and sanitization
- File type and size validation
- Meta API error handling

## 📝 Usage Examples

### **Complete Upload Flow**
```typescript
// 1. Start upload session
const sessionResponse = await fetch('/meta-media-upload/start-session', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>',
    'x-meta-app-id': '<app_id>'
  },
  body: JSON.stringify({
    fileName: 'document.pdf',
    fileLength: 1024000,
    fileType: 'application/pdf'
  })
});

const sessionData = await sessionResponse.json();
const uploadSessionId = sessionData.data.id;

// 2. Upload file
const formData = new FormData();
formData.append('file', fileBlob);
formData.append('uploadSessionId', uploadSessionId);

const uploadResponse = await fetch('/meta-media-upload/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer <token>'
  },
  body: formData
});

const uploadData = await uploadResponse.json();
const handleId = uploadData.data.handleId;
```

### **Resume Interrupted Upload**
```typescript
// Check upload status
const resumeResponse = await fetch(`/meta-media-upload/resume/${uploadSessionId}`, {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer <token>'
  }
});

const resumeData = await resumeResponse.json();
const fileOffset = resumeData.data.fileOffset;

// Resume upload from offset
const formData = new FormData();
formData.append('file', fileBlob.slice(fileOffset));
formData.append('uploadSessionId', uploadSessionId);
formData.append('fileOffset', fileOffset.toString());
```

## ⚠️ Error Handling

### **Common Error Scenarios**
- **Invalid file type**: File format not supported
- **File too large**: Exceeds 100MB limit
- **Invalid session**: Upload session not found or expired
- **Meta API errors**: Token expired, permission denied, etc.
- **Network issues**: Timeout or connection problems

### **Error Response Format**
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information",
  "status_code": 400,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 🚀 Getting Started

### **Prerequisites**
- Meta App ID and access token
- Valid user authentication
- File in supported format (PDF, JPEG, JPG, PNG, MP4)

### **Integration Steps**
1. Import the module in your application
2. Ensure Meta credentials are available
3. Use the provided endpoints for upload operations
4. Handle responses and errors appropriately

## 📚 Related Documentation
- [API Endpoints](./docs/API_ENDPOINTS.md) - Detailed endpoint documentation
- [Architecture](./docs/ARCHITECTURE.md) - Technical architecture details
- [Security](./docs/SECURITY.md) - Security considerations and best practices
- [Testing](./docs/TESTING.md) - Testing guidelines and examples
- [Deployment](./docs/DEPLOYMENT.md) - Deployment configuration

## 🔄 Meta API Integration

This module integrates with Meta's Resumable Upload API v23.0:
- **Base URL**: `https://graph.facebook.com/v23.0`
- **Authentication**: OAuth 2.0 with access tokens
- **File Limits**: Up to 100MB per file
- **Supported Formats**: PDF, JPEG, JPG, PNG, MP4

For more information about Meta's Resumable Upload API, refer to the [official documentation](https://developers.facebook.com/docs/graph-api/guides/upload).
