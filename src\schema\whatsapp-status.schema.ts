import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type WhatsAppStatusDocument = WhatsAppStatus & Document;

@Schema({ timestamps: true })
export class WhatsAppStatus {
  @Prop({ required: true })
  messageId: string;

  @Prop({ required: true })
  status: string;

  @Prop({ required: true })
  timestamp: Date;

  @Prop({ required: true })
  recipientId: string;

  @Prop({ required: true })
  phoneNumberId: string;

  @Prop({ type: Object })
  metadata?: any;
}

export const WhatsAppStatusSchema = SchemaFactory.createForClass(WhatsAppStatus); 