import { Test, TestingModule } from '@nestjs/testing';
import { CampaignMessageProcessor, ScheduledCampaignMessageProcessor, RetryCampaignMessageProcessor } from '../../queue.processor';
import { MetaApiService } from '../../../meta-api/meta-api.service';
import { TemplateService } from '../../../template/template.service';
import { MessageStatusService } from '../../message-status.service';
import { MetaOnboardingService } from '../../../meta-onboarding/meta-onboarding.service';

describe('CampaignMessageProcessor', () => {
  let processor: CampaignMessageProcessor;
  let mockMetaApiService: any;
  let mockTemplateService: any;
  let mockMessageStatusService: any;
  let mockMetaOnboardingService: any;

  beforeEach(async () => {
    const mockMetaApi = {
      sendTemplateMessage: jest.fn(),
    };

    const mockTemplate = {
      getTemplateById: jest.fn(),
    };

    const mockMessageStatus = {
      updateMessageStatus: jest.fn(),
    };

    const mockMetaOnboarding = {
      getMetaCredentialsByPhoneId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignMessageProcessor,
        {
          provide: MetaApiService,
          useValue: mockMetaApi,
        },
        {
          provide: TemplateService,
          useValue: mockTemplate,
        },
        {
          provide: MessageStatusService,
          useValue: mockMessageStatus,
        },
        {
          provide: MetaOnboardingService,
          useValue: mockMetaOnboarding,
        },
      ],
    }).compile();

    processor = module.get<CampaignMessageProcessor>(CampaignMessageProcessor);
    mockMetaApiService = module.get(MetaApiService);
    mockTemplateService = module.get(TemplateService);
    mockMessageStatusService = module.get(MessageStatusService);
    mockMetaOnboardingService = module.get(MetaOnboardingService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processCampaignMessage', () => {
    it('should process campaign message successfully', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {
            body: {
              '{{1}}': 'John',
            },
          },
          workspaceId: 1,
          userId: 'user-123',
        },
      };

      const mockTemplate = {
        status: 'success',
        data: {
          template: {
            name: 'welcome_template',
            language: 'en',
          },
        },
      };

      const mockCredentials = {
        data: {
          credentials: {
            access_token: 'mock-access-token',
          },
        },
      };

      const mockApiResult = {
        messageId: 'message-123',
        success: true,
      };

      mockTemplateService.getTemplateById.mockResolvedValue(mockTemplate);
      mockMetaOnboardingService.getMetaCredentialsByPhoneId.mockResolvedValue(mockCredentials);
      mockMetaApiService.sendTemplateMessage.mockResolvedValue(mockApiResult);
      mockMessageStatusService.updateMessageStatus.mockResolvedValue(undefined);

      const result = await processor.processCampaignMessage(mockJob);

      expect(mockTemplateService.getTemplateById).toHaveBeenCalledWith('template-789', {
        user: { id: 'user-123' },
      });
      expect(mockMetaOnboardingService.getMetaCredentialsByPhoneId).toHaveBeenCalledWith(
        'phone-123',
        { user: { id: 'user-123' } }
      );
      expect(mockMetaApiService.sendTemplateMessage).toHaveBeenCalledWith(
        'phone-123',
        'welcome_template',
        '1234567890',
        '91',
        {
          body: {
            '{{1}}': 'John',
          },
        },
        'en',
        'mock-access-token'
      );
      expect(mockMessageStatusService.updateMessageStatus).toHaveBeenCalledWith(
        'campaign-123',
        'contact-456',
        'SENT',
        'message-123'
      );
      expect(result).toEqual({
        success: true,
        messageId: 'message-123',
      });
    });

    it('should handle template not found', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {},
          workspaceId: 1,
          userId: 'user-123',
        },
      };

      mockTemplateService.getTemplateById.mockResolvedValue(null);

      await expect(processor.processCampaignMessage(mockJob)).rejects.toThrow(
        'Template not found or not approved: template-789'
      );
    });

    it('should handle template not approved', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {},
          workspaceId: 1,
          userId: 'user-123',
        },
      };

      const mockTemplate = {
        status: 'pending',
        data: {
          template: {
            name: 'welcome_template',
            language: 'en',
          },
        },
      };

      mockTemplateService.getTemplateById.mockResolvedValue(mockTemplate);

      await expect(processor.processCampaignMessage(mockJob)).rejects.toThrow(
        'Template not found or not approved: template-789'
      );
    });

    it('should handle API error and update status to failed', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {},
          workspaceId: 1,
          userId: 'user-123',
        },
      };

      const mockTemplate = {
        status: 'success',
        data: {
          template: {
            name: 'welcome_template',
            language: 'en',
          },
        },
      };

      const mockCredentials = {
        data: {
          credentials: {
            access_token: 'mock-access-token',
          },
        },
      };

      mockTemplateService.getTemplateById.mockResolvedValue(mockTemplate);
      mockMetaOnboardingService.getMetaCredentialsByPhoneId.mockResolvedValue(mockCredentials);
      mockMetaApiService.sendTemplateMessage.mockRejectedValue(new Error('API Error'));
      mockMessageStatusService.updateMessageStatus.mockResolvedValue(undefined);

      await expect(processor.processCampaignMessage(mockJob)).rejects.toThrow('API Error');

      expect(mockMessageStatusService.updateMessageStatus).toHaveBeenCalledWith(
        'campaign-123',
        'contact-456',
        'FAILED',
        undefined,
        'API Error'
      );
    });
  });

  describe('buildMessageComponents', () => {
    it('should build message components correctly', () => {
      const variableMapping = {
        body: {
          '{{1}}': 'Hello',
          '{{2}}': 'World',
        },
        header: {
          '{{1}}': 'Welcome',
        },
        footer: {
          '{{1}}': 'Thank you',
        },
      };

      const result = (processor as any).buildMessageComponents(variableMapping);

      expect(result).toEqual([
        {
          type: 'body',
          parameters: [
            { type: 'text', text: 'Hello' },
            { type: 'text', text: 'World' },
          ],
        },
        {
          type: 'header',
          parameters: [
            { type: 'text', text: 'Welcome' },
          ],
        },
        {
          type: 'footer',
          parameters: [
            { type: 'text', text: 'Thank you' },
          ],
        },
      ]);
    });

    it('should handle empty variable mapping', () => {
      const variableMapping = {};

      const result = (processor as any).buildMessageComponents(variableMapping);

      expect(result).toEqual([]);
    });

    it('should handle undefined variable mapping', () => {
      const variableMapping = undefined;

      const result = (processor as any).buildMessageComponents(variableMapping);

      expect(result).toEqual([]);
    });
  });
});

describe('ScheduledCampaignMessageProcessor', () => {
  let processor: ScheduledCampaignMessageProcessor;
  let mockMetaApiService: any;
  let mockTemplateService: any;
  let mockMessageStatusService: any;
  let mockMetaOnboardingService: any;

  beforeEach(async () => {
    const mockMetaApi = {
      sendTemplateMessage: jest.fn(),
    };

    const mockTemplate = {
      getTemplateById: jest.fn(),
    };

    const mockMessageStatus = {
      updateMessageStatus: jest.fn(),
    };

    const mockMetaOnboarding = {
      getMetaCredentialsByWorkspace: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduledCampaignMessageProcessor,
        {
          provide: MetaApiService,
          useValue: mockMetaApi,
        },
        {
          provide: TemplateService,
          useValue: mockTemplate,
        },
        {
          provide: MessageStatusService,
          useValue: mockMessageStatus,
        },
        {
          provide: MetaOnboardingService,
          useValue: mockMetaOnboarding,
        },
      ],
    }).compile();

    processor = module.get<ScheduledCampaignMessageProcessor>(ScheduledCampaignMessageProcessor);
    mockMetaApiService = module.get(MetaApiService);
    mockTemplateService = module.get(TemplateService);
    mockMessageStatusService = module.get(MessageStatusService);
    mockMetaOnboardingService = module.get(MetaOnboardingService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processScheduledCampaignMessage', () => {
    it('should process scheduled message when time has come', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {},
          workspaceId: 1,
          userId: 'user-123',
          scheduledAt: new Date(Date.now() - 60000), // 1 minute ago
        },
      };

      const mockTemplate = {
        status: 'success',
        data: {
          template: {
            name: 'welcome_template',
            language: 'en',
          },
        },
      };

      const mockCredentials = {
        status: 'success',
        data: [
          {
            access_token: 'mock-access-token',
          },
        ],
      };

      const mockApiResult = {
        messageId: 'message-123',
        success: true,
      };

      mockTemplateService.getTemplateById.mockResolvedValue(mockTemplate);
      mockMetaOnboardingService.getMetaCredentialsByWorkspace.mockResolvedValue(mockCredentials);
      mockMetaApiService.sendTemplateMessage.mockResolvedValue(mockApiResult);
      mockMessageStatusService.updateMessageStatus.mockResolvedValue(undefined);

      const result = await processor.processScheduledCampaignMessage(mockJob);

      expect(result).toEqual({
        success: true,
        messageId: 'message-123',
      });
    });

    it('should reschedule message if scheduled time is in the future', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {},
          workspaceId: 1,
          userId: 'user-123',
          scheduledAt: new Date(Date.now() + 60000), // 1 minute in future
        },
      };

      await expect(processor.processScheduledCampaignMessage(mockJob)).rejects.toThrow(
        'Message scheduled for future, rescheduling'
      );
    });

    it('should handle missing credentials', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {},
          workspaceId: 1,
          userId: 'user-123',
          scheduledAt: new Date(Date.now() - 60000),
        },
      };

      const mockTemplate = {
        status: 'success',
        data: {
          template: {
            name: 'welcome_template',
            language: 'en',
          },
        },
      };

      const mockCredentials = {
        status: 'error',
        data: null,
      };

      mockTemplateService.getTemplateById.mockResolvedValue(mockTemplate);
      mockMetaOnboardingService.getMetaCredentialsByWorkspace.mockResolvedValue(mockCredentials);

      await expect(processor.processScheduledCampaignMessage(mockJob)).rejects.toThrow(
        'Meta credentials not found'
      );
    });
  });
});

describe('RetryCampaignMessageProcessor', () => {
  let processor: RetryCampaignMessageProcessor;
  let mockMetaApiService: any;
  let mockTemplateService: any;
  let mockMessageStatusService: any;
  let mockMetaOnboardingService: any;

  beforeEach(async () => {
    const mockMetaApi = {
      sendTemplateMessage: jest.fn(),
    };

    const mockTemplate = {
      getTemplateById: jest.fn(),
    };

    const mockMessageStatus = {
      updateMessageStatus: jest.fn(),
    };

    const mockMetaOnboarding = {
      getMetaCredentialsByWorkspace: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RetryCampaignMessageProcessor,
        {
          provide: MetaApiService,
          useValue: mockMetaApi,
        },
        {
          provide: TemplateService,
          useValue: mockTemplate,
        },
        {
          provide: MessageStatusService,
          useValue: mockMessageStatus,
        },
        {
          provide: MetaOnboardingService,
          useValue: mockMetaOnboarding,
        },
      ],
    }).compile();

    processor = module.get<RetryCampaignMessageProcessor>(RetryCampaignMessageProcessor);
    mockMetaApiService = module.get(MetaApiService);
    mockTemplateService = module.get(TemplateService);
    mockMessageStatusService = module.get(MessageStatusService);
    mockMetaOnboardingService = module.get(MetaOnboardingService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processRetryCampaignMessage', () => {
    it('should process retry message successfully', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {},
          workspaceId: 1,
          userId: 'user-123',
        },
        attemptsMade: 1,
      };

      const mockTemplate = {
        status: 'success',
        data: {
          template: {
            name: 'welcome_template',
            language: 'en',
          },
        },
      };

      const mockCredentials = {
        status: 'success',
        data: [
          {
            access_token: 'mock-access-token',
          },
        ],
      };

      const mockApiResult = {
        messageId: 'message-123',
        success: true,
      };

      mockTemplateService.getTemplateById.mockResolvedValue(mockTemplate);
      mockMetaOnboardingService.getMetaCredentialsByWorkspace.mockResolvedValue(mockCredentials);
      mockMetaApiService.sendTemplateMessage.mockResolvedValue(mockApiResult);
      mockMessageStatusService.updateMessageStatus.mockResolvedValue(undefined);

      const result = await processor.processRetryCampaignMessage(mockJob);

      expect(result).toEqual({
        success: true,
        messageId: 'message-123',
        retryAttempt: 2,
      });
    });

    it('should handle retry failure and update status', async () => {
      const mockJob = {
        data: {
          campaignId: 'campaign-123',
          contactId: 'contact-456',
          phoneNumber: '1234567890',
          countryCode: '91',
          templateId: 'template-789',
          phoneNumberId: 'phone-123',
          variableMapping: {},
          workspaceId: 1,
          userId: 'user-123',
        },
        attemptsMade: 2,
      };

      const mockTemplate = {
        status: 'success',
        data: {
          template: {
            name: 'welcome_template',
            language: 'en',
          },
        },
      };

      const mockCredentials = {
        status: 'success',
        data: [
          {
            access_token: 'mock-access-token',
          },
        ],
      };

      mockTemplateService.getTemplateById.mockResolvedValue(mockTemplate);
      mockMetaOnboardingService.getMetaCredentialsByWorkspace.mockResolvedValue(mockCredentials);
      mockMetaApiService.sendTemplateMessage.mockRejectedValue(new Error('Retry failed'));
      mockMessageStatusService.updateMessageStatus.mockResolvedValue(undefined);

      await expect(processor.processRetryCampaignMessage(mockJob)).rejects.toThrow('Retry failed');

      expect(mockMessageStatusService.updateMessageStatus).toHaveBeenCalledWith(
        'campaign-123',
        'contact-456',
        'FAILED',
        undefined,
        'Retry failed'
      );
    });
  });
});
