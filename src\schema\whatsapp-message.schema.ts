import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type WhatsAppMessageDocument = WhatsAppMessage & Document;

@Schema({ timestamps: true })
export class WhatsAppMessage {
  @Prop({ required: true })
  messageId: string;

  @Prop({ required: true })
  from: string;

  @Prop({ required: true })
  timestamp: Date;

  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  phoneNumberId: string;

  @Prop({ required: true })
  displayPhoneNumber: string;

  @Prop({ type: Object })
  content: any;

  @Prop({ default: false })
  processed: boolean;

  @Prop({ type: Object })
  metadata?: any;
}

export const WhatsAppMessageSchema = SchemaFactory.createForClass(WhatsAppMessage); 