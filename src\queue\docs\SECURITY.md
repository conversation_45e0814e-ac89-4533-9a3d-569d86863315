# Queue Module Security Documentation

## Overview

This document outlines the security measures, best practices, and guidelines for the Queue Module to ensure secure message processing and queue management.

## Security Architecture

### Authentication & Authorization

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client        │    │   API Gateway   │    │   Queue         │
│   Application   │◄──►│   (JWT Auth)    │◄──►│   Module        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Role-Based    │    │   Redis         │
                       │   Access        │    │   Security      │
                       │   Control       │    │   (Password)    │
                       └─────────────────┘    └─────────────────┘
```

## Authentication

### JWT Token Authentication

All queue endpoints require valid JWT tokens for authentication.

#### Token Structure

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "user-123",
    "workspaceId": 1,
    "role": "admin",
    "permissions": ["queue:read", "queue:write", "queue:admin"],
    "iat": 1640995200,
    "exp": 1641081600
  }
}
```

#### Token Validation

```typescript
// Auth Guard
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    
    if (!token) {
      throw new UnauthorizedException('Token not provided');
    }

    try {
      const payload = this.jwtService.verify(token);
      request.user = payload;
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

### Role-Based Access Control (RBAC)

#### User Roles

| Role | Permissions | Description |
|------|-------------|-------------|
| **Admin** | `queue:*` | Full access to all queue operations |
| **Manager** | `queue:read`, `queue:write` | Can view stats and manage queues |
| **User** | `queue:read` | Can only view queue statistics |
| **Viewer** | `queue:read` | Read-only access to queue stats |

#### Permission Matrix

| Operation | Admin | Manager | User | Viewer |
|-----------|-------|---------|------|--------|
| View Queue Stats | ✅ | ✅ | ✅ | ✅ |
| Pause Queue | ✅ | ✅ | ❌ | ❌ |
| Resume Queue | ✅ | ✅ | ❌ | ❌ |
| Clear Queue | ✅ | ❌ | ❌ | ❌ |
| View Job Details | ✅ | ✅ | ❌ | ❌ |
| Retry Failed Jobs | ✅ | ✅ | ❌ | ❌ |

#### Permission Guard

```typescript
@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>('permissions', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredPermissions) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredPermissions.some((permission) => user.permissions?.includes(permission));
  }
}
```

#### Usage in Controllers

```typescript
@Controller('queue')
@UseGuards(AuthGuard, PermissionsGuard)
export class QueueController {
  @Get('stats')
  @RequirePermissions('queue:read')
  async getQueueStats() {
    // Implementation
  }

  @Post('pause/:queueName')
  @RequirePermissions('queue:write')
  async pauseQueue(@Param('queueName') queueName: string) {
    // Implementation
  }

  @Post('clear/:queueName')
  @RequirePermissions('queue:admin')
  async clearQueue(@Param('queueName') queueName: string) {
    // Implementation
  }
}
```

## Data Security

### Message Data Encryption

#### Sensitive Data Handling

```typescript
// Queue Service
@Injectable()
export class QueueService {
  private readonly encryptionKey: string;

  constructor(private configService: ConfigService) {
    this.encryptionKey = this.configService.get<string>('ENCRYPTION_KEY');
  }

  async sendCampaignMessage(message: CampaignMessage): Promise<void> {
    // Encrypt sensitive data before queuing
    const encryptedMessage = this.encryptSensitiveData(message);
    
    await this.campaignQueue.add('process-campaign-message', encryptedMessage, {
      // Job options
    });
  }

  private encryptSensitiveData(message: CampaignMessage): CampaignMessage {
    return {
      ...message,
      phoneNumber: this.encrypt(message.phoneNumber),
      variableMapping: this.encrypt(JSON.stringify(message.variableMapping)),
    };
  }

  private encrypt(data: string): string {
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  private decrypt(encryptedData: string): string {
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
```

#### PII Data Masking

```typescript
// Queue Processor
@Processor('campaign-messages')
export class CampaignMessageProcessor {
  private readonly logger = new Logger(CampaignMessageProcessor.name);

  @Process('process-campaign-message')
  async processCampaignMessage(job: Job<CampaignMessage>) {
    const message = job.data;
    
    // Log with masked phone number
    this.logger.log(`Processing message for campaign ${message.campaignId} to ${this.maskPhoneNumber(message.phoneNumber)}`);
    
    try {
      // Process message
    } catch (error) {
      // Log error without sensitive data
      this.logger.error(`Failed to process message for campaign ${message.campaignId}: ${error.message}`);
    }
  }

  private maskPhoneNumber(phoneNumber: string): string {
    if (phoneNumber.length <= 4) return phoneNumber;
    const start = phoneNumber.substring(0, 2);
    const end = phoneNumber.substring(phoneNumber.length - 2);
    const middle = '*'.repeat(phoneNumber.length - 4);
    return `${start}${middle}${end}`;
  }
}
```

### Redis Security

#### Redis Configuration

```yaml
# docker-compose.redis.yml
redis:
  image: redis:7-alpine
  command: >
    redis-server
    --requirepass ${REDIS_PASSWORD}
    --bind 127.0.0.1
    --port 6379
    --tcp-keepalive 60
    --timeout 300
    --maxmemory 256mb
    --maxmemory-policy allkeys-lru
  environment:
    - REDIS_PASSWORD=${REDIS_PASSWORD}
  volumes:
    - redis-data:/data
  networks:
    - internal
```

#### Redis Connection Security

```typescript
// Queue Module
BullModule.forRootAsync({
  useFactory: async (configService: ConfigService) => ({
    redis: {
      host: configService.get('REDIS_HOST'),
      port: configService.get('REDIS_PORT'),
      password: configService.get('REDIS_PASSWORD'),
      db: configService.get('REDIS_DB'),
      tls: configService.get('REDIS_TLS') === 'true' ? {} : undefined,
      connectTimeout: 10000,
      lazyConnect: true,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    },
  }),
})
```

## Network Security

### Firewall Configuration

```bash
# UFW Firewall Rules
sudo ufw allow 22/tcp          # SSH
sudo ufw allow 80/tcp          # HTTP
sudo ufw allow 443/tcp         # HTTPS
sudo ufw allow 3000/tcp        # API (if needed)
sudo ufw deny 6379/tcp         # Redis (internal only)
sudo ufw enable
```

### Network Isolation

```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    networks:
      - internal
    ports:
      - "127.0.0.1:6379:6379"  # Bind to localhost only

  app:
    networks:
      - internal
      - external
    depends_on:
      - redis

networks:
  internal:
    driver: bridge
    internal: true
  external:
    driver: bridge
```

## Input Validation

### Request Validation

```typescript
// Queue Controller
@Controller('queue')
export class QueueController {
  @Post('pause/:queueName')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async pauseQueue(
    @Param('queueName', new ParseEnumPipe(['campaign-messages', 'scheduled-campaign-messages', 'campaign-retry-messages']))
    queueName: string
  ) {
    // Implementation
  }
}
```

### Data Sanitization

```typescript
// Queue Service
@Injectable()
export class QueueService {
  async sendCampaignMessage(message: CampaignMessage): Promise<void> {
    // Validate and sanitize input
    const sanitizedMessage = this.sanitizeMessage(message);
    
    // Validate phone number format
    if (!this.isValidPhoneNumber(sanitizedMessage.phoneNumber)) {
      throw new BadRequestException('Invalid phone number format');
    }
    
    // Validate template ID
    if (!this.isValidTemplateId(sanitizedMessage.templateId)) {
      throw new BadRequestException('Invalid template ID');
    }
    
    await this.campaignQueue.add('process-campaign-message', sanitizedMessage);
  }

  private sanitizeMessage(message: CampaignMessage): CampaignMessage {
    return {
      ...message,
      phoneNumber: message.phoneNumber.replace(/[^0-9+]/g, ''),
      countryCode: message.countryCode.replace(/[^0-9+]/g, ''),
      templateId: message.templateId.trim(),
      campaignId: message.campaignId.trim(),
      contactId: message.contactId.trim(),
    };
  }

  private isValidPhoneNumber(phoneNumber: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  private isValidTemplateId(templateId: string): boolean {
    const templateIdRegex = /^[a-zA-Z0-9_-]{1,50}$/;
    return templateIdRegex.test(templateId);
  }
}
```

## Rate Limiting

### API Rate Limiting

```typescript
// Rate Limiting Guard
@Injectable()
export class RateLimitGuard implements CanActivate {
  private readonly rateLimitStore = new Map<string, { count: number; resetTime: number }>();

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const clientId = this.getClientId(request);
    const now = Date.now();
    
    const clientData = this.rateLimitStore.get(clientId);
    
    if (!clientData || now > clientData.resetTime) {
      this.rateLimitStore.set(clientId, { count: 1, resetTime: now + 60000 }); // 1 minute window
      return true;
    }
    
    if (clientData.count >= 100) { // 100 requests per minute
      throw new TooManyRequestsException('Rate limit exceeded');
    }
    
    clientData.count++;
    return true;
  }

  private getClientId(request: Request): string {
    return request.ip || request.connection.remoteAddress || 'unknown';
  }
}
```

### Queue Rate Limiting

```typescript
// Queue Service with Rate Limiting
@Injectable()
export class QueueService {
  private readonly rateLimiter = new Map<string, { count: number; resetTime: number }>();

  async sendCampaignMessage(message: CampaignMessage): Promise<void> {
    const rateLimitKey = `user:${message.userId}`;
    
    if (!this.checkRateLimit(rateLimitKey, 1000, 3600000)) { // 1000 messages per hour
      throw new TooManyRequestsException('Message rate limit exceeded');
    }
    
    await this.campaignQueue.add('process-campaign-message', message);
  }

  private checkRateLimit(key: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const clientData = this.rateLimiter.get(key);
    
    if (!clientData || now > clientData.resetTime) {
      this.rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }
    
    if (clientData.count >= maxRequests) {
      return false;
    }
    
    clientData.count++;
    return true;
  }
}
```

## Audit Logging

### Security Event Logging

```typescript
// Security Logger
@Injectable()
export class SecurityLogger {
  private readonly logger = new Logger(SecurityLogger.name);

  logAuthenticationAttempt(userId: string, success: boolean, ip: string) {
    this.logger.log(`Authentication attempt: userId=${userId}, success=${success}, ip=${ip}`);
  }

  logPermissionDenied(userId: string, resource: string, action: string, ip: string) {
    this.logger.warn(`Permission denied: userId=${userId}, resource=${resource}, action=${action}, ip=${ip}`);
  }

  logQueueOperation(userId: string, operation: string, queueName: string, success: boolean) {
    this.logger.log(`Queue operation: userId=${userId}, operation=${operation}, queue=${queueName}, success=${success}`);
  }

  logSuspiciousActivity(userId: string, activity: string, details: any) {
    this.logger.error(`Suspicious activity: userId=${userId}, activity=${activity}, details=${JSON.stringify(details)}`);
  }
}
```

### Audit Trail

```typescript
// Queue Controller with Audit Logging
@Controller('queue')
@UseGuards(AuthGuard, PermissionsGuard, AuditGuard)
export class QueueController {
  constructor(
    private queueService: QueueService,
    private securityLogger: SecurityLogger
  ) {}

  @Post('pause/:queueName')
  @RequirePermissions('queue:write')
  async pauseQueue(
    @Param('queueName') queueName: string,
    @Request() req: any
  ) {
    try {
      await this.queueService.pauseQueue(queueName);
      this.securityLogger.logQueueOperation(req.user.sub, 'pause', queueName, true);
      return { message: `Queue ${queueName} paused successfully` };
    } catch (error) {
      this.securityLogger.logQueueOperation(req.user.sub, 'pause', queueName, false);
      throw error;
    }
  }
}
```

## Security Headers

### HTTP Security Headers

```typescript
// Security Headers Middleware
@Injectable()
export class SecurityHeadersMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.setHeader('Content-Security-Policy', "default-src 'self'");
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    next();
  }
}
```

### CORS Configuration

```typescript
// CORS Configuration
const corsOptions = {
  origin: (origin: string, callback: Function) => {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
};
```

## Vulnerability Management

### Dependency Security

```bash
# Check for vulnerabilities
npm audit

# Fix vulnerabilities
npm audit fix

# Update dependencies
npm update

# Use security-focused package manager
npm install --audit-level moderate
```

### Security Scanning

```bash
# OWASP ZAP Security Scan
docker run -t owasp/zap2docker-stable zap-baseline.py -t http://localhost:3000

# Snyk Security Scan
npx snyk test

# Retire.js for JavaScript vulnerabilities
npx retire
```

## Incident Response

### Security Incident Response Plan

1. **Detection**
   - Monitor security logs
   - Set up alerts for suspicious activities
   - Regular security audits

2. **Response**
   - Immediate containment
   - Assess impact
   - Notify stakeholders
   - Document incident

3. **Recovery**
   - Restore services
   - Patch vulnerabilities
   - Update security measures

4. **Post-Incident**
   - Conduct post-mortem
   - Update security policies
   - Improve monitoring

### Emergency Procedures

```typescript
// Emergency Queue Shutdown
@Injectable()
export class EmergencyService {
  async emergencyShutdown() {
    // Pause all queues
    await this.queueService.pauseQueue('campaign-messages');
    await this.queueService.pauseQueue('scheduled-campaign-messages');
    await this.queueService.pauseQueue('campaign-retry-messages');
    
    // Clear sensitive data
    await this.queueService.clearQueue('campaign-messages');
    
    // Log emergency action
    this.logger.error('Emergency shutdown executed');
  }
}
```

## Compliance

### GDPR Compliance

- **Data Minimization**: Only collect necessary data
- **Data Retention**: Automatic cleanup of old jobs
- **Right to Erasure**: Remove user data on request
- **Data Portability**: Export user data

### SOC 2 Compliance

- **Access Controls**: Role-based access
- **Audit Logging**: Comprehensive audit trails
- **Data Encryption**: Encrypt sensitive data
- **Incident Response**: Documented procedures

## Security Best Practices

### Development

1. **Secure Coding**
   - Input validation
   - Output encoding
   - Error handling
   - Logging

2. **Code Review**
   - Security-focused reviews
   - Automated security scanning
   - Dependency updates

3. **Testing**
   - Security testing
   - Penetration testing
   - Vulnerability assessment

### Operations

1. **Monitoring**
   - Security event monitoring
   - Performance monitoring
   - Error tracking

2. **Maintenance**
   - Regular updates
   - Security patches
   - Configuration reviews

3. **Backup**
   - Regular backups
   - Disaster recovery
   - Business continuity

## Security Checklist

### Pre-Deployment

- [ ] All dependencies updated
- [ ] Security vulnerabilities fixed
- [ ] Input validation implemented
- [ ] Authentication configured
- [ ] Authorization implemented
- [ ] Rate limiting enabled
- [ ] Security headers configured
- [ ] CORS properly configured
- [ ] Audit logging enabled
- [ ] Error handling implemented

### Post-Deployment

- [ ] Security monitoring active
- [ ] Logs being collected
- [ ] Alerts configured
- [ ] Backup procedures tested
- [ ] Incident response plan ready
- [ ] Security documentation updated
- [ ] Team training completed
- [ ] Regular security audits scheduled
