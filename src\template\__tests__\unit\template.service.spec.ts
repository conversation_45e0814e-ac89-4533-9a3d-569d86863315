import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { BadRequestException, NotFoundException, ConflictException } from '@nestjs/common';
import { TemplateService } from '../../template.service';
import { TemplateValidationUtil } from '../../utils/template-validation.util';
import { TemplateResponseUtil } from '../../utils/template-response.util';
import { TEMPLATE_CONSTANTS } from '../../utils/template-constants.util';
import { templateFixtures } from '../fixtures/template.fixtures';

describe('TemplateService', () => {
  let service: TemplateService;
  let mockTemplateModel: any;
  let mockValidationUtil: any;
  let mockResponseUtil: any;

  beforeEach(async () => {
    const mockModel = {
      create: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      findByIdAndUpdate: jest.fn(),
      findByIdAndDelete: jest.fn(),
      countDocuments: jest.fn(),
      aggregate: jest.fn(),
    };

    const mockValidation = {
      validateUserContext: jest.fn(),
      validateTemplateCreationData: jest.fn(),
      validateTemplateExists: jest.fn(),
      validateWorkspaceAccess: jest.fn(),
    };

    const mockResponse = {
      success: jest.fn(),
      error: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TemplateService,
        {
          provide: getModelToken('Template'),
          useValue: mockModel,
        },
        {
          provide: TemplateValidationUtil,
          useValue: mockValidation,
        },
        {
          provide: TemplateResponseUtil,
          useValue: mockResponse,
        },
      ],
    }).compile();

    service = module.get<TemplateService>(TemplateService);
    mockTemplateModel = module.get(getModelToken('Template'));
    mockValidationUtil = module.get(TemplateValidationUtil);
    mockResponseUtil = module.get(TemplateResponseUtil);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTemplate', () => {
    it('should create template successfully', async () => {
      const createTemplateDto = templateFixtures.validTemplate;
      const req = templateFixtures.mockRequest;
      const mockTemplate = templateFixtures.mockTemplate;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockValidationUtil.validateTemplateCreationData.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(null);
      mockTemplateModel.create.mockResolvedValue(mockTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.createTemplate(createTemplateDto, req);

      expect(mockValidationUtil.validateUserContext).toHaveBeenCalledWith(req);
      expect(mockValidationUtil.validateTemplateCreationData).toHaveBeenCalledWith(createTemplateDto);
      expect(mockTemplateModel.findOne).toHaveBeenCalled();
      expect(mockTemplateModel.create).toHaveBeenCalled();
      expect(mockResponseUtil.success).toHaveBeenCalled();
      expect(result.status).toBe('success');
    });

    it('should handle duplicate template names', async () => {
      const createTemplateDto = templateFixtures.validTemplate;
      const req = templateFixtures.mockRequest;
      const existingTemplate = templateFixtures.mockTemplate;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockValidationUtil.validateTemplateCreationData.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(existingTemplate);
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.createTemplate(createTemplateDto, req);

      expect(mockTemplateModel.findOne).toHaveBeenCalled();
      expect(mockTemplateModel.create).not.toHaveBeenCalled();
      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });

    it('should handle validation errors', async () => {
      const createTemplateDto = templateFixtures.invalidTemplate;
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockValidationUtil.validateTemplateCreationData.mockRejectedValue(
        new BadRequestException('Validation failed')
      );

      await expect(service.createTemplate(createTemplateDto, req)).rejects.toThrow(
        BadRequestException
      );
    });
  });

  describe('createDraftTemplate', () => {
    it('should create draft template successfully', async () => {
      const createTemplateDto = templateFixtures.validTemplate;
      const req = templateFixtures.mockRequest;
      const mockTemplate = { ...templateFixtures.mockTemplate, meta_template_status: 'DRAFT' };

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockValidationUtil.validateTemplateCreationData.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(null);
      mockTemplateModel.create.mockResolvedValue(mockTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.createDraftTemplate(createTemplateDto, req);

      expect(mockTemplateModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          meta_template_status: 'DRAFT',
          meta_template_id: null,
        })
      );
      expect(result.status).toBe('success');
    });
  });

  describe('getUserTemplates', () => {
    it('should retrieve user templates successfully', async () => {
      const queryDto = templateFixtures.validQuery;
      const req = templateFixtures.mockRequest;
      const mockTemplates = [templateFixtures.mockTemplate];
      const mockPagination = { page: 1, limit: 10, total: 1, totalPages: 1 };

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.find.mockReturnValue({
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockTemplates),
      });
      mockTemplateModel.countDocuments.mockResolvedValue(1);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.getUserTemplates(queryDto, req);

      expect(mockTemplateModel.find).toHaveBeenCalled();
      expect(mockTemplateModel.countDocuments).toHaveBeenCalled();
      expect(result.status).toBe('success');
    });

    it('should handle empty results', async () => {
      const queryDto = templateFixtures.validQuery;
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.find.mockReturnValue({
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([]),
      });
      mockTemplateModel.countDocuments.mockResolvedValue(0);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.getUserTemplates(queryDto, req);

      expect(result.status).toBe('success');
    });
  });

  describe('getWorkspaceTemplates', () => {
    it('should retrieve workspace templates successfully', async () => {
      const workspaceId = 'workspace-123';
      const queryDto = templateFixtures.validQuery;
      const req = templateFixtures.mockRequest;
      const mockTemplates = [templateFixtures.mockTemplate];

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockValidationUtil.validateWorkspaceAccess.mockResolvedValue(undefined);
      mockTemplateModel.find.mockReturnValue({
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockTemplates),
      });
      mockTemplateModel.countDocuments.mockResolvedValue(1);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.getWorkspaceTemplates(workspaceId, queryDto, req);

      expect(mockValidationUtil.validateWorkspaceAccess).toHaveBeenCalledWith(workspaceId, req);
      expect(result.status).toBe('success');
    });

    it('should handle workspace access denied', async () => {
      const workspaceId = 'workspace-123';
      const queryDto = templateFixtures.validQuery;
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockValidationUtil.validateWorkspaceAccess.mockRejectedValue(
        new BadRequestException('Access denied')
      );

      await expect(service.getWorkspaceTemplates(workspaceId, queryDto, req)).rejects.toThrow(
        BadRequestException
      );
    });
  });

  describe('getTemplateById', () => {
    it('should retrieve template by ID successfully', async () => {
      const templateId = 'template-123';
      const req = templateFixtures.mockRequest;
      const mockTemplate = templateFixtures.mockTemplate;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(mockTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.getTemplateById(templateId, req);

      expect(mockTemplateModel.findOne).toHaveBeenCalledWith({
        _id: templateId,
        user_id: req.user.id,
      });
      expect(result.status).toBe('success');
    });

    it('should handle template not found', async () => {
      const templateId = 'template-123';
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(null);
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.getTemplateById(templateId, req);

      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });
  });

  describe('updateTemplate', () => {
    it('should update template successfully', async () => {
      const templateId = 'template-123';
      const updateTemplateDto = templateFixtures.validUpdate;
      const req = templateFixtures.mockRequest;
      const mockTemplate = templateFixtures.mockTemplate;
      const updatedTemplate = { ...mockTemplate, ...updateTemplateDto };

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(mockTemplate);
      mockTemplateModel.findByIdAndUpdate.mockResolvedValue(updatedTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.updateTemplate(templateId, updateTemplateDto, req);

      expect(mockTemplateModel.findByIdAndUpdate).toHaveBeenCalled();
      expect(result.status).toBe('success');
    });

    it('should handle template not found for update', async () => {
      const templateId = 'template-123';
      const updateTemplateDto = templateFixtures.validUpdate;
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(null);
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.updateTemplate(templateId, updateTemplateDto, req);

      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });
  });

  describe('deleteTemplate', () => {
    it('should delete template successfully', async () => {
      const wabaId = 'waba-123';
      const templateId = 'template-123';
      const req = templateFixtures.mockRequest;
      const mockTemplate = templateFixtures.mockTemplate;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(mockTemplate);
      mockTemplateModel.findByIdAndDelete.mockResolvedValue(mockTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.deleteTemplate(wabaId, templateId, req);

      expect(mockTemplateModel.findByIdAndDelete).toHaveBeenCalled();
      expect(result.status).toBe('success');
    });

    it('should handle template not found for deletion', async () => {
      const wabaId = 'waba-123';
      const templateId = 'template-123';
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(null);
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.deleteTemplate(wabaId, templateId, req);

      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });
  });

  describe('syncAllWithMeta', () => {
    it('should sync all templates with Meta successfully', async () => {
      const wabaId = 'waba-123';
      const req = templateFixtures.mockRequest;
      const mockMetaTemplates = [templateFixtures.mockMetaTemplate];

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      // Mock Meta API call
      jest.spyOn(service as any, 'fetchMetaTemplates').mockResolvedValue(mockMetaTemplates);
      mockTemplateModel.findOne.mockResolvedValue(null);
      mockTemplateModel.create.mockResolvedValue(templateFixtures.mockTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.syncAllWithMeta(wabaId, req);

      expect(result.status).toBe('success');
    });

    it('should handle Meta API errors', async () => {
      const wabaId = 'waba-123';
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      jest.spyOn(service as any, 'fetchMetaTemplates').mockRejectedValue(
        new Error('Meta API error')
      );
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.syncAllWithMeta(wabaId, req);

      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });
  });

  describe('syncTemplateWithMeta', () => {
    it('should sync single template with Meta successfully', async () => {
      const templateId = 'template-123';
      const req = templateFixtures.mockRequest;
      const mockTemplate = templateFixtures.mockTemplate;
      const mockMetaResponse = templateFixtures.mockMetaResponse;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(mockTemplate);
      jest.spyOn(service as any, 'submitTemplateToMeta').mockResolvedValue(mockMetaResponse);
      mockTemplateModel.findByIdAndUpdate.mockResolvedValue(mockTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.syncTemplateWithMeta(templateId, req);

      expect(result.status).toBe('success');
    });

    it('should handle template not found for sync', async () => {
      const templateId = 'template-123';
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      mockTemplateModel.findOne.mockResolvedValue(null);
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.syncTemplateWithMeta(templateId, req);

      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });
  });

  describe('getMetaTemplates', () => {
    it('should retrieve Meta templates successfully', async () => {
      const req = templateFixtures.mockRequest;
      const mockMetaTemplates = [templateFixtures.mockMetaTemplate];

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      jest.spyOn(service as any, 'fetchMetaTemplates').mockResolvedValue(mockMetaTemplates);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.getMetaTemplates(req);

      expect(result.status).toBe('success');
    });

    it('should handle Meta API errors', async () => {
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      jest.spyOn(service as any, 'fetchMetaTemplates').mockRejectedValue(
        new Error('Meta API error')
      );
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.getMetaTemplates(req);

      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });
  });

  describe('generateAiTemplate', () => {
    it('should generate AI template successfully', async () => {
      const createAiTemplateDto = templateFixtures.validAiTemplate;
      const req = templateFixtures.mockRequest;
      const mockAiResponse = templateFixtures.mockAiResponse;
      const mockTemplate = templateFixtures.mockTemplate;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      jest.spyOn(service as any, 'callAiService').mockResolvedValue(mockAiResponse);
      mockTemplateModel.create.mockResolvedValue(mockTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.generateAiTemplate(createAiTemplateDto, req);

      expect(result.status).toBe('success');
    });

    it('should handle AI service errors', async () => {
      const createAiTemplateDto = templateFixtures.validAiTemplate;
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      jest.spyOn(service as any, 'callAiService').mockRejectedValue(
        new Error('AI service error')
      );
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.generateAiTemplate(createAiTemplateDto, req);

      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });
  });

  describe('generateVoiceTemplate', () => {
    it('should generate template from voice successfully', async () => {
      const createVoiceTemplateDto = templateFixtures.validVoiceTemplate;
      const req = templateFixtures.mockRequest;
      const mockTranscription = 'Transcribed text';
      const mockAiResponse = templateFixtures.mockAiResponse;
      const mockTemplate = templateFixtures.mockTemplate;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      jest.spyOn(service as any, 'transcribeAudio').mockResolvedValue(mockTranscription);
      jest.spyOn(service as any, 'callAiService').mockResolvedValue(mockAiResponse);
      mockTemplateModel.create.mockResolvedValue(mockTemplate);
      mockResponseUtil.success.mockReturnValue({ status: 'success' });

      const result = await service.generateVoiceTemplate(createVoiceTemplateDto, req);

      expect(result.status).toBe('success');
    });

    it('should handle audio transcription errors', async () => {
      const createVoiceTemplateDto = templateFixtures.validVoiceTemplate;
      const req = templateFixtures.mockRequest;

      mockValidationUtil.validateUserContext.mockResolvedValue(undefined);
      jest.spyOn(service as any, 'transcribeAudio').mockRejectedValue(
        new Error('Transcription error')
      );
      mockResponseUtil.error.mockReturnValue({ status: 'error' });

      const result = await service.generateVoiceTemplate(createVoiceTemplateDto, req);

      expect(mockResponseUtil.error).toHaveBeenCalled();
      expect(result.status).toBe('error');
    });
  });
});
