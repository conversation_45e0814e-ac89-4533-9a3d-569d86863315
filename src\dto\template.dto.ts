import { Is<PERSON><PERSON>, IsNot<PERSON>mpty, IsO<PERSON>al, IsArray, IsObject, IsIn, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUrl, ValidateNested, IsUUID, IsBoolean, IsEnum, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { PartialType } from '@nestjs/mapped-types';

export class ButtonDto {
  @IsString({ message: 'Button ID must be a string' })
  @IsNotEmpty({ message: 'Button ID is required' })
  id: string;

  @IsString({ message: 'Button title must be a string' })
  @IsNotEmpty({ message: 'Button title is required' })
  @MaxLength(20, { message: 'Button title must not exceed 20 characters' })
  title: string;

  @IsOptional()
  @IsString({ message: 'Button text must be a string' })
  text?: string;

  @IsOptional()
  @IsIn(['QUICK_REPLY', 'URL', 'PHONE_NUMBER', 'COPY_CODE', 'OTP', 'CATALOG', 'MPM'], { 
    message: 'Button type must be one of: QUICK_REPLY, URL, PHONE_NUMBER, COPY_CODE, OTP, CATALOG, MPM' 
  })
  type?: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER' | 'COPY_CODE' | 'OTP' | 'CATALOG' | 'MPM';

  @IsOptional()
  @IsString({ message: 'Button URL must be a string' })
  url?: string;

  @IsOptional()
  @IsString({ message: 'Button phone number must be a string' })
  phone_number?: string;

  @IsOptional()
  @IsIn(['COPY_CODE', 'ONE_TAP'], { 
    message: 'OTP type must be one of: COPY_CODE, ONE_TAP' 
  })
  otp_type?: 'COPY_CODE' | 'ONE_TAP';

  @IsOptional()
  @IsString({ message: 'Autofill text must be a string' })
  autofill_text?: string;

  @IsOptional()
  @IsString({ message: 'Package name must be a string' })
  package_name?: string;

  @IsOptional()
  @IsString({ message: 'Signature hash must be a string' })
  signature_hash?: string;


}

export class RowDto {
  @IsString({ message: 'Row ID must be a string' })
  @IsNotEmpty({ message: 'Row ID is required' })
  id: string;

  @IsString({ message: 'Row title must be a string' })
  @IsNotEmpty({ message: 'Row title is required' })
  @MaxLength(24, { message: 'Row title must not exceed 24 characters' })
  title: string;

  @IsOptional()
  @IsString({ message: 'Row description must be a string' })
  @MaxLength(72, { message: 'Row description must not exceed 72 characters' })
  description?: string;
}

export class SectionDto {
  @IsString({ message: 'Section title must be a string' })
  @IsNotEmpty({ message: 'Section title is required' })
  title: string;

  @IsArray({ message: 'Section rows must be an array' })
  @IsNotEmpty({ message: 'Section rows are required' })
  @ValidateNested({ each: true })
  @Type(() => RowDto)
  rows: RowDto[];
}

export class CreateTemplateDto {
  @IsString({ message: 'Template name must be a string' })
  @IsNotEmpty({ message: 'Template name is required' })
  @MinLength(3, { message: 'Template name must be at least 3 characters long' })
  @MaxLength(50, { message: 'Template name must not exceed 50 characters' })
  name: string;

  @IsString({ message: 'Template description must be a string' })
  @IsOptional()
  @MaxLength(200, { message: 'Template description must not exceed 200 characters' })
  description?: string;

  @IsIn(['text', 'interactive', 'image', 'video', 'document', 'location'], { 
    message: 'Template type must be one of: text, interactive, image, video, document, location' 
  })
  @IsNotEmpty({ message: 'Template type is required' })
  type?: 'text' | 'interactive' | 'image' | 'video' | 'document' | 'location';

  @IsString({ message: 'Template content must be a string' })
  @IsNotEmpty({ message: 'Template content is required' })
  @MinLength(1, { message: 'Template content cannot be empty' })
  @MaxLength(1000, { message: 'Template content must not exceed 1000 characters' })
  content: string;

  @IsOptional()
  @IsArray({ message: 'Buttons must be an array' })
  @ValidateNested({ each: true })
  @Type(() => ButtonDto)
  buttons?: ButtonDto[];

  @IsOptional()
  @IsArray({ message: 'Sections must be an array' })
  @ValidateNested({ each: true })
  @Type(() => SectionDto)
  sections?: SectionDto[];

  @IsOptional()
  @IsUrl({}, { message: 'Image URL must be a valid URL' })
  imageUrl?: string;

  @IsOptional()
  @IsString({ message: 'Image caption must be a string' })
  @MaxLength(3000, { message: 'Image caption must not exceed 3000 characters' })
  imageCaption?: string;

  @IsOptional()
  @IsString({ message: 'Header text must be a string' })
  @MaxLength(60, { message: 'Header text must not exceed 60 characters' })
  headerText?: string;

  @IsOptional()
  @IsString({ message: 'Footer text must be a string' })
  @MaxLength(60, { message: 'Footer text must not exceed 60 characters' })
  footer?: string;


  @IsIn(['MARKETING', 'UTILITY', 'AUTHENTICATION'], { 
    message: 'Template category must be one of: MARKETING, UTILITY, AUTHENTICATION' 
  })
  @IsNotEmpty({ message: 'Template category is required' })
  category?: 'MARKETING' | 'UTILITY' | 'AUTHENTICATION';

  @IsOptional()
  @IsString({ message: 'Language must be a string' })
  language?: string;

  @IsOptional()
  @IsObject({ message: 'Variables must be an object' })
  variables?: Record<string, any>;

  @IsOptional()
  @IsNumber({}, { message: 'Code expiration minutes must be a number' })
  codeExpirationMinutes?: number;

  @IsOptional()
  @IsBoolean({ message: 'Add security recommendation must be a boolean' })
  addSecurityRecommendation?: boolean;

  @IsString({ message: 'WABA ID must be a string' })
  waba_id: string;
}

export class UpdateTemplateDto extends PartialType(CreateTemplateDto) {}

export class SendTemplateMessageDto {
  @IsString({ message: 'Recipient must be a string' })
  @IsNotEmpty({ message: 'Recipient is required' })
  to: string;

  @IsString({ message: 'Phone number ID must be a string' })
  @IsNotEmpty({ message: 'Phone number ID is required' })
  phone_number_id: string;

  @IsObject({ message: 'Variables must be an object' })
  @IsOptional()
  variables?: Record<string, any>;

  @IsString({ message: 'Language must be a string' })
  @IsOptional()
  language?: string;
}

export class TemplateQueryDto {
  @IsOptional()
  @IsString({ message: 'Search term must be a string' })
  search?: string;

  @IsOptional()
  @IsString({ message: 'Category must be a string' })
  category?: string;

  @IsOptional()
  @IsString({ message: 'Type must be a string' })
  type?: string;

  @IsOptional()
  @IsString({ message: 'Language must be a string' })
  language?: string;

  @IsOptional()
  @IsString({ message: 'Status must be a string' })
  status?: string;

  @IsOptional()
  @IsString({ message: 'Page must be a string' })
  page?: string;

  @IsOptional()
  @IsString({ message: 'Limit must be a string' })
  limit?: string;

  @IsOptional()
  @IsString({ message: 'WABA ID must be a string' })
  waba_id?: string;
} 

export class CreateAiTemplateDto {
  @IsString({ message: 'User prompt must be a string' })
  @IsNotEmpty({ message: 'User prompt is required' })
  @MaxLength(500, { message: 'User prompt must not exceed 500 characters' })
  prompt: string;

  @IsString()
  @IsNotEmpty({ message: 'WABA ID is required' })
  waba_id: string;
}

export class CreateVoiceTemplateDto {
  @IsString({ message: 'Audio file must be provided as base64 string' })
  @IsNotEmpty({ message: 'Audio file is required' })
  audioFile: string;

  @IsOptional()
  @IsString({ message: 'Audio format must be a string' })
  audioFormat?: string; // e.g., 'mp3', 'wav', 'm4a'
} 