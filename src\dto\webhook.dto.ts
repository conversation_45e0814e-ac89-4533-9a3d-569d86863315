import { IsString, IsOptional, IsObject, IsArray } from 'class-validator';

export class WebhookVerificationDto {
  @IsString()
  'hub.mode': string;

  @IsString()
  'hub.verify_token': string;

  @IsString()
  'hub.challenge': string;
}

export class WebhookMessageDto {
  @IsString()
  from: string;

  @IsString()
  timestamp: string;

  @IsString()
  type: string;

  @IsOptional()
  @IsObject()
  text?: {
    body: string;
  };

  @IsOptional()
  @IsObject()
  image?: {
    id: string;
    mime_type: string;
    sha256: string;
    filename?: string;
  };

  @IsOptional()
  @IsObject()
  button?: {
    text: string;
    payload: string;
  };

  @IsOptional()
  @IsObject()
  interactive?: {
    type: string;
    button_reply?: {
      id: string;
      title: string;
    };
    list_reply?: {
      id: string;
      title: string;
      description?: string;
    };
  };
}

export class WebhookEntryDto {
  @IsString()
  id: string;

  @IsArray()
  changes: Array<{
    value: {
      messaging_product: string;
      metadata: {
        display_phone_number: string;
        phone_number_id: string;
      };
      contacts?: Array<{
        profile: {
          name: string;
        };
        wa_id: string;
      }>;
      messages?: WebhookMessageDto[];
      statuses?: Array<{
        id: string;
        status: string;
        timestamp: string;
        recipient_id: string;
      }>;
    };
    field: string;
  }>;
}

export class WebhookBodyDto {
  @IsString()
  object: string;

  @IsArray()
  entry: WebhookEntryDto[];
} 