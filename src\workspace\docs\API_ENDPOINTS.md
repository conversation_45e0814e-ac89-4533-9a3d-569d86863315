# Workspace Module API Endpoints

## Base URL
All endpoints are prefixed with `/workspace`

## Authentication
All endpoints require authentication via `AuthGuard`. Include the Bearer token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### 1. Create Workspace

**POST** `/workspace`

Creates a new workspace for the authenticated user.

#### Request Body
```typescript
{
  name: string;                    // Required, 2-100 characters
  description?: string;            // Optional, max 500 characters
  industry?: string;              // Optional, max 50 characters
  website?: string;               // Optional, max 200 characters
  timezone?: string;              // Optional, max 50 characters
  language?: string;              // Optional, max 10 characters
}
```

#### Response
```typescript
{
  status: 'success',
  code: 201,
  message: 'Workspace created successfully with user as admin',
  data: {
    workspace: {
      id: number,
      name: string,
      description: string,
      industry: string,
      website: string,
      timezone: string,
      language: string,
      created_by: string,
      status: 'active',
      trial_expiry: string,
      waba_access: boolean,
      created_at: string,
      updated_at: string
    },
    created_at: string,
    user_id: string,
    user_profile_updated: boolean,
    user_profile: object,
    workspace_member: object,
    admin_role: object,
    automate_member: object
  },
  timestamp: string
}
```

#### Error Responses
- **400 Bad Request** - User already has a workspace
- **400 Bad Request** - Invalid input data
- **401 Unauthorized** - Invalid or missing token
- **500 Internal Server Error** - Server error during creation

---

### 2. Add Member to Workspace

**POST** `/workspace/add-member`

Adds a new member to the workspace.

#### Request Body
```typescript
{
  email: string;                  // Required, valid email
  password: string;               // Required, min 6 chars, must contain uppercase, lowercase, number
  first_name: string;             // Required, 2-50 characters, letters and spaces only
  last_name: string;              // Required, 2-50 characters, letters and spaces only
  phone: string;                  // Required, 10-15 digits
  country_code: string;           // Required, format +XXX
  country: string;                // Required, max 50 characters
  role: 'Admin' | 'Manager' | 'User';  // Required
  reports_to?: string;            // Optional
  waba_access: boolean;           // Required
  role_id?: string;               // Required if waba_access is true
}
```

#### Response
```typescript
{
  status: 'success',
  code: 201,
  message: 'User registered and added to workspace successfully',
  data: {
    user: {
      id: string,
      email: string,
      // ... other user fields
    },
    profile: object,
    member: {
      id: number,
      workspace_id: number,
      user_id: string,
      role: string,
      status: 'active',
      waba_access: boolean
    },
    automate_member?: object  // Present if waba_access is true
  },
  timestamp: string
}
```

#### Error Responses
- **400 Bad Request** - User not associated with workspace
- **400 Bad Request** - User with email already exists
- **400 Bad Request** - Member already exists in workspace
- **400 Bad Request** - Role ID required when waba_access is true
- **401 Unauthorized** - Invalid or missing token
- **500 Internal Server Error** - Server error during member addition

---

### 3. Get Workspace Members

**GET** `/workspace/members`

Retrieves all members of the workspace with pagination and filtering.

#### Query Parameters
```typescript
{
  search?: string;        // Search across member fields
  page?: string;          // Page number (default: 1)
  limit?: string;         // Items per page (default: 10, max: 100)
  sortBy?: string;        // Sort field: 'created_at', 'updated_at', 'first_name', 'last_name', 'email', 'role'
  sortOrder?: string;     // Sort order: 'asc' or 'desc' (default: 'desc')
}
```

#### Response
```typescript
{
  status: 'success',
  code: 200,
  message: 'Workspace members retrieved successfully',
  data: {
    members: [
      {
        id: number,
        workspace_id: number,
        user_id: string,
        role: string,
        status: string,
        waba_access: boolean,
        created_at: string,
        updated_at: string,
        // ... user profile fields
      }
    ],
    pagination: {
      currentPage: number,
      totalPages: number,
      totalItems: number,
      itemsPerPage: number,
      hasNextPage: boolean,
      hasPrevPage: boolean,
      nextPage: number | null,
      prevPage: number | null
    },
    filters: {
      search: string,
      sortBy: string,
      sortOrder: string
    }
  },
  timestamp: string
}
```

#### Error Responses
- **400 Bad Request** - User not associated with workspace
- **401 Unauthorized** - Invalid or missing token
- **500 Internal Server Error** - Server error during fetch

---

## Common Error Response Format

```typescript
{
  status: 'error',
  code: number,
  message: string,
  error?: any,
  timestamp: string
}
```

## HTTP Status Codes

- **200 OK** - Successful GET requests
- **201 Created** - Successful POST requests
- **400 Bad Request** - Invalid input or business logic errors
- **401 Unauthorized** - Authentication required or failed
- **404 Not Found** - Resource not found
- **409 Conflict** - Resource already exists
- **500 Internal Server Error** - Server errors

## Rate Limiting

All endpoints are subject to rate limiting. Check response headers for rate limit information:
- `X-RateLimit-Limit` - Requests per time window
- `X-RateLimit-Remaining` - Remaining requests in current window
- `X-RateLimit-Reset` - Time when the rate limit resets

## Examples

### Create Workspace
```bash
curl -X POST http://localhost:3000/workspace \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Company",
    "description": "Our main workspace",
    "industry": "Technology",
    "website": "https://mycompany.com",
    "timezone": "UTC",
    "language": "en"
  }'
```

### Add Member
```bash
curl -X POST http://localhost:3000/workspace/add-member \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "1234567890",
    "country_code": "+1",
    "country": "USA",
    "role": "User",
    "waba_access": true,
    "role_id": "role-123"
  }'
```

### Get Members
```bash
curl -X GET "http://localhost:3000/workspace/members?page=1&limit=10&search=john" \
  -H "Authorization: Bearer <token>"
```


