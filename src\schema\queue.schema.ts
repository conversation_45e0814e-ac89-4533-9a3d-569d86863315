import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type QueueJobDocument = QueueJob & Document;

@Schema({ timestamps: true })
export class QueueJob {
  @Prop({ required: true })
  name: string; // 'send-message', 'update-delivery-status'

  @Prop({ required: true })
  status: string; // 'waiting', 'active', 'completed', 'failed', 'delayed'

  @Prop({ type: Object, required: true })
  data: Record<string, any>;

  @Prop({ default: 0 })
  attempts: number;

  @Prop({ default: 3 })
  maxAttempts: number;

  @Prop()
  error?: string;

  @Prop()
  result?: any;

  @Prop()
  startedAt?: Date;

  @Prop()
  completedAt?: Date;

  @Prop()
  failedAt?: Date;

  @Prop()
  delay?: number; // Delay in milliseconds

  @Prop()
  priority?: number; // Higher number = higher priority

  @Prop({ required: true })
  workspace_id: number;

  @Prop({ required: true })
  created_by: string;
}

export const QueueJobSchema = SchemaFactory.createForClass(QueueJob);

// Indexes for better performance
QueueJobSchema.index({ status: 1, priority: -1, createdAt: 1 });
QueueJobSchema.index({ workspace_id: 1, status: 1 });
QueueJobSchema.index({ name: 1, status: 1 });
