import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Types } from 'mongoose';
import { SEGMENTS_CONSTANTS } from './segments-constants.util';
import { SegmentRule } from '../../schema/segment.schema';

/**
 * Utility class for segments-related validations
 */
export class SegmentsValidationUtil {
  /**
   * Validates segment name
   */
  static validateSegmentName(name: string): void {
    if (!name || typeof name !== 'string') {
      throw new BadRequestException('Segment name is required and must be a string');
    }

    const trimmedName = name.trim();
    if (trimmedName.length < SEGMENTS_CONSTANTS.VALIDATION.NAME_MIN_LENGTH) {
      throw new BadRequestException('Segment name must be at least 1 character long');
    }

    if (trimmedName.length > SEGMENTS_CONSTANTS.VALIDATION.NAME_MAX_LENGTH) {
      throw new BadRequestException('Segment name must not exceed 100 characters');
    }

    // Check for valid characters (alphanumeric, spaces, hyphens, underscores)
    const nameRegex = /^[a-zA-Z0-9\s\-_]+$/;
    if (!nameRegex.test(trimmedName)) {
      throw new BadRequestException('Segment name can only contain letters, numbers, spaces, hyphens, and underscores');
    }
  }

  /**
   * Validates segment description
   */
  static validateSegmentDescription(description: string | undefined): string | undefined {
    if (!description) return undefined;

    if (typeof description !== 'string') {
      throw new BadRequestException('Segment description must be a string');
    }

    if (description.length > SEGMENTS_CONSTANTS.VALIDATION.DESCRIPTION_MAX_LENGTH) {
      throw new BadRequestException('Segment description must not exceed 500 characters');
    }

    return description.trim();
  }

  /**
   * Validates match type
   */
  static validateMatchType(match: string | undefined): 'all' | 'any' {
    if (!match) return SEGMENTS_CONSTANTS.DEFAULTS.MATCH_TYPE as 'all';

    if (match !== SEGMENTS_CONSTANTS.MATCH_TYPES.ALL && match !== SEGMENTS_CONSTANTS.MATCH_TYPES.ANY) {
      throw new BadRequestException(SEGMENTS_CONSTANTS.ERROR_MESSAGES.INVALID_MATCH_TYPE);
    }

    return match as 'all' | 'any';
  }

  /**
   * Validates segment operator
   */
  static validateSegmentOperator(operator: string): string {
    const validOperators = Object.values(SEGMENTS_CONSTANTS.OPERATORS);
    if (!validOperators.includes(operator as any)) {
      throw new BadRequestException(SEGMENTS_CONSTANTS.ERROR_MESSAGES.INVALID_SEGMENT_OPERATOR);
    }
    return operator;
  }

  /**
   * Validates segment field
   */
  static validateSegmentField(field: string): string {
    if (!field || typeof field !== 'string') {
      throw new BadRequestException('Segment field is required and must be a string');
    }

    const trimmedField = field.trim();
    if (trimmedField.length === 0) {
      throw new BadRequestException('Segment field cannot be empty');
    }

    // Check if field starts with 'custom.' for custom fields
    if (trimmedField.startsWith('custom.')) {
      return trimmedField;
    }

    // Validate against supported fields
    const supportedFields = Object.values(SEGMENTS_CONSTANTS.SUPPORTED_FIELDS);
    if (!supportedFields.includes(trimmedField as any)) {
      throw new BadRequestException(SEGMENTS_CONSTANTS.ERROR_MESSAGES.INVALID_SEGMENT_FIELD);
    }

    return trimmedField;
  }

  /**
   * Validates segment rule value
   */
  static validateSegmentRuleValue(value: any, operator: string, field: string): any {
    if (value === null || value === undefined) {
      // Some operators don't require values
      if (operator === SEGMENTS_CONSTANTS.OPERATORS.EXISTS) {
        return true; // Default to true for exists check
      }
      return value;
    }

    // Validate value length for string values
    if (typeof value === 'string' && value.length > SEGMENTS_CONSTANTS.VALIDATION.MAX_RULE_VALUE_LENGTH) {
      throw new BadRequestException('Rule value must not exceed 1000 characters');
    }

    // Validate array values for specific operators
    if (operator === SEGMENTS_CONSTANTS.OPERATORS.IN || 
        operator === SEGMENTS_CONSTANTS.OPERATORS.HAS_ANY_TAG || 
        operator === SEGMENTS_CONSTANTS.OPERATORS.HAS_ALL_TAGS) {
      if (!Array.isArray(value)) {
        throw new BadRequestException('Value must be an array for this operator');
      }
      if (value.length === 0) {
        throw new BadRequestException('Value array cannot be empty');
      }
      if (value.length > 50) {
        throw new BadRequestException('Value array cannot exceed 50 items');
      }
    }

    // Validate tag IDs for tag-related operators
    if (operator === SEGMENTS_CONSTANTS.OPERATORS.HAS_ANY_TAG || 
        operator === SEGMENTS_CONSTANTS.OPERATORS.HAS_ALL_TAGS) {
      if (Array.isArray(value)) {
        value.forEach((id, index) => {
          if (!Types.ObjectId.isValid(id)) {
            throw new BadRequestException(`Invalid tag ID at index ${index}`);
          }
        });
      }
    }

    return value;
  }

  /**
   * Validates segment rule
   */
  static validateSegmentRule(rule: any, index: number): SegmentRule {
    if (!rule || typeof rule !== 'object') {
      throw new BadRequestException(`Rule at index ${index} must be an object`);
    }

    const field = this.validateSegmentField(rule.field);
    const operator = this.validateSegmentOperator(rule.operator);
    const value = this.validateSegmentRuleValue(rule.value, operator, field);

    return {
      field,
      operator: operator as any,
      value,
    };
  }

  /**
   * Validates segment rules array
   */
  static validateSegmentRules(rules: any[] | undefined): SegmentRule[] | undefined {
    if (!rules) return undefined;

    if (!Array.isArray(rules)) {
      throw new BadRequestException('Segment rules must be an array');
    }

    if (rules.length === 0) {
      throw new BadRequestException('Segment rules cannot be empty');
    }

    if (rules.length > SEGMENTS_CONSTANTS.VALIDATION.MAX_RULES_PER_SEGMENT) {
      throw new BadRequestException(`Maximum ${SEGMENTS_CONSTANTS.VALIDATION.MAX_RULES_PER_SEGMENT} rules allowed per segment`);
    }

    return rules.map((rule, index) => this.validateSegmentRule(rule, index));
  }

  /**
   * Validates user context from request
   */
  static validateUserContext(req: any): any {
    const user = req.user;
    if (!user || !user.id) {
      throw new BadRequestException('User context not found in request');
    }
    return user;
  }

  /**
   * Validates user profile and extracts workspace ID
   */
  static validateUserProfile(userProfile: any, userProfileError: any): number {
    if (userProfileError) {
      throw new BadRequestException('Failed to fetch user profile');
    }

    if (!userProfile) {
      throw new BadRequestException(SEGMENTS_CONSTANTS.ERROR_MESSAGES.USER_WORKSPACE_NOT_FOUND);
    }

    if (!userProfile.workspace_id) {
      throw new BadRequestException(SEGMENTS_CONSTANTS.ERROR_MESSAGES.USER_WORKSPACE_NOT_FOUND);
    }

    return userProfile.workspace_id;
  }

  /**
   * Validates segment exists and belongs to workspace
   */
  static validateSegmentExists(segment: any, segmentId: string): void {
    if (!segment) {
      throw new NotFoundException(SEGMENTS_CONSTANTS.ERROR_MESSAGES.SEGMENT_NOT_FOUND);
    }
  }

  /**
   * Validates pagination parameters
   */
  static validatePaginationParams(page?: number, limit?: number): { page: number; limit: number } {
    const pageNum = page && page > 0 ? page : SEGMENTS_CONSTANTS.DEFAULTS.PAGE;
    const limitNum = limit && limit > 0 ? Math.min(limit, SEGMENTS_CONSTANTS.DEFAULTS.MAX_LIMIT) : SEGMENTS_CONSTANTS.DEFAULTS.LIMIT;

    return { page: pageNum, limit: limitNum };
  }

  /**
   * Validates segment creation data
   */
  static validateSegmentCreationData(dto: any, userId: string, workspaceId: number): any {
    // Validate name
    this.validateSegmentName(dto.name);

    // Validate description
    const description = this.validateSegmentDescription(dto.description);

    // Validate rules
    const rules = this.validateSegmentRules(dto.rules);

    // Validate match type
    const match = this.validateMatchType(dto.match);

    return {
      name: dto.name.trim(),
      description,
      rules,
      match,
      createdBy: userId,
      workspaceId,
    };
  }

  /**
   * Validates segment update data
   */
  static validateSegmentUpdateData(dto: any): any {
    const updateData: any = {};

    if (dto.name !== undefined) {
      this.validateSegmentName(dto.name);
      updateData.name = dto.name.trim();
    }

    if (dto.description !== undefined) {
      updateData.description = this.validateSegmentDescription(dto.description);
    }

    if (dto.rules !== undefined) {
      updateData.rules = this.validateSegmentRules(dto.rules);
    }

    if (dto.match !== undefined) {
      updateData.match = this.validateMatchType(dto.match);
    }

    return updateData;
  }
}
