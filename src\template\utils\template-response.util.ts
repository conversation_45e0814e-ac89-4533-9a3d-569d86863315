/**
 * Template Response Utility
 * Standardized response handling for template operations
 */

import { TEMPLATE_CONSTANTS } from './template-constants.util';

export interface StandardTemplateResponse {
  status: 'success' | 'error';
  code: number;
  message: string;
  data?: any;
  timestamp: string;
}

export interface TemplateResponseData {
  template?: any;
  templates?: any[];
  pagination?: any;
  meta_template?: any;
  local_template?: any;
  sync_results?: any;
  errors?: any[];
  total_meta_templates?: number;
  new_templates_found?: number;
  successful_syncs?: number;
  failed_syncs?: number;
  campaign?: any;
  campaigns?: any[];
  campaign_stats?: any;
  contact_selection_info?: any;
  test_results?: any[];
  test_contacts?: any[];
  template_variables?: any;
  segment_conditions?: any;
  creation_data?: any;
  summary?: any;
  total?: number;
  templateId?: string;
  template_name?: string;
  meta_template_status?: string;
  deleted_from_meta?: boolean;
  paging?: any;
}

export class TemplateResponseUtil {
  /**
   * Creates a success response
   */
  static createSuccessResponse(
    data: TemplateResponseData,
    message: string,
    code: number = TEMPLATE_CONSTANTS.HTTP_STATUS.OK
  ): StandardTemplateResponse {
    return {
      status: 'success',
      code,
      message,
      data,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Creates an error response
   */
  static createErrorResponse(
    message: string,
    code: number = TEMPLATE_CONSTANTS.HTTP_STATUS.BAD_REQUEST,
    data?: any
  ): StandardTemplateResponse {
    return {
      status: 'error',
      code,
      message,
      data,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Creates template creation data
   */
  static createTemplateCreationData(template: any): TemplateResponseData {
    return {
      template: {
        id: template.id,
        name: template.name,
        description: template.description,
        language: template.language,
        components: template.components,
        meta_template_id: template.meta_template_id,
        meta_template_status: template.meta_template_status,
        meta_template_category: template.meta_template_category,
        workspace_id: template.workspace_id,
        created_by: template.created_by,
        is_active: template.is_active,
        ai_generated: template.ai_generated,
        waba_id: template.waba_id,
        created_at: template.created_at,
        updated_at: template.updated_at,
      },
    };
  }

  /**
   * Creates template list data with pagination
   */
  static createTemplateListData(templates: any[], pagination: any): TemplateResponseData {
    return {
      templates: templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        language: template.language,
        components: template.components,
        meta_template_id: template.meta_template_id,
        meta_template_status: template.meta_template_status,
        meta_template_category: template.meta_template_category,
        workspace_id: template.workspace_id,
        created_by: template.created_by,
        is_active: template.is_active,
        ai_generated: template.ai_generated,
        waba_id: template.waba_id,
        created_at: template.created_at,
        updated_at: template.updated_at,
      })),
      pagination,
    };
  }

  /**
   * Creates pagination metadata
   */
  static createPaginationMetadata(page: number, limit: number, total: number): any {
    return {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNextPage: page < Math.ceil(total / limit),
      hasPrevPage: page > 1,
    };
  }

  /**
   * Creates template sync data
   */
  static createTemplateSyncData(localTemplate: any, metaTemplate: any): TemplateResponseData {
    return {
      local_template: {
        id: localTemplate.id,
        name: localTemplate.name,
        meta_template_id: localTemplate.meta_template_id,
        meta_template_status: localTemplate.meta_template_status,
        updated_at: localTemplate.updated_at,
      },
      meta_template: {
        id: metaTemplate.id,
        name: metaTemplate.name,
        status: metaTemplate.status,
        category: metaTemplate.category,
      },
    };
  }

  /**
   * Creates bulk sync data
   */
  static createBulkSyncData(syncResults: any[], errors: any[], metaTemplates: any[]): TemplateResponseData {
    return {
      sync_results: syncResults,
      errors,
      total_meta_templates: metaTemplates.length,
      new_templates_found: syncResults.length + errors.length,
      successful_syncs: syncResults.length,
      failed_syncs: errors.length,
    };
  }

  /**
   * Creates campaign creation data
   */
  static createCampaignCreationData(campaign: any, totalContacts: number): TemplateResponseData {
    return {
      campaign: {
        id: campaign._id,
        name: campaign.name,
        description: campaign.description,
        template_id: campaign.template_id,
        contact_selection_type: campaign.contact_selection_type,
        total_contacts: campaign.total_contacts,
        pending_count: campaign.pending_count,
        status: campaign.status,
        send_type: campaign.send_type,
        scheduled_at: campaign.scheduled_at,
        workspace_id: campaign.workspace_id,
        created_by: campaign.created_by,
        created_at: campaign.createdAt,
        updated_at: campaign.updatedAt,
      },
      summary: {
        total_contacts: totalContacts,
        can_send_test: true,
      },
    };
  }

  /**
   * Creates campaign list data with pagination
   */
  static createCampaignListData(campaigns: any[], pagination: any): TemplateResponseData {
    return {
      campaigns: campaigns.map(campaign => ({
        id: campaign._id,
        name: campaign.name,
        description: campaign.description,
        template_id: campaign.template_id,
        contact_selection_type: campaign.contact_selection_type,
        total_contacts: campaign.total_contacts,
        pending_count: campaign.pending_count,
        sent_count: campaign.sent_count,
        delivered_count: campaign.delivered_count,
        read_count: campaign.read_count,
        failed_count: campaign.failed_count,
        status: campaign.status,
        send_type: campaign.send_type,
        scheduled_at: campaign.scheduled_at,
        started_at: campaign.started_at,
        completed_at: campaign.completed_at,
        workspace_id: campaign.workspace_id,
        created_by: campaign.created_by,
        created_at: campaign.createdAt,
        updated_at: campaign.updatedAt,
      })),
      pagination,
    };
  }

  /**
   * Creates campaign details data
   */
  static createCampaignDetailsData(campaign: any, contactSelectionInfo: any): TemplateResponseData {
    return {
      campaign: {
        id: campaign._id,
        name: campaign.name,
        description: campaign.description,
        template_id: campaign.template_id,
        contact_selection_type: campaign.contact_selection_type,
        contact_filters: campaign.contact_filters,
        csv_contacts_string: campaign.csv_contacts_string,
        csv_mapping: campaign.csv_mapping,
        quick_contacts: campaign.quick_contacts,
        total_contacts: campaign.total_contacts,
        pending_count: campaign.pending_count,
        sent_count: campaign.sent_count,
        delivered_count: campaign.delivered_count,
        read_count: campaign.read_count,
        failed_count: campaign.failed_count,
        status: campaign.status,
        send_type: campaign.send_type,
        scheduled_at: campaign.scheduled_at,
        started_at: campaign.started_at,
        completed_at: campaign.completed_at,
        paused_at: campaign.paused_at,
        resumed_at: campaign.resumed_at,
        workspace_id: campaign.workspace_id,
        created_by: campaign.created_by,
        created_at: campaign.createdAt,
        updated_at: campaign.updatedAt,
      },
      contact_selection_info: contactSelectionInfo,
    };
  }

  /**
   * Creates campaign stats data
   */
  static createCampaignStatsData(campaign: any, messageLogs: any[]): TemplateResponseData {
    return {
      campaign_stats: {
        campaign_id: campaign._id,
        name: campaign.name,
        status: campaign.status,
        total_contacts: campaign.total_contacts,
        delivery_stats: campaign.delivery_stats,
        progress_percentage: campaign.total_contacts > 0 
          ? ((campaign.sent_count + campaign.failed_count) / campaign.total_contacts) * 100 
          : 0,
        message_logs: messageLogs.length > 0 ? messageLogs : undefined,
        created_at: campaign.createdAt,
        started_at: campaign.started_at,
        completed_at: campaign.completed_at,
      },
    };
  }

  /**
   * Creates test message results data
   */
  static createTestMessageResultsData(results: any[], testContacts: string[]): TemplateResponseData {
    return {
      test_results: results,
      test_contacts: testContacts,
    };
  }

  /**
   * Creates template variables data
   */
  static createTemplateVariablesData(template: any, variables: string[]): TemplateResponseData {
    return {
      template_variables: {
        template: {
          id: template.id,
          name: template.name,
          components: template.components,
          language: template.language,
        },
        variables,
        variableMapping: {
          header: {},
          body: {},
          buttons: {},
          footer: {},
        },
      },
    };
  }

  /**
   * Creates segment conditions data
   */
  static createSegmentConditionsData(segment: any, contactFilters: any, rules: any[]): TemplateResponseData {
    return {
      segment_conditions: {
        segment: {
          id: segment.id,
          name: segment.name,
          description: segment.description,
          rules: segment.rules,
        },
        contactFilters,
        rules,
      },
    };
  }

  /**
   * Creates campaign creation data response
   */
  static createCampaignCreationDataResponse(segments: any[], templates: any[], contactFields: any[], operators: any[]): TemplateResponseData {
    return {
      creation_data: {
        segments,
        templates,
        contactFields,
        operators,
      },
    };
  }

  /**
   * Creates duplicate error response
   */
  static createDuplicateErrorResponse(message: string, existingTemplate?: any): StandardTemplateResponse {
    return {
      status: 'error',
      code: TEMPLATE_CONSTANTS.HTTP_STATUS.CONFLICT,
      message,
      data: existingTemplate ? {
        existing_template: {
          id: existingTemplate.id,
          name: existingTemplate.name,
          language: existingTemplate.language,
          waba_id: existingTemplate.waba_id,
        },
      } : undefined,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Creates meta API error response
   */
  static createMetaApiErrorResponse(message: string, metaError?: any): StandardTemplateResponse {
    return {
      status: 'error',
      code: TEMPLATE_CONSTANTS.HTTP_STATUS.BAD_REQUEST,
      message,
      data: metaError ? {
        meta_error: metaError,
      } : undefined,
      timestamp: new Date().toISOString(),
    };
  }
}
