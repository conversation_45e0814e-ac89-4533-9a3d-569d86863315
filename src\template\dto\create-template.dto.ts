import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, IsArray, IsObject, IsEnum, IsNotEmpty, MaxLength, ValidateNested, IsBoolean, IsNumber, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';
import { TEMPLATE_CONSTANTS } from '../utils/template-constants.util';

export class ButtonDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(25)
  text: string;

  @IsEnum(Object.values(TEMPLATE_CONSTANTS.BUTTON_TYPES))
  type: string;

  @IsOptional()
  @IsString()
  url?: string;

  @IsOptional()
  @IsString()
  phone_number?: string;
}

export class MetaTemplateButtonDto {
  @IsEnum(['QUICK_REPLY', 'URL', 'PHONE_NUMBER', 'COPY_CODE', 'OTP', 'CATALOG', 'MPM'])
  type: 'QUICK_REPLY' | 'URL' | 'PHONE_NUMBER' | 'COPY_CODE' | 'OTP' | 'CATALOG' | 'MPM';

  @IsString()
  @IsNotEmpty()
  text: string;

  @IsOptional()
  @IsString()
  url?: string;

  @IsOptional()
  @IsString()
  phone_number?: string;

  @IsOptional()
  @IsEnum(['COPY_CODE', 'ONE_TAP'])
  otp_type?: 'COPY_CODE' | 'ONE_TAP';

  @IsOptional()
  @IsString()
  autofill_text?: string;

  @IsOptional()
  @IsString()
  package_name?: string;

  @IsOptional()
  @IsString()
  signature_hash?: string;
}

export class MetaTemplateExampleDto {
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  header_text?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  header_handle?: string[];

  @IsOptional()
  @IsArray()
  body_text?: string[][];
}

export class MetaTemplateComponentDto {
  @IsEnum(['HEADER', 'BODY', 'FOOTER', 'BUTTONS'])
  type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';

  @IsOptional()
  @IsEnum(['TEXT', 'IMAGE', 'VIDEO', 'DOCUMENT', 'LOCATION'])
  format?: 'TEXT' | 'IMAGE' | 'VIDEO' | 'DOCUMENT' | 'LOCATION';

  @IsOptional()
  @IsString()
  @MaxLength(1024)
  text?: string;

  @IsOptional()
  @IsBoolean()
  add_security_recommendation?: boolean;

  @IsOptional()
  @IsNumber()
  code_expiration_minutes?: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => MetaTemplateExampleDto)
  example?: MetaTemplateExampleDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MetaTemplateButtonDto)
  buttons?: MetaTemplateButtonDto[];
}

export class SectionDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsEnum(Object.values(TEMPLATE_CONSTANTS.SECTION_TYPES))
  type: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  products?: any[];
}

export class CreateTemplateDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(TEMPLATE_CONSTANTS.VALIDATION.NAME_MAX_LENGTH)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(TEMPLATE_CONSTANTS.VALIDATION.DESCRIPTION_MAX_LENGTH)
  description?: string;

  @IsEnum(Object.values(TEMPLATE_CONSTANTS.TEMPLATE_CATEGORIES))
  category: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(TEMPLATE_CONSTANTS.VALIDATION.LANGUAGE_MAX_LENGTH)
  language: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MetaTemplateComponentDto)
  @ArrayMinSize(1, { message: 'At least one component is required' })
  @ArrayMaxSize(4, { message: 'Maximum 4 components allowed' })
  components: MetaTemplateComponentDto[];

  @IsString()
  @IsNotEmpty()
  @MaxLength(TEMPLATE_CONSTANTS.VALIDATION.WABA_ID_MAX_LENGTH)
  waba_id: string;
}
