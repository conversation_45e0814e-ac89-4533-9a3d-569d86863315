import { Injectable, OnModuleInit, OnModule<PERSON><PERSON>roy, <PERSON><PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';

@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DatabaseService.name);
  private isConnected = false;

  constructor(
    private configService: ConfigService,
    @InjectConnection() private readonly connection: Connection,
  ) {
    // Listen for connection events
    this.connection.on('connected', () => {
      this.isConnected = true;
      this.logger.log('✅ MongoDB connected successfully');
    });

    this.connection.on('disconnected', () => {
      this.isConnected = false;
      this.logger.error('❌ MongoDB disconnected');
    });

    this.connection.on('error', (error) => {
      this.isConnected = false;
      this.logger.error('❌ MongoDB connection error:', error);
    });
  }

  async onModuleInit() {
    try {
      const mongoUri = this.configService.get<string>('MONGODB_URI');
      const dbName = this.configService.get<string>('MONGODB_DATABASE_NAME');
      
      this.logger.log(`🔌 Attempting to connect to MongoDB...`);
      this.logger.log(`📡 URI: ${mongoUri}`);
      this.logger.log(`🗄️  Database: ${dbName}`);
      
      // Test the connection
      if (this.connection.db) {
        await this.connection.db.admin().ping();
        this.logger.log('✅ MongoDB connection test successful');
      }
      
    } catch (error) {
      this.logger.error('❌ Failed to connect to MongoDB:', error.message);
      throw error;
    }
  }

  async onModuleDestroy() {
    this.logger.log('🔌 Disconnecting from MongoDB...');
    await this.connection.close();
  }

  // Health check method
  async isDatabaseConnected(): Promise<boolean> {
    try {
      if (this.connection.db) {
        await this.connection.db.admin().ping();
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  // Get connection status
  getConnectionStatus(): { connected: boolean; readyState: string } {
    const readyState = this.connection.readyState;
    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting',
    };
    
    return {
      connected: this.isConnected,
      readyState: states[readyState] || 'unknown',
    };
  }

  // Get database info
  async getDatabaseInfo() {
    try {
      const db = this.connection.db;
      if (!db) {
        return null;
      }
      
      const adminDb = db.admin();
      
      const [serverInfo, dbStats] = await Promise.all([
        adminDb.serverInfo(),
        db.stats(),
      ]);

      return {
        server: serverInfo.version,
        database: db.databaseName,
        collections: dbStats.collections,
        documents: dbStats.objects,
        dataSize: dbStats.dataSize,
        storageSize: dbStats.storageSize,
      };
    } catch (error) {
      this.logger.error('Failed to get database info:', error.message);
      return null;
    }
  }
} 